"use client";

import React, { useState } from 'react';
import { 
  Shield, 
  Key, 
  Eye, 
  EyeOff, 
  CheckCircle, 
  AlertTriangle,
  Clock,
  Monitor,
  Smartphone,
  MapPin,
  Calendar
} from 'lucide-react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { useAuth } from '../../hooks/useAuth';
import { supabase } from '../../lib/supabase';
import { securitySettingsSchema } from '../../lib/validations';
import { PasswordInput, calculatePasswordStrength } from '../ui/PasswordStrength';
import Card from '../ui/Card';
import Button from '../ui/Button';
import Input, { FormGroup } from '../ui/Input';
import { cn } from '../../lib/utils';

export default function SecurityPage() {
  const { user } = useAuth();
  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [passwordData, setPasswordData] = useState({
    current_password: '',
    new_password: '',
    confirm_password: ''
  });
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  });
  const [errors, setErrors] = useState({});
  const [success, setSuccess] = useState('');

  // Fetch security logs
  const { data: securityLogs = [], isLoading: logsLoading } = useQuery({
    queryKey: ['security-logs', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      
      const { data, error } = await supabase
        .from('security_logs')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(10);
      
      if (error) {
        console.log('Security logs not available:', error);
        return [];
      }
      return data || [];
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000,
  });

  // Change password mutation
  const changePassword = useMutation({
    mutationFn: async (passwordData) => {
      // Validate the form data
      const validatedData = securitySettingsSchema.parse(passwordData);
      
      // Update password using Supabase Auth
      const { error } = await supabase.auth.updateUser({
        password: validatedData.new_password
      });
      
      if (error) throw error;
      
      // Log the security event
      try {
        await supabase.from('security_logs').insert([{
          user_id: user.id,
          action: 'password_change',
          success: true,
          details: { method: 'profile_settings' }
        }]);
      } catch (logError) {
        console.log('Failed to log security event:', logError);
      }
      
      return true;
    },
    onSuccess: () => {
      setSuccess('Password changed successfully!');
      setPasswordData({
        current_password: '',
        new_password: '',
        confirm_password: ''
      });
      setShowPasswordForm(false);
      setErrors({});
      
      // Clear success message after 5 seconds
      setTimeout(() => setSuccess(''), 5000);
    },
    onError: (error) => {
      if (error.errors) {
        const newErrors = {};
        error.errors.forEach(err => {
          newErrors[err.path[0]] = err.message;
        });
        setErrors(newErrors);
      } else {
        setErrors({ general: error.message || 'Failed to change password' });
      }
    }
  });

  const handlePasswordSubmit = async (e) => {
    e.preventDefault();
    setErrors({});
    setSuccess('');
    
    try {
      await changePassword.mutateAsync(passwordData);
    } catch (error) {
      // Error handling is done in the mutation
    }
  };

  const getDeviceIcon = (userAgent) => {
    if (!userAgent) return Monitor;
    if (userAgent.includes('Mobile') || userAgent.includes('Android') || userAgent.includes('iPhone')) {
      return Smartphone;
    }
    return Monitor;
  };

  const formatUserAgent = (userAgent) => {
    if (!userAgent) return 'Unknown Device';
    
    // Extract browser and OS info
    const browserMatch = userAgent.match(/(Chrome|Firefox|Safari|Edge)\/[\d.]+/);
    const osMatch = userAgent.match(/(Windows|Mac|Linux|Android|iOS)/);
    
    const browser = browserMatch ? browserMatch[1] : 'Unknown Browser';
    const os = osMatch ? osMatch[1] : 'Unknown OS';
    
    return `${browser} on ${os}`;
  };

  const passwordStrength = calculatePasswordStrength(passwordData.new_password);

  return (
    <div className="space-y-6">
      {/* Success Message */}
      {success && (
        <Card className="p-4 bg-green-50 border-green-200">
          <div className="flex items-center gap-3">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <span className="text-green-800">{success}</span>
          </div>
        </Card>
      )}

      {/* Password Security */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-50 rounded-lg">
              <Key className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Password</h3>
              <p className="text-sm text-gray-600">Keep your account secure with a strong password</p>
            </div>
          </div>
          <Button
            variant="outline"
            onClick={() => setShowPasswordForm(!showPasswordForm)}
          >
            Change Password
          </Button>
        </div>

        {showPasswordForm && (
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <form onSubmit={handlePasswordSubmit} className="space-y-4">
              {errors.general && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-sm text-red-600">{errors.general}</p>
                </div>
              )}

              <FormGroup label="Current Password" error={errors.current_password}>
                <div className="relative">
                  <Input
                    type={showPasswords.current ? "text" : "password"}
                    value={passwordData.current_password}
                    onChange={(e) => setPasswordData(prev => ({ 
                      ...prev, 
                      current_password: e.target.value 
                    }))}
                    error={!!errors.current_password}
                    placeholder="Enter your current password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPasswords(prev => ({ 
                      ...prev, 
                      current: !prev.current 
                    }))}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPasswords.current ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
              </FormGroup>

              <FormGroup label="New Password" error={errors.new_password}>
                <PasswordInput
                  value={passwordData.new_password}
                  onChange={(e) => setPasswordData(prev => ({ 
                    ...prev, 
                    new_password: e.target.value 
                  }))}
                  error={!!errors.new_password}
                  placeholder="Enter your new password"
                  showStrength={true}
                />
              </FormGroup>

              <FormGroup label="Confirm New Password" error={errors.confirm_password}>
                <div className="relative">
                  <Input
                    type={showPasswords.confirm ? "text" : "password"}
                    value={passwordData.confirm_password}
                    onChange={(e) => setPasswordData(prev => ({ 
                      ...prev, 
                      confirm_password: e.target.value 
                    }))}
                    error={!!errors.confirm_password}
                    placeholder="Confirm your new password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPasswords(prev => ({ 
                      ...prev, 
                      confirm: !prev.confirm 
                    }))}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPasswords.confirm ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
                
                {passwordData.confirm_password && passwordData.new_password !== passwordData.confirm_password && (
                  <p className="text-xs text-red-600 mt-1">Passwords do not match</p>
                )}
                {passwordData.confirm_password && passwordData.new_password === passwordData.confirm_password && passwordData.confirm_password.length > 0 && (
                  <p className="text-xs text-green-600 mt-1 flex items-center gap-1">
                    <CheckCircle className="w-3 h-3" />
                    Passwords match
                  </p>
                )}
              </FormGroup>

              <div className="flex gap-3 pt-4">
                <Button
                  type="submit"
                  disabled={
                    changePassword.isPending ||
                    !passwordData.current_password ||
                    !passwordData.new_password ||
                    !passwordData.confirm_password ||
                    passwordData.new_password !== passwordData.confirm_password ||
                    passwordStrength.score < 3
                  }
                >
                  {changePassword.isPending ? 'Changing...' : 'Change Password'}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setShowPasswordForm(false);
                    setPasswordData({
                      current_password: '',
                      new_password: '',
                      confirm_password: ''
                    });
                    setErrors({});
                  }}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </div>
        )}
      </Card>

      {/* Account Security Status */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 bg-green-50 rounded-lg">
            <Shield className="w-5 h-5 text-green-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">Account Security</h3>
            <p className="text-sm text-gray-600">Your account security status</p>
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
            <div className="flex items-center gap-3">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <span className="text-sm font-medium text-green-800">Email Verified</span>
            </div>
            <span className="text-xs text-green-600">Active</span>
          </div>

          <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
            <div className="flex items-center gap-3">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <span className="text-sm font-medium text-green-800">Strong Password</span>
            </div>
            <span className="text-xs text-green-600">Secure</span>
          </div>

          <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
            <div className="flex items-center gap-3">
              <AlertTriangle className="w-5 h-5 text-yellow-600" />
              <span className="text-sm font-medium text-yellow-800">Two-Factor Authentication</span>
            </div>
            <span className="text-xs text-yellow-600">Not Enabled</span>
          </div>
        </div>
      </Card>

      {/* Recent Security Activity */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 bg-purple-50 rounded-lg">
            <Clock className="w-5 h-5 text-purple-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">Recent Activity</h3>
            <p className="text-sm text-gray-600">Recent security events on your account</p>
          </div>
        </div>

        {logsLoading ? (
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="animate-pulse">
                <div className="h-16 bg-gray-200 rounded-lg"></div>
              </div>
            ))}
          </div>
        ) : securityLogs.length === 0 ? (
          <div className="text-center py-8">
            <Clock className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <p className="text-gray-600">No recent security activity</p>
          </div>
        ) : (
          <div className="space-y-3">
            {securityLogs.map((log) => {
              const DeviceIcon = getDeviceIcon(log.user_agent);
              
              return (
                <div key={log.id} className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
                  <div className={cn(
                    'p-2 rounded-lg',
                    log.success ? 'bg-green-100' : 'bg-red-100'
                  )}>
                    <DeviceIcon className={cn(
                      'w-4 h-4',
                      log.success ? 'text-green-600' : 'text-red-600'
                    )} />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-gray-900 capitalize">
                        {log.action.replace('_', ' ')}
                      </span>
                      {log.success ? (
                        <CheckCircle className="w-4 h-4 text-green-600" />
                      ) : (
                        <AlertTriangle className="w-4 h-4 text-red-600" />
                      )}
                    </div>
                    <div className="text-sm text-gray-600">
                      {formatUserAgent(log.user_agent)}
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="text-sm text-gray-900">
                      {new Date(log.created_at).toLocaleDateString()}
                    </div>
                    <div className="text-xs text-gray-500">
                      {new Date(log.created_at).toLocaleTimeString()}
                    </div>
                    {log.ip_address && (
                      <div className="text-xs text-gray-500 flex items-center gap-1">
                        <MapPin className="w-3 h-3" />
                        {log.ip_address}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </Card>

      {/* Security Recommendations */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 bg-blue-50 rounded-lg">
            <Shield className="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">Security Recommendations</h3>
            <p className="text-sm text-gray-600">Tips to keep your account secure</p>
          </div>
        </div>

        <div className="space-y-3 text-sm text-gray-600">
          <div className="flex items-start gap-3">
            <CheckCircle className="w-4 h-4 text-green-600 mt-0.5" />
            <span>Use a strong, unique password for your account</span>
          </div>
          <div className="flex items-start gap-3">
            <AlertTriangle className="w-4 h-4 text-yellow-600 mt-0.5" />
            <span>Enable two-factor authentication for extra security (coming soon)</span>
          </div>
          <div className="flex items-start gap-3">
            <CheckCircle className="w-4 h-4 text-green-600 mt-0.5" />
            <span>Keep your email address up to date</span>
          </div>
          <div className="flex items-start gap-3">
            <CheckCircle className="w-4 h-4 text-green-600 mt-0.5" />
            <span>Review your account activity regularly</span>
          </div>
        </div>
      </Card>
    </div>
  );
}
