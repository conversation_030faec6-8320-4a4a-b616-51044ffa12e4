import { supabase } from '../../../lib/supabase';
import { v4 as uuidv4 } from 'uuid';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

export const useSupabase = () => {
  const queryClient = useQueryClient();
  const PAGE_SIZE = 10;

  // Products Queries & Mutations
  const useProducts = (page = 0, filterType = 'all') => useQuery({
    queryKey: ['products', page, filterType],
    queryFn: async () => {
      let query = supabase
        .from('products')
        .select('*')
        .range(page * PAGE_SIZE, (page + 1) * PAGE_SIZE - 1);
      
      if (filterType === 'promoted') {
        query = query.eq('is_promoted', true);
      } else if (filterType === 'regular') {
        query = query.eq('is_promoted', false);
      }
      
      const { data, error } = await query;
      if (error) throw error;
      return data;
    }
  });
  
  const useTotalProducts = (filterType = 'all') => useQuery({
    queryKey: ['totalProducts', filterType],
    queryFn: async () => {
      let query = supabase.from('products').select('id', { count: 'exact' });
      
      if (filterType === 'promoted') {
        query = query.eq('is_promoted', true);
      } else if (filterType === 'regular') {
        query = query.eq('is_promoted', false);
      }
      
      const { count } = await query;
      return count;
    }
  });
    

  const useAddProduct = () => useMutation({
    mutationFn: async (product) => {
      const { data, error } = await supabase
        .from('products')
        .insert([{
          ...product,
          wishlist_count: 0
        }])
        .select();
      if (error) throw error;
      return data;
    },
    onSuccess: () => queryClient.invalidateQueries(['products'])
  });

  const useUpdateProduct = () => useMutation({
    mutationFn: async ({ id, product }) => {
      const { data, error } = await supabase
        .from('products')
        .update(product)
        .eq('id', id)
        .select();
      if (error) throw error;
      return data;
    },
    onSuccess: () => queryClient.invalidateQueries(['products'])
  });

  const useDeleteProduct = () => useMutation({
    mutationFn: async (id) => {
      const { data, error } = await supabase
        .from('products')
        .delete()
        .eq('id', id);
      if (error) throw error;
      return data;
    },
    onSuccess: () => queryClient.invalidateQueries(['products'])
  });

  // Categories Queries & Mutations
  const useCategories = () => useQuery({
    queryKey: ['categories'],
    queryFn: async () => {
      const { data, error } = await supabase.from('categories').select('*');
      if (error) throw error;
      return data;
    }
  });

  const useAddCategory = () => useMutation({
    mutationFn: async (categoryData) => {
      const { data, error } = await supabase
        .from('categories')
        .insert([categoryData])
        .select();
      if (error) throw error;
      return data;
    },
    onSuccess: () => queryClient.invalidateQueries(['categories'])
  });

  const useUpdateCategory = () => useMutation({
    mutationFn: async ({ id, categoryData }) => {
      const { data, error } = await supabase
        .from('categories')
        .update(categoryData)
        .eq('id', id)
        .select();
      if (error) throw error;
      return data;
    },
    onSuccess: () => queryClient.invalidateQueries(['categories'])
  });

  const useDeleteCategory = () => useMutation({
    mutationFn: async (id) => {
      const { data, error } = await supabase
        .from('categories')
        .delete()
        .eq('id', id);
      if (error) throw error;
      return data;
    },
    onSuccess: () => queryClient.invalidateQueries(['categories'])
  });

  // Orders Queries & Mutations
  const useOrders = (page = 0) => useQuery({
    queryKey: ['orders', page],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('orders')
        .select('*')
        .order('created_at', { ascending: false })
        .range(page * PAGE_SIZE, (page + 1) * PAGE_SIZE - 1);
      if (error) throw error;
      return data;
    }
  });

  const useUpdateOrderStatus = () => useMutation({
    mutationFn: async ({ orderId, status }) => {
      const { data, error } = await supabase
        .from('orders')
        .update({ status })
        .eq('id', orderId)
        .select();
      if (error) throw error;
      return data;
    },
    onSuccess: () => queryClient.invalidateQueries(['orders'])
  });

  // Dashboard Data Query
  const useDashboardData = () => useQuery({
    queryKey: ['dashboard'],
    queryFn: async () => {
      const [salesData, recentOrders, topProducts] = await Promise.all([
        supabase.rpc('get_sales_data'),
        supabase.from('orders')
          .select('*, billing_details')
          .order('created_at', { ascending: false })
          .limit(5),
        supabase.from('products')
          .select('*')
          .order('wishlist_count', { ascending: false })
          .limit(5)
      ]);
  
      // Calculate totals
      const totals = {
        confirmed_sales: salesData.data.reduce((sum, day) => sum + (day.confirmed_sales || 0), 0),
        pending_sales: salesData.data.reduce((sum, day) => sum + (day.pending_sales || 0), 0),
        total_orders: salesData.data.reduce((sum, day) => sum + (day.total_orders || 0), 0)
      };
      
      return {
        salesData: salesData.data,
        recentOrders: recentOrders.data,
        topProducts: topProducts.data,
        totals
      };
    },
    staleTime: 1000 * 60 * 5
  });
  
  
  // Image Upload Utility
  const uploadProductImage = async (imageFile) => {
    const fileName = `${uuidv4()}-${imageFile.name}`;
    const { data, error } = await supabase.storage
      .from('product_images')
      .upload(fileName, imageFile);
    if (error) throw error;
    
    const { data: { publicUrl } } = supabase.storage
      .from('product_images')
      .getPublicUrl(fileName);
    
    return publicUrl;
  };
  const fetchMarketingBanners = async () => {
    const { data, error } = await supabase
      .from('marketing_banners')
      .select('*')
      .order('display_order', { ascending: true });
    if (error) throw error;
    return data;
  };
  
  const createMarketingBanner = async (bannerData) => {
    const { data, error } = await supabase
      .from('marketing_banners')
      .insert([{
        title: bannerData.title,
        image_url: bannerData.image_url,
        link: bannerData.link,
        display_order: bannerData.display_order,
        is_active: bannerData.is_active
      }])
      .select();
    if (error) throw error;
    return data;
  };
  
  const updateMarketingBanner = async (id, bannerData) => {
    const { data, error } = await supabase
      .from('marketing_banners')
      .update({
        title: bannerData.title,
        image_url: bannerData.image_url,
        link: bannerData.link,
        display_order: bannerData.display_order,
        is_active: bannerData.is_active
      })
      .eq('id', id)
      .select();
    if (error) throw error;
    return data;
  };
  
  const deleteMarketingBanner = async (id) => {
    const { error } = await supabase
      .from('marketing_banners')
      .delete()
      .eq('id', id);
    if (error) throw error;
  };
  return {
    useProducts,
    useTotalProducts,
    useAddProduct,
    useUpdateProduct,
    useDeleteProduct,
    useCategories,
    useAddCategory,
    useUpdateCategory,
    useDeleteCategory,
    useOrders,
    useUpdateOrderStatus,
    useDashboardData,
    uploadProductImage,
    fetchMarketingBanners,
    createMarketingBanner,
    updateMarketingBanner,
    deleteMarketingBanner
  };
};
