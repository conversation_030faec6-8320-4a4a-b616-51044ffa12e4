"use client";

import React, { useState } from 'react';
import { 
  Bell, 
  Mail, 
  Smartphone, 
  Package, 
  Tag, 
  Settings, 
  Check, 
  X,
  Clock,
  CheckCircle,
  AlertCircle,
  Info,
  Gift
} from 'lucide-react';
import { 
  useUserNotifications, 
  useMarkNotificationRead,
  useUserSettings,
  useUpdateUserSettings
} from '../../hooks/useProfile';
import Card from '../ui/Card';
import Button from '../ui/Button';
import { cn } from '../../lib/utils';

const NOTIFICATION_TYPES = {
  order_update: {
    label: 'Order Updates',
    description: 'Get notified about order status changes',
    icon: Package,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50'
  },
  promotion: {
    label: 'Promotions & Deals',
    description: 'Receive notifications about sales and special offers',
    icon: Tag,
    color: 'text-green-600',
    bgColor: 'bg-green-50'
  },
  system: {
    label: 'System Updates',
    description: 'Important account and system notifications',
    icon: Settings,
    color: 'text-purple-600',
    bgColor: 'bg-purple-50'
  },
  newsletter: {
    label: 'Newsletter',
    description: 'Weekly newsletter with new products and tips',
    icon: Mail,
    color: 'text-orange-600',
    bgColor: 'bg-orange-50'
  }
};

export default function NotificationsPage() {
  const [activeTab, setActiveTab] = useState('notifications');
  
  const { data: notifications = [], isLoading: notificationsLoading } = useUserNotifications();
  const { data: settings = {}, isLoading: settingsLoading } = useUserSettings();
  const markAsRead = useMarkNotificationRead();
  const updateSettings = useUpdateUserSettings();

  const notificationPreferences = settings.notification_preferences || {
    order_updates: true,
    promotions: true,
    newsletter: false,
    sms_notifications: true,
    email_notifications: true
  };

  const handlePreferenceChange = async (key, value) => {
    try {
      const newPreferences = {
        ...notificationPreferences,
        [key]: value
      };
      
      await updateSettings.mutateAsync({
        key: 'notification_preferences',
        value: newPreferences
      });
    } catch (error) {
      console.error('Failed to update notification preferences:', error);
    }
  };

  const handleMarkAsRead = async (notificationId) => {
    try {
      await markAsRead.mutateAsync(notificationId);
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  const getNotificationIcon = (type) => {
    const config = NOTIFICATION_TYPES[type];
    if (config) {
      const Icon = config.icon;
      return <Icon className={cn('w-5 h-5', config.color)} />;
    }
    return <Bell className="w-5 h-5 text-gray-600" />;
  };

  const getNotificationBg = (type) => {
    const config = NOTIFICATION_TYPES[type];
    return config ? config.bgColor : 'bg-gray-50';
  };

  const formatNotificationTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now - date) / (1000 * 60));
      return `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const unreadCount = notifications.filter(n => !n.is_read).length;

  return (
    <div className="space-y-6">
      {/* Header with Tabs */}
      <div className="border-b border-gray-200">
        <div className="flex space-x-8">
          <button
            onClick={() => setActiveTab('notifications')}
            className={cn(
              'py-2 px-1 border-b-2 font-medium text-sm',
              activeTab === 'notifications'
                ? 'border-primarycolor text-primarycolor'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            )}
          >
            <div className="flex items-center gap-2">
              <Bell className="w-4 h-4" />
              Notifications
              {unreadCount > 0 && (
                <span className="bg-red-500 text-white text-xs rounded-full px-2 py-0.5">
                  {unreadCount}
                </span>
              )}
            </div>
          </button>
          <button
            onClick={() => setActiveTab('preferences')}
            className={cn(
              'py-2 px-1 border-b-2 font-medium text-sm',
              activeTab === 'preferences'
                ? 'border-primarycolor text-primarycolor'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            )}
          >
            <div className="flex items-center gap-2">
              <Settings className="w-4 h-4" />
              Preferences
            </div>
          </button>
        </div>
      </div>

      {/* Notifications Tab */}
      {activeTab === 'notifications' && (
        <div className="space-y-4">
          {notificationsLoading ? (
            <div className="space-y-4">
              {[1, 2, 3, 4].map(i => (
                <div key={i} className="animate-pulse">
                  <div className="h-20 bg-gray-200 rounded-lg"></div>
                </div>
              ))}
            </div>
          ) : notifications.length === 0 ? (
            <Card className="p-8 text-center">
              <Bell className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Notifications</h3>
              <p className="text-gray-600">
                You&apos;re all caught up! New notifications will appear here.
              </p>
            </Card>
          ) : (
            <div className="space-y-3">
              {notifications.map((notification) => (
                <Card 
                  key={notification.id} 
                  className={cn(
                    'p-4 transition-colors',
                    !notification.is_read ? 'bg-blue-50 border-blue-200' : 'hover:bg-gray-50'
                  )}
                >
                  <div className="flex items-start gap-4">
                    <div className={cn(
                      'p-2 rounded-lg flex-shrink-0',
                      getNotificationBg(notification.type)
                    )}>
                      {getNotificationIcon(notification.type)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between gap-2">
                        <div>
                          <h4 className={cn(
                            'font-medium text-gray-900',
                            !notification.is_read && 'font-semibold'
                          )}>
                            {notification.title}
                          </h4>
                          <p className="text-sm text-gray-600 mt-1">
                            {notification.message}
                          </p>
                          <div className="flex items-center gap-2 mt-2 text-xs text-gray-500">
                            <Clock className="w-3 h-3" />
                            {formatNotificationTime(notification.created_at)}
                          </div>
                        </div>
                        
                        {!notification.is_read && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleMarkAsRead(notification.id)}
                            disabled={markAsRead.isPending}
                          >
                            <Check className="w-4 h-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Preferences Tab */}
      {activeTab === 'preferences' && (
        <div className="space-y-6">
          {/* Notification Types */}
          <Card className="p-6">
            <h3 className="font-semibold text-gray-900 mb-4">Notification Types</h3>
            <div className="space-y-4">
              {Object.entries(NOTIFICATION_TYPES).map(([key, config]) => {
                const Icon = config.icon;
                const isEnabled = notificationPreferences[key] !== false;
                
                return (
                  <div key={key} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className={cn('p-2 rounded-lg', config.bgColor)}>
                        <Icon className={cn('w-5 h-5', config.color)} />
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">{config.label}</h4>
                        <p className="text-sm text-gray-600">{config.description}</p>
                      </div>
                    </div>
                    
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={isEnabled}
                        onChange={(e) => handlePreferenceChange(key, e.target.checked)}
                        disabled={updateSettings.isPending}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primarycolor-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primarycolor"></div>
                    </label>
                  </div>
                );
              })}
            </div>
          </Card>

          {/* Delivery Methods */}
          <Card className="p-6">
            <h3 className="font-semibold text-gray-900 mb-4">Delivery Methods</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-50 rounded-lg">
                    <Mail className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Email Notifications</h4>
                    <p className="text-sm text-gray-600">Receive notifications via email</p>
                  </div>
                </div>
                
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={notificationPreferences.email_notifications !== false}
                    onChange={(e) => handlePreferenceChange('email_notifications', e.target.checked)}
                    disabled={updateSettings.isPending}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primarycolor-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primarycolor"></div>
                </label>
              </div>

              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-50 rounded-lg">
                    <Smartphone className="w-5 h-5 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">SMS Notifications</h4>
                    <p className="text-sm text-gray-600">Receive notifications via SMS</p>
                  </div>
                </div>
                
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={notificationPreferences.sms_notifications !== false}
                    onChange={(e) => handlePreferenceChange('sms_notifications', e.target.checked)}
                    disabled={updateSettings.isPending}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primarycolor-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primarycolor"></div>
                </label>
              </div>
            </div>
          </Card>

          {/* Quiet Hours */}
          <Card className="p-6">
            <h3 className="font-semibold text-gray-900 mb-4">Quiet Hours</h3>
            <p className="text-sm text-gray-600 mb-4">
              Set times when you don&apos;t want to receive notifications (coming soon)
            </p>
            <div className="bg-gray-100 p-4 rounded-lg">
              <p className="text-sm text-gray-500 text-center">
                This feature will be available in a future update
              </p>
            </div>
          </Card>

          {settingsLoading && (
            <div className="text-center py-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primarycolor mx-auto"></div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
