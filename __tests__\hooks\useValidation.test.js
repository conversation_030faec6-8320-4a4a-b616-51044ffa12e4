import { renderHook, act } from '@testing-library/react'
import { z } from 'zod'
import { useValidation, useFormSubmission } from '../../app/hooks/useValidation'

// Mock schema for testing
const testSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email format'),
  age: z.number().min(18, 'Must be at least 18 years old'),
})

describe('useValidation', () => {
  it('initializes with default values', () => {
    const { result } = renderHook(() => useValidation(testSchema))
    
    expect(result.current.data).toEqual({})
    expect(result.current.errors).toBeNull()
    expect(result.current.isValid).toBe(false)
    expect(result.current.touched).toEqual({})
  })

  it('initializes with provided initial data', () => {
    const initialData = { name: '<PERSON>', email: '<EMAIL>', age: 25 }
    const { result } = renderHook(() => useValidation(testSchema, initialData))
    
    expect(result.current.data).toEqual(initialData)
  })

  it('validates data correctly', () => {
    const { result } = renderHook(() => useValidation(testSchema))
    
    const validData = { name: 'John Doe', email: '<EMAIL>', age: 25 }
    
    act(() => {
      const validationResult = result.current.validate(validData)
      expect(validationResult.success).toBe(true)
      expect(validationResult.data).toEqual(validData)
    })
    
    expect(result.current.isValid).toBe(true)
    expect(result.current.errors).toBeNull()
  })

  it('handles validation errors', () => {
    const { result } = renderHook(() => useValidation(testSchema))
    
    const invalidData = { name: 'J', email: 'invalid-email', age: 16 }
    
    act(() => {
      const validationResult = result.current.validate(invalidData)
      expect(validationResult.success).toBe(false)
    })
    
    expect(result.current.isValid).toBe(false)
    expect(result.current.errors).toBeTruthy()
    expect(result.current.errors.length).toBeGreaterThan(0)
  })

  it('updates field values correctly', () => {
    const { result } = renderHook(() => useValidation(testSchema))
    
    act(() => {
      result.current.updateField('name', 'John Doe')
    })
    
    expect(result.current.data.name).toBe('John Doe')
    expect(result.current.touched.name).toBe(true)
  })

  it('validates individual fields', () => {
    const { result } = renderHook(() => useValidation(testSchema))
    
    act(() => {
      result.current.updateField('name', 'J') // Too short
      result.current.touchField('name')
    })
    
    const error = result.current.getError('name')
    expect(error).toBe('Name must be at least 2 characters')
    expect(result.current.hasError('name')).toBe(true)
  })

  it('clears errors when field becomes valid', () => {
    const { result } = renderHook(() => useValidation(testSchema))
    
    // First set invalid value
    act(() => {
      result.current.updateField('name', 'J')
      result.current.touchField('name')
    })
    
    expect(result.current.hasError('name')).toBe(true)
    
    // Then set valid value
    act(() => {
      result.current.updateField('name', 'John Doe')
    })
    
    expect(result.current.hasError('name')).toBe(false)
  })

  it('resets form correctly', () => {
    const initialData = { name: 'Initial', email: '<EMAIL>', age: 20 }
    const { result } = renderHook(() => useValidation(testSchema, initialData))
    
    // Make some changes
    act(() => {
      result.current.updateField('name', 'Changed')
      result.current.touchField('name')
    })
    
    expect(result.current.data.name).toBe('Changed')
    expect(result.current.touched.name).toBe(true)
    
    // Reset
    act(() => {
      result.current.reset()
    })
    
    expect(result.current.data).toEqual(initialData)
    expect(result.current.touched).toEqual({})
    expect(result.current.errors).toBeNull()
  })

  it('sets validated data correctly', () => {
    const { result } = renderHook(() => useValidation(testSchema))
    
    const newData = { name: 'John Doe', email: '<EMAIL>', age: 25 }
    
    act(() => {
      result.current.setData(newData)
    })
    
    expect(result.current.data).toEqual(newData)
    expect(result.current.isValid).toBe(true)
  })

  it('handles field validation for non-existent fields gracefully', () => {
    const { result } = renderHook(() => useValidation(testSchema))
    
    act(() => {
      const error = result.current.validateField('nonExistentField', 'value')
      expect(error).toBeNull()
    })
  })
})

describe('useFormSubmission', () => {
  const mockOnSubmit = jest.fn()
  
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('initializes correctly', () => {
    const { result } = renderHook(() => useFormSubmission(testSchema, mockOnSubmit))
    
    expect(result.current.isSubmitting).toBe(false)
    expect(result.current.submitError).toBeNull()
    expect(typeof result.current.handleSubmit).toBe('function')
  })

  it('handles successful form submission', async () => {
    mockOnSubmit.mockResolvedValue()
    
    const { result } = renderHook(() => useFormSubmission(testSchema, mockOnSubmit))
    
    // Set valid data
    act(() => {
      result.current.setData({ name: 'John Doe', email: '<EMAIL>', age: 25 })
    })
    
    // Submit form
    await act(async () => {
      await result.current.handleSubmit()
    })
    
    expect(mockOnSubmit).toHaveBeenCalledWith({
      name: 'John Doe',
      email: '<EMAIL>',
      age: 25
    })
    expect(result.current.submitError).toBeNull()
  })

  it('handles form submission with validation errors', async () => {
    const { result } = renderHook(() => useFormSubmission(testSchema, mockOnSubmit))
    
    // Set invalid data
    act(() => {
      result.current.setData({ name: 'J', email: 'invalid', age: 16 })
    })
    
    // Submit form
    await act(async () => {
      await result.current.handleSubmit()
    })
    
    expect(mockOnSubmit).not.toHaveBeenCalled()
    expect(result.current.isValid).toBe(false)
  })

  it('handles submission errors', async () => {
    const errorMessage = 'Submission failed'
    mockOnSubmit.mockRejectedValue(new Error(errorMessage))
    
    const { result } = renderHook(() => useFormSubmission(testSchema, mockOnSubmit))
    
    // Set valid data
    act(() => {
      result.current.setData({ name: 'John Doe', email: '<EMAIL>', age: 25 })
    })
    
    // Submit form
    await act(async () => {
      await result.current.handleSubmit()
    })
    
    expect(result.current.submitError).toBe(errorMessage)
    expect(result.current.isSubmitting).toBe(false)
  })

  it('sets isSubmitting state correctly during submission', async () => {
    let resolveSubmit
    const submitPromise = new Promise(resolve => {
      resolveSubmit = resolve
    })
    mockOnSubmit.mockReturnValue(submitPromise)
    
    const { result } = renderHook(() => useFormSubmission(testSchema, mockOnSubmit))
    
    // Set valid data
    act(() => {
      result.current.setData({ name: 'John Doe', email: '<EMAIL>', age: 25 })
    })
    
    // Start submission
    act(() => {
      result.current.handleSubmit()
    })
    
    expect(result.current.isSubmitting).toBe(true)
    
    // Resolve submission
    await act(async () => {
      resolveSubmit()
      await submitPromise
    })
    
    expect(result.current.isSubmitting).toBe(false)
  })

  it('clears submit error', () => {
    const { result } = renderHook(() => useFormSubmission(testSchema, mockOnSubmit))
    
    // Manually set an error (in real usage this would come from failed submission)
    act(() => {
      result.current.clearSubmitError()
    })
    
    expect(result.current.submitError).toBeNull()
  })

  it('prevents default on form events', async () => {
    const mockEvent = {
      preventDefault: jest.fn(),
    }
    
    const { result } = renderHook(() => useFormSubmission(testSchema, mockOnSubmit))
    
    act(() => {
      result.current.setData({ name: 'John Doe', email: '<EMAIL>', age: 25 })
    })
    
    await act(async () => {
      await result.current.handleSubmit(mockEvent)
    })
    
    expect(mockEvent.preventDefault).toHaveBeenCalled()
  })
})
