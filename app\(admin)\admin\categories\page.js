"use client";
import { useEffect, useState } from 'react';
import { useSupabase } from '../hooks/useSupabase';
import CategoryForm from '../components/categoryform';
import { toast } from 'sonner';

export default function CategoryManagement() {
  const { useCategories, useAddCategory, useUpdateCategory, useDeleteCategory } = useSupabase();
  const [selectedCategory, setSelectedCategory] = useState(null);

  // Use the React Query hook
  const { data: categories = [] } = useCategories();

  const addCategoryMutation = useAddCategory();
  const updateCategoryMutation = useUpdateCategory();
  const deleteCategoryMutation = useDeleteCategory();
  
  useEffect(() => {
    console.log('Selected category changed:', selectedCategory);
  }, [selectedCategory]);

  const handleSave = async (categoryData) => {
    try {
      if (selectedCategory) {
        await updateCategoryMutation.mutateAsync({
          id: selectedCategory.id,
          categoryData
        });
        toast.success("Category updated successfully");
      } else {
        await addCategoryMutation.mutateAsync(categoryData);
        toast.success("Category created successfully");
      }
      setSelectedCategory(null);
    } catch (error) {
      toast.error(`Error: ${error.message || 'Failed to save category'}`);
    }
  };

  return (
    <div className="p-6 text-primarycolor">
      <h1 className="text-2xl font-bold mb-6">Category Management</h1>
      <CategoryForm
        onSave={handleSave}
        initialData={selectedCategory}
      />
      <div className="mt-6">
        {categories.map(category => (
          <div key={category.id} className="flex items-center justify-between p-4 border-b">
            <div>
              <h3 className="font-medium">{category.name}</h3>
              <p className="text-sm text-secondarycolor">{category.description}</p>
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => {
                  console.log('Edit clicked:', category);
                  setSelectedCategory(category);
                }}
                className="text-blue-600 hover:text-blue-800"
              >
                Edit
              </button>
              <button
                onClick={() => deleteCategoryMutation.mutate(category.id)}
                className="text-red-600 hover:text-red-800"
              >
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
