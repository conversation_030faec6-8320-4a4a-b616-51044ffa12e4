#!/usr/bin/env node

/**
 * Supabase Database Structure Fetcher
 * 
 * This script fetches the complete database structure from Supabase including:
 * - Tables and their columns
 * - Row Level Security (RLS) policies
 * - Functions and triggers
 * - Indexes and constraints
 * 
 * Usage:
 * node scripts/fetch-db-structure.js
 * 
 * Requirements:
 * - NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY in .env.local
 * - Or SUPABASE_SERVICE_ROLE_KEY for admin access
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

class DatabaseStructureFetcher {
  constructor() {
    this.supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    this.supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    
    if (!this.supabaseUrl || !this.supabaseKey) {
      throw new Error('Missing Supabase credentials. Please check your .env.local file.');
    }
    
    this.supabase = createClient(this.supabaseUrl, this.supabaseKey);
    this.outputDir = path.join(__dirname, '..', 'database-structure');
  }

  /**
   * Fetch all tables in the public schema
   */
  async fetchTables() {
    console.log('📋 Fetching tables...');
    
    const { data, error } = await this.supabase
      .from('information_schema.tables')
      .select('table_name, table_type')
      .eq('table_schema', 'public')
      .order('table_name');
    
    if (error) {
      console.error('Error fetching tables:', error);
      return [];
    }
    
    return data || [];
  }

  /**
   * Fetch columns for a specific table
   */
  async fetchTableColumns(tableName) {
    const { data, error } = await this.supabase
      .from('information_schema.columns')
      .select(`
        column_name,
        data_type,
        is_nullable,
        column_default,
        character_maximum_length,
        numeric_precision,
        numeric_scale
      `)
      .eq('table_schema', 'public')
      .eq('table_name', tableName)
      .order('ordinal_position');
    
    if (error) {
      console.error(`Error fetching columns for ${tableName}:`, error);
      return [];
    }
    
    return data || [];
  }

  /**
   * Fetch foreign key constraints
   */
  async fetchForeignKeys() {
    console.log('🔗 Fetching foreign key constraints...');
    
    const { data, error } = await this.supabase.rpc('get_foreign_keys');
    
    if (error) {
      console.log('Note: Could not fetch foreign keys (requires custom function)');
      return [];
    }
    
    return data || [];
  }

  /**
   * Fetch RLS policies
   */
  async fetchRLSPolicies() {
    console.log('🔒 Fetching RLS policies...');
    
    const { data, error } = await this.supabase
      .from('pg_policies')
      .select(`
        schemaname,
        tablename,
        policyname,
        permissive,
        roles,
        cmd,
        qual,
        with_check
      `);
    
    if (error) {
      console.log('Note: Could not fetch RLS policies (requires elevated permissions)');
      return [];
    }
    
    return data || [];
  }

  /**
   * Test actual table access and data
   */
  async testTableAccess() {
    console.log('🧪 Testing table access...');
    
    const commonTables = [
      'users', 'products', 'categories', 'orders', 'cart', 
      'wishlists', 'marketing_banners', 'order_items'
    ];
    
    const accessResults = {};
    
    for (const tableName of commonTables) {
      try {
        const { data, error, count } = await this.supabase
          .from(tableName)
          .select('*', { count: 'exact', head: true });
        
        if (error) {
          accessResults[tableName] = { 
            accessible: false, 
            error: error.message,
            code: error.code 
          };
        } else {
          accessResults[tableName] = { 
            accessible: true, 
            rowCount: count 
          };
        }
      } catch (err) {
        accessResults[tableName] = { 
          accessible: false, 
          error: err.message 
        };
      }
    }
    
    return accessResults;
  }

  /**
   * Analyze existing queries in the codebase
   */
  analyzeCodebaseQueries() {
    console.log('🔍 Analyzing codebase queries...');
    
    // This would scan the codebase for .from() calls
    // For now, return known tables from the code analysis
    return {
      tables_used: [
        'users', 'products', 'categories', 'orders', 'cart', 
        'wishlists', 'marketing_banners', 'order_items'
      ],
      common_queries: [
        'products with categories join',
        'user orders with product details',
        'cart items with product info',
        'wishlist items',
        'dashboard sales data (RPC)'
      ]
    };
  }

  /**
   * Generate comprehensive database report
   */
  async generateReport() {
    console.log('🚀 Starting database structure analysis...\n');
    
    const report = {
      generated_at: new Date().toISOString(),
      supabase_url: this.supabaseUrl,
      analysis: {}
    };

    try {
      // Fetch basic table information
      const tables = await this.fetchTables();
      report.analysis.tables = {};
      
      for (const table of tables) {
        console.log(`📊 Analyzing table: ${table.table_name}`);
        const columns = await this.fetchTableColumns(table.table_name);
        
        report.analysis.tables[table.table_name] = {
          type: table.table_type,
          columns: columns.map(col => ({
            name: col.column_name,
            type: col.data_type,
            nullable: col.is_nullable === 'YES',
            default: col.column_default,
            max_length: col.character_maximum_length
          }))
        };
      }
      
      // Test table access
      report.analysis.table_access = await this.testTableAccess();
      
      // Analyze codebase usage
      report.analysis.codebase_usage = this.analyzeCodebaseQueries();
      
      // Try to fetch RLS policies
      report.analysis.rls_policies = await this.fetchRLSPolicies();
      
    } catch (error) {
      console.error('Error generating report:', error);
      report.error = error.message;
    }

    return report;
  }

  /**
   * Save report to file
   */
  async saveReport(report) {
    // Ensure output directory exists
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
    }
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `database-structure-${timestamp}.json`;
    const filepath = path.join(this.outputDir, filename);
    
    fs.writeFileSync(filepath, JSON.stringify(report, null, 2));
    
    // Also create a latest.json for easy access
    const latestPath = path.join(this.outputDir, 'latest.json');
    fs.writeFileSync(latestPath, JSON.stringify(report, null, 2));
    
    console.log(`\n✅ Database structure report saved to:`);
    console.log(`   ${filepath}`);
    console.log(`   ${latestPath}`);
    
    return filepath;
  }

  /**
   * Print summary to console
   */
  printSummary(report) {
    console.log('\n📊 DATABASE STRUCTURE SUMMARY');
    console.log('================================');
    
    if (report.analysis.tables) {
      console.log(`\n📋 Tables Found: ${Object.keys(report.analysis.tables).length}`);
      Object.entries(report.analysis.tables).forEach(([name, info]) => {
        console.log(`   • ${name} (${info.columns.length} columns)`);
      });
    }
    
    if (report.analysis.table_access) {
      console.log('\n🔐 Table Access Status:');
      Object.entries(report.analysis.table_access).forEach(([table, access]) => {
        const status = access.accessible ? '✅' : '❌';
        const info = access.accessible 
          ? `${access.rowCount} rows` 
          : `Error: ${access.error}`;
        console.log(`   ${status} ${table} - ${info}`);
      });
    }
    
    if (report.analysis.codebase_usage) {
      console.log('\n💻 Codebase Usage:');
      console.log(`   Tables used: ${report.analysis.codebase_usage.tables_used.join(', ')}`);
    }
  }

  /**
   * Main execution function
   */
  async run() {
    try {
      const report = await this.generateReport();
      await this.saveReport(report);
      this.printSummary(report);
      
      console.log('\n🎉 Database structure analysis complete!');
      
    } catch (error) {
      console.error('❌ Error:', error.message);
      process.exit(1);
    }
  }
}

// Run the script
if (require.main === module) {
  const fetcher = new DatabaseStructureFetcher();
  fetcher.run();
}

module.exports = DatabaseStructureFetcher;
