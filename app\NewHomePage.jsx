"use client";

import { useProducts, useCategories } from './hooks/useQueries';
import { useState } from 'react';
import { Search, ArrowRight, TrendingUp, Star } from 'lucide-react';
import Link from 'next/link';
import { ProductGridSkeleton, CategoryListSkeleton } from './components/LoadingStates';
import HeroSection, { ModernCategories, PromotionalBanner } from './components/HeroSection';
import FlashSaleSection from './components/FlashSaleSection';
import ModernProductSection, { HorizontalProductSection } from './components/ModernProductSection';
import EnhancedProductCard from './components/EnhancedProductCard';
import Card, { CardHeader, CardTitle, CardContent } from './components/ui/Card';
import Button from './components/ui/Button';
import { SearchInput } from './components/ui/Input';


export default function NewHomePage() {
  const { data: products = [], isLoading: productsLoading, error: productsError } = useProducts();
  const { data: categories = [], isLoading: categoriesLoading, error: categoriesError } = useCategories();
  const [searchTerm, setSearchTerm] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Get featured/promoted products
  const featuredProducts = products.filter(product => product.is_promoted).slice(0, 6);
  const newArrivals = products.slice(0, 8);
  const bestSellers = products.slice(8, 16); // Mock best sellers
  const topDeals = products.filter(product => product.discount > 0).slice(0, 8);

  const handleSearch = (query) => {
    if (query.trim()) {
      // Navigate to search results
      window.location.href = `/search?q=${encodeURIComponent(query)}`;
    }
  };

  const handleInputChange = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    
    if (value.length > 2) {
      const filtered = products
        .filter(product => 
          product.name.toLowerCase().includes(value.toLowerCase())
        )
        .slice(0, 5);
      setSuggestions(filtered);
      setShowSuggestions(true);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <HeroSection
        featuredProducts={featuredProducts}
      />

      {/* Modern Categories */}
      <ModernCategories categories={categories} />

      {/* Flash Sale Section */}
      <FlashSaleSection
        products={topDeals}
        endTime={new Date(Date.now() + 24 * 60 * 60 * 1000)} // 24 hours from now
      />

      {/* Featured Products Section */}
      <ModernProductSection
        title="Featured Products"
        products={featuredProducts}
        viewAllLink="/featured"
        compact
      />

      {/* New Arrivals Section */}
      <ModernProductSection
        title="New Arrivals"
        products={newArrivals}
        viewAllLink="/new-arrivals"
      />

      {/* Best Sellers Section */}
      <HorizontalProductSection
        title="Best Sellers"
        products={bestSellers}
        viewAllLink="/best-sellers"
      />
    </div>
  );
}
