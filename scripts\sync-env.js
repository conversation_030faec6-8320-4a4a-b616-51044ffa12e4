#!/usr/bin/env node

/**
 * Vercel Environment Variables Sync Script
 *
 * This script fetches environment variables from Vercel and creates/updates local .env files
 *
 * Usage with npm scripts (recommended):
 * npm run sync-env              # Sync development environment
 * npm run sync-env:dev          # Sync development environment
 * npm run sync-env:prod         # Sync production environment
 * npm run sync-env:preview      # Sync preview environment
 *
 * Usage with npx:
 * npx node scripts/sync-env.js [environment] [project-id]
 *
 * Direct usage:
 * node scripts/sync-env.js [environment] [project-id]
 *
 * Examples:
 * npm run sync-env:prod                    # Fetch production env to .env.production
 * npx node scripts/sync-env.js production # Same as above
 * node scripts/sync-env.js development    # Fetch development env to .env.local
 *
 * Requirements:
 * - Vercel CLI (will use npx vercel if not globally installed)
 * - Authenticated with Vercel: npx vercel login
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const DEFAULT_ENVIRONMENTS = ['development', 'preview', 'production'];
const ENV_FILE_MAP = {
  development: '.env.local',
  preview: '.env.preview',
  production: '.env.production'
};

class VercelEnvSync {
  constructor() {
    this.projectRoot = process.cwd();
    this.scriptsDir = path.join(this.projectRoot, 'scripts');
    this.envBackupDir = path.join(this.scriptsDir, 'env-backups');
    this.vercelCommand = 'vercel'; // Will be set to 'npx vercel' if global install not found
  }

  /**
   * Main execution function
   */
  async run() {
    try {
      console.log('🚀 Starting Vercel Environment Variables Sync...\n');
      
      // Parse command line arguments
      const args = process.argv.slice(2);
      const environment = args[0] || 'development';
      const projectId = args[1] || await this.getProjectId();
      
      // Validate environment
      if (!DEFAULT_ENVIRONMENTS.includes(environment)) {
        throw new Error(`Invalid environment: ${environment}. Valid options: ${DEFAULT_ENVIRONMENTS.join(', ')}`);
      }

      console.log(`📋 Environment: ${environment}`);
      console.log(`🏗️  Project ID: ${projectId}\n`);

      // Check Vercel CLI
      await this.checkVercelCLI();
      
      // Create backup directory
      this.ensureBackupDirectory();
      
      // Backup existing env file
      await this.backupExistingEnv(environment);
      
      // Fetch environment variables
      const envVars = await this.fetchEnvironmentVariables(environment, projectId);
      
      // Write to local file
      await this.writeEnvFile(environment, envVars);
      
      console.log('✅ Environment variables synced successfully!');
      console.log(`📁 File created/updated: ${ENV_FILE_MAP[environment]}\n`);
      
      // Show summary
      this.showSummary(envVars);
      
    } catch (error) {
      console.error('❌ Error:', error.message);
      process.exit(1);
    }
  }

  /**
   * Check if Vercel CLI is installed and user is authenticated
   */
  async checkVercelCLI() {
    // First try global vercel installation
    try {
      execSync('vercel --version', { stdio: 'pipe' });
      this.vercelCommand = 'vercel';
      console.log('✅ Vercel CLI found (global installation)');
    } catch (error) {
      // If global installation fails, try npx
      try {
        execSync('npx vercel --version', { stdio: 'pipe' });
        this.vercelCommand = 'npx vercel';
        console.log('✅ Vercel CLI found (using npx)');
      } catch (npxError) {
        throw new Error('Vercel CLI not found. Please install it globally: npm i -g vercel, or ensure npx is available');
      }
    }

    // Check authentication
    try {
      execSync(`${this.vercelCommand} whoami`, { stdio: 'pipe' });
      console.log('✅ Vercel authentication verified');
    } catch (error) {
      throw new Error(`Not authenticated with Vercel. Please run: ${this.vercelCommand} login`);
    }
  }

  /**
   * Get project ID from vercel.json or .vercel/project.json
   */
  async getProjectId() {
    // Try .vercel/project.json first
    const vercelProjectPath = path.join(this.projectRoot, '.vercel', 'project.json');
    if (fs.existsSync(vercelProjectPath)) {
      const projectData = JSON.parse(fs.readFileSync(vercelProjectPath, 'utf8'));
      return projectData.projectId;
    }

    // Try vercel.json
    const vercelConfigPath = path.join(this.projectRoot, 'vercel.json');
    if (fs.existsSync(vercelConfigPath)) {
      const vercelConfig = JSON.parse(fs.readFileSync(vercelConfigPath, 'utf8'));
      if (vercelConfig.name) {
        return vercelConfig.name;
      }
    }

    // Try package.json name
    const packageJsonPath = path.join(this.projectRoot, 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      return packageJson.name;
    }

    throw new Error('Could not determine project ID. Please provide it as a command line argument.');
  }

  /**
   * Ensure backup directory exists
   */
  ensureBackupDirectory() {
    if (!fs.existsSync(this.envBackupDir)) {
      fs.mkdirSync(this.envBackupDir, { recursive: true });
    }
  }

  /**
   * Backup existing environment file
   */
  async backupExistingEnv(environment) {
    const envFile = ENV_FILE_MAP[environment];
    const envPath = path.join(this.projectRoot, envFile);
    
    if (fs.existsSync(envPath)) {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupName = `${envFile}.${timestamp}.backup`;
      const backupPath = path.join(this.envBackupDir, backupName);
      
      fs.copyFileSync(envPath, backupPath);
      console.log(`💾 Backed up existing ${envFile} to ${backupName}`);
    }
  }

  /**
   * Fetch environment variables from Vercel
   */
  async fetchEnvironmentVariables(environment, projectId) {
    console.log('🔄 Fetching environment variables from Vercel...');

    try {
      // Use vercel env pull to get environment variables
      const tempEnvFile = path.join(this.projectRoot, `.env.${environment}.temp`);
      const command = `${this.vercelCommand} env pull ${tempEnvFile} --environment=${environment}`;

      console.log(`📡 Running: ${command}`);

      execSync(command, {
        stdio: 'pipe',
        encoding: 'utf8',
        cwd: this.projectRoot
      });

      // Read and parse the temporary env file
      const envContent = fs.readFileSync(tempEnvFile, 'utf8');
      const envVars = this.parseEnvFile(envContent);

      // Clean up temporary file
      fs.unlinkSync(tempEnvFile);

      console.log(`✅ Fetched ${envVars.length} environment variables`);

      return envVars;
    } catch (error) {
      throw new Error(`Failed to fetch environment variables: ${error.message}`);
    }
  }

  /**
   * Parse environment file content into key-value pairs
   */
  parseEnvFile(content) {
    const envVars = [];
    const lines = content.split('\n');

    for (const line of lines) {
      const trimmedLine = line.trim();

      // Skip empty lines and comments
      if (!trimmedLine || trimmedLine.startsWith('#')) {
        continue;
      }

      // Parse KEY=VALUE format
      const equalIndex = trimmedLine.indexOf('=');
      if (equalIndex > 0) {
        const key = trimmedLine.substring(0, equalIndex).trim();
        const value = trimmedLine.substring(equalIndex + 1).trim();

        // Remove quotes if present
        const cleanValue = value.replace(/^["']|["']$/g, '');

        envVars.push({ key, value: cleanValue });
      }
    }

    return envVars;
  }

  /**
   * Write environment variables to local file
   */
  async writeEnvFile(environment, envVars) {
    const envFile = ENV_FILE_MAP[environment];
    const envPath = path.join(this.projectRoot, envFile);
    
    let content = `# Environment variables for ${environment}\n`;
    content += `# Generated on ${new Date().toISOString()}\n`;
    content += `# DO NOT EDIT MANUALLY - Use 'npm run sync-env:${environment === 'development' ? 'dev' : environment}' to update\n\n`;
    
    // Group variables by type for better organization
    const grouped = this.groupEnvironmentVariables(envVars);
    
    Object.entries(grouped).forEach(([group, vars]) => {
      if (vars.length > 0) {
        content += `# ${group.toUpperCase()}\n`;
        vars.forEach(envVar => {
          content += `${envVar.key}=${envVar.value}\n`;
        });
        content += '\n';
      }
    });
    
    fs.writeFileSync(envPath, content);
    console.log(`📝 Written ${envVars.length} variables to ${envFile}`);
  }

  /**
   * Group environment variables by common prefixes
   */
  groupEnvironmentVariables(envVars) {
    const groups = {
      database: [],
      auth: [],
      api: [],
      next: [],
      vercel: [],
      other: []
    };

    envVars.forEach(envVar => {
      const key = envVar.key.toLowerCase();
      
      if (key.includes('database') || key.includes('db') || key.includes('supabase')) {
        groups.database.push(envVar);
      } else if (key.includes('auth') || key.includes('jwt') || key.includes('secret')) {
        groups.auth.push(envVar);
      } else if (key.includes('api') || key.includes('key')) {
        groups.api.push(envVar);
      } else if (key.startsWith('next_')) {
        groups.next.push(envVar);
      } else if (key.startsWith('vercel_')) {
        groups.vercel.push(envVar);
      } else {
        groups.other.push(envVar);
      }
    });

    return groups;
  }

  /**
   * Show summary of synced variables
   */
  showSummary(envVars) {
    console.log('📊 Summary:');
    console.log(`   Total variables: ${envVars.length}`);
    
    const grouped = this.groupEnvironmentVariables(envVars);
    Object.entries(grouped).forEach(([group, vars]) => {
      if (vars.length > 0) {
        console.log(`   ${group}: ${vars.length}`);
      }
    });
    
    console.log('\n💡 Usage Commands:');
    console.log('   📥 Fetch from production to local:');
    console.log('      npm run sync-env:prod');
    console.log('   📥 Fetch from development:');
    console.log('      npm run sync-env:dev');
    console.log('   📥 Fetch from preview:');
    console.log('      npm run sync-env:preview');
    console.log('');
    console.log('💡 Tips:');
    console.log('   - Add .env.* files to .gitignore');
    console.log('   - Run this script when environment variables change');
    console.log('   - Backups are stored in scripts/env-backups/');
    console.log(`   - Using: ${this.vercelCommand}`);
  }
}

// Run the script
if (require.main === module) {
  const sync = new VercelEnvSync();
  sync.run();
}

module.exports = VercelEnvSync;
