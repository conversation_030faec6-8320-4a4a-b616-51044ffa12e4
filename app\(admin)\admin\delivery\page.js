"use client";

import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  Truck, 
  MapPin, 
  Clock, 
  DollarSign, 
  Plus, 
  Edit, 
  Trash2,
  Search,
  Filter,
  Package,
  CheckCircle,
  AlertTriangle,
  Settings
} from 'lucide-react';
import { supabase } from '../../../lib/supabase';
import Card, { CardHeader, CardTitle, CardContent } from '../../../components/ui/Card';
import Button from '../../../components/ui/Button';
import Input, { FormGroup } from '../../../components/ui/Input';
import { formatCurrency } from '../../../lib/utils';
import { cn } from '../../../lib/utils';

// Mock delivery zones data (in a real app, this would come from database)
const MOCK_DELIVERY_ZONES = [
  {
    id: '1',
    name: 'Nairobi CBD',
    areas: ['CBD', 'Westlands', 'Kilimani', 'Lavington'],
    cost: 200,
    estimated_time: '2-4 hours',
    is_active: true,
    created_at: new Date().toISOString()
  },
  {
    id: '2',
    name: 'Nairobi Suburbs',
    areas: ['Karen', 'Langata', 'Runda', 'Muthaiga'],
    cost: 300,
    estimated_time: '4-6 hours',
    is_active: true,
    created_at: new Date().toISOString()
  },
  {
    id: '3',
    name: 'Kiambu County',
    areas: ['Thika', 'Ruiru', 'Kikuyu', 'Limuru'],
    cost: 500,
    estimated_time: '6-8 hours',
    is_active: true,
    created_at: new Date().toISOString()
  },
  {
    id: '4',
    name: 'Machakos County',
    areas: ['Machakos Town', 'Athi River', 'Mavoko'],
    cost: 600,
    estimated_time: '8-12 hours',
    is_active: false,
    created_at: new Date().toISOString()
  }
];

export default function DeliveryPage() {
  const [activeTab, setActiveTab] = useState('zones');
  const [searchTerm, setSearchTerm] = useState('');
  const [showZoneForm, setShowZoneForm] = useState(false);
  const [editingZone, setEditingZone] = useState(null);
  const [zoneFormData, setZoneFormData] = useState({
    name: '',
    areas: '',
    cost: '',
    estimated_time: '',
    is_active: true
  });

  // Fetch delivery statistics from orders
  const { data: deliveryStats, isLoading: statsLoading } = useQuery({
    queryKey: ['delivery-stats'],
    queryFn: async () => {
      const { data: orders, error } = await supabase
        .from('orders')
        .select('delivery_cost, status, created_at');

      if (error) throw error;

      const totalDeliveries = orders.length;
      const totalRevenue = orders.reduce((sum, order) => sum + (order.delivery_cost || 0), 0);
      const pendingDeliveries = orders.filter(order => 
        order.status === 'CONFIRMED' || order.status === 'PROCESSING'
      ).length;
      const completedDeliveries = orders.filter(order => 
        order.status === 'DELIVERED'
      ).length;

      return {
        totalDeliveries,
        totalRevenue,
        pendingDeliveries,
        completedDeliveries,
        averageDeliveryCost: totalDeliveries > 0 ? totalRevenue / totalDeliveries : 0
      };
    },
    staleTime: 5 * 60 * 1000,
  });

  // Mock delivery zones query (replace with real database query)
  const { data: deliveryZones = MOCK_DELIVERY_ZONES } = useQuery({
    queryKey: ['delivery-zones'],
    queryFn: async () => {
      // In a real app, this would fetch from database
      return MOCK_DELIVERY_ZONES;
    },
    staleTime: 10 * 60 * 1000,
  });

  const filteredZones = deliveryZones.filter(zone =>
    zone.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    zone.areas.some(area => area.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleZoneSubmit = (e) => {
    e.preventDefault();
    // In a real app, this would save to database
    console.log('Zone data:', zoneFormData);
    setShowZoneForm(false);
    setEditingZone(null);
    setZoneFormData({
      name: '',
      areas: '',
      cost: '',
      estimated_time: '',
      is_active: true
    });
  };

  const handleEditZone = (zone) => {
    setZoneFormData({
      name: zone.name,
      areas: zone.areas.join(', '),
      cost: zone.cost.toString(),
      estimated_time: zone.estimated_time,
      is_active: zone.is_active
    });
    setEditingZone(zone);
    setShowZoneForm(true);
  };

  if (statsLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
          <div className="h-96 bg-gray-200 rounded-lg"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Delivery Management</h1>
          <p className="text-gray-600">Manage delivery zones, costs, and logistics</p>
        </div>
        <Button onClick={() => setShowZoneForm(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Add Delivery Zone
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Deliveries</p>
              <p className="text-2xl font-bold text-gray-900">
                {deliveryStats?.totalDeliveries || 0}
              </p>
            </div>
            <div className="p-2 bg-blue-50 rounded-lg">
              <Package className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Pending Deliveries</p>
              <p className="text-2xl font-bold text-gray-900">
                {deliveryStats?.pendingDeliveries || 0}
              </p>
            </div>
            <div className="p-2 bg-yellow-50 rounded-lg">
              <Clock className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-gray-900">
                {deliveryStats?.completedDeliveries || 0}
              </p>
            </div>
            <div className="p-2 bg-green-50 rounded-lg">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Delivery Revenue</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(deliveryStats?.totalRevenue || 0)}
              </p>
            </div>
            <div className="p-2 bg-purple-50 rounded-lg">
              <DollarSign className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </Card>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <div className="flex space-x-8">
          <button
            onClick={() => setActiveTab('zones')}
            className={cn(
              'py-2 px-1 border-b-2 font-medium text-sm',
              activeTab === 'zones'
                ? 'border-primarycolor text-primarycolor'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            )}
          >
            <div className="flex items-center gap-2">
              <MapPin className="w-4 h-4" />
              Delivery Zones
            </div>
          </button>
          <button
            onClick={() => setActiveTab('settings')}
            className={cn(
              'py-2 px-1 border-b-2 font-medium text-sm',
              activeTab === 'settings'
                ? 'border-primarycolor text-primarycolor'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            )}
          >
            <div className="flex items-center gap-2">
              <Settings className="w-4 h-4" />
              Settings
            </div>
          </button>
        </div>
      </div>

      {/* Delivery Zones Tab */}
      {activeTab === 'zones' && (
        <div className="space-y-6">
          {/* Search and Filters */}
          <Card className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <Input
                  type="text"
                  placeholder="Search delivery zones or areas..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  icon={Search}
                />
              </div>
              <Button variant="outline">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
            </div>
          </Card>

          {/* Delivery Zones */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredZones.map((zone) => (
              <Card key={zone.id} className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex items-center gap-3">
                    <div className={cn(
                      'p-2 rounded-lg',
                      zone.is_active ? 'bg-green-50' : 'bg-gray-50'
                    )}>
                      <MapPin className={cn(
                        'w-5 h-5',
                        zone.is_active ? 'text-green-600' : 'text-gray-400'
                      )} />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{zone.name}</h3>
                      <span className={cn(
                        'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                        zone.is_active 
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      )}>
                        {zone.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                  <div className="flex gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditZone(zone)}
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="sm" className="text-red-600">
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium text-gray-700">Coverage Areas:</p>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {zone.areas.map((area, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-full"
                        >
                          {area}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-gray-700">Delivery Cost:</p>
                      <p className="text-lg font-bold text-primarycolor">
                        {formatCurrency(zone.cost)}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Est. Time:</p>
                      <p className="text-sm text-gray-600 flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        {zone.estimated_time}
                      </p>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Settings Tab */}
      {activeTab === 'settings' && (
        <div className="space-y-6">
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Delivery Settings</h3>
            
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormGroup label="Default Delivery Cost">
                  <Input
                    type="number"
                    placeholder="Enter default cost"
                    defaultValue="250"
                  />
                </FormGroup>
                
                <FormGroup label="Free Delivery Threshold">
                  <Input
                    type="number"
                    placeholder="Minimum order amount"
                    defaultValue="2000"
                  />
                </FormGroup>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormGroup label="Standard Delivery Time">
                  <Input
                    type="text"
                    placeholder="e.g., 2-4 hours"
                    defaultValue="2-4 hours"
                  />
                </FormGroup>
                
                <FormGroup label="Express Delivery Surcharge">
                  <Input
                    type="number"
                    placeholder="Additional cost"
                    defaultValue="200"
                  />
                </FormGroup>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="enable_tracking"
                  defaultChecked
                  className="w-4 h-4 text-primarycolor border-gray-300 rounded focus:ring-primarycolor"
                />
                <label htmlFor="enable_tracking" className="text-sm text-gray-700">
                  Enable delivery tracking for customers
                </label>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="sms_notifications"
                  defaultChecked
                  className="w-4 h-4 text-primarycolor border-gray-300 rounded focus:ring-primarycolor"
                />
                <label htmlFor="sms_notifications" className="text-sm text-gray-700">
                  Send SMS notifications for delivery updates
                </label>
              </div>

              <div className="pt-4">
                <Button>Save Settings</Button>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Add/Edit Zone Modal */}
      {showZoneForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="max-w-2xl w-full">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-900">
                  {editingZone ? 'Edit Delivery Zone' : 'Add New Delivery Zone'}
                </h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setShowZoneForm(false);
                    setEditingZone(null);
                    setZoneFormData({
                      name: '',
                      areas: '',
                      cost: '',
                      estimated_time: '',
                      is_active: true
                    });
                  }}
                >
                  ✕
                </Button>
              </div>

              <form onSubmit={handleZoneSubmit} className="space-y-4">
                <FormGroup label="Zone Name">
                  <Input
                    type="text"
                    placeholder="e.g., Nairobi CBD"
                    value={zoneFormData.name}
                    onChange={(e) => setZoneFormData(prev => ({ ...prev, name: e.target.value }))}
                    required
                  />
                </FormGroup>

                <FormGroup label="Coverage Areas">
                  <Input
                    type="text"
                    placeholder="e.g., CBD, Westlands, Kilimani (comma separated)"
                    value={zoneFormData.areas}
                    onChange={(e) => setZoneFormData(prev => ({ ...prev, areas: e.target.value }))}
                    required
                  />
                </FormGroup>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormGroup label="Delivery Cost (KES)">
                    <Input
                      type="number"
                      placeholder="250"
                      value={zoneFormData.cost}
                      onChange={(e) => setZoneFormData(prev => ({ ...prev, cost: e.target.value }))}
                      required
                    />
                  </FormGroup>

                  <FormGroup label="Estimated Delivery Time">
                    <Input
                      type="text"
                      placeholder="e.g., 2-4 hours"
                      value={zoneFormData.estimated_time}
                      onChange={(e) => setZoneFormData(prev => ({ ...prev, estimated_time: e.target.value }))}
                      required
                    />
                  </FormGroup>
                </div>

                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="zone_active"
                    checked={zoneFormData.is_active}
                    onChange={(e) => setZoneFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                    className="w-4 h-4 text-primarycolor border-gray-300 rounded focus:ring-primarycolor"
                  />
                  <label htmlFor="zone_active" className="text-sm text-gray-700">
                    Zone is active and available for delivery
                  </label>
                </div>

                <div className="flex gap-3 pt-4">
                  <Button type="submit" className="flex-1">
                    {editingZone ? 'Update Zone' : 'Add Zone'}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setShowZoneForm(false);
                      setEditingZone(null);
                    }}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
}
