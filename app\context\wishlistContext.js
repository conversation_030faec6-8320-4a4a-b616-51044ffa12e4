"use client"
import { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useSupabase } from '../hooks/useSupabase';

export const WishlistContext = createContext();

export function WishlistProvider({ children }) {
  const { user } = useAuth();
  const { fetchWishlistItems, addToWishlist, deleteFromWishlist } = useSupabase();
  const [wishlistItems, setWishlistItems] = useState([]);
  const initialized = useRef(false);

  useEffect(() => {
    if (!initialized.current) {
      initialized.current = true;

      if (user) {
        fetchWishlistItems(user.id).then(dbWishlist => {
          setWishlistItems(dbWishlist);
        });
      } else {
        const localWishlist = JSON.parse(localStorage.getItem('wishlist') || '[]');
        setWishlistItems(localWishlist);
      }
    }
  }, [user, fetchWishlistItems]);

  const toggleWishlistItem = useCallback(async (product) => {
    if (user) {
      const isInWishlist = wishlistItems.some(item => item.product_id === product.id);
      
      if (isInWishlist) {
        await deleteFromWishlist(user.id, product.id);
      } else {
        await addToWishlist(user.id, product.id);
      }
      
      const updatedWishlist = await fetchWishlistItems(user.id);
      setWishlistItems(updatedWishlist);
    } else {
      const localWishlist = [...wishlistItems];
      const isInWishlist = localWishlist.some(item => item.id === product.id);
      
      const newWishlist = isInWishlist
        ? localWishlist.filter(item => item.id !== product.id)
        : [...localWishlist, product];
      
      setWishlistItems(newWishlist);
      localStorage.setItem('wishlist', JSON.stringify(newWishlist));
    }
  }, [user, wishlistItems, addToWishlist, deleteFromWishlist, fetchWishlistItems]);

  const isInWishlist = useCallback((productId) => 
    wishlistItems.some(item => (item.product_id === productId || item.id === productId)),
    [wishlistItems]
  );

  return (
    <WishlistContext.Provider value={{ 
      wishlistItems, 
      toggleWishlistItem,
      isInWishlist
    }}>
      {children}
    </WishlistContext.Provider>
  );
}
