{"name": "sherrys-merchants-households", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "sync-env": "node scripts/sync-env.js", "sync-env:dev": "node scripts/sync-env.js development", "sync-env:prod": "node scripts/sync-env.js production", "sync-env:preview": "node scripts/sync-env.js preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "node scripts/test-runner.js unit", "test:integration": "node scripts/test-runner.js integration", "test:e2e": "node scripts/test-runner.js e2e", "test:all": "node scripts/test-runner.js all"}, "dependencies": {"@heroicons/react": "^2.1.5", "@iconify-icons/arcticons": "^1.2.77", "@iconify/react": "^5.0.2", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.45.4", "@tanstack/react-query": "^5.59.16", "@tanstack/react-query-devtools": "^5.59.16", "chart.js": "^4.4.4", "clsx": "^2.1.0", "cookies-next": "^4.3.0", "dotenv": "^17.2.0", "framer-motion": "^11.11.9", "lucide-react": "^0.446.0", "next": "^14.2.22", "nodemailer": "^6.9.16", "react": "^18", "react-chartjs-2": "^5.2.0", "react-dom": "^18", "react-icons": "^5.3.0", "resend": "^4.0.1-alpha.0", "sharp": "^0.33.5", "sonner": "^1.5.0", "swiper": "^11.1.14", "tailwind-merge": "^2.2.0", "tailwind-scrollbar-hide": "^1.1.7", "uuid": "^10.0.0", "zod": "^4.0.5"}, "devDependencies": {"@shadcn/ui": "^0.0.4", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/react": "18.3.12", "@types/uuid": "^10.0.0", "eslint": "^8", "eslint-config-next": "14.2.13", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "tailwindcss": "^3.4.1"}}