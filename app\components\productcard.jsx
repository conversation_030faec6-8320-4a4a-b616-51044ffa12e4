"use client";
import { Heart, ShoppingCart, Star, Eye, Badge } from "lucide-react";
import Link from "next/link";
import { useState, useEffect, useContext } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "../hooks/useAuth";
import { useSupabase } from "../hooks/useSupabase";
import { WishlistContext } from "../context/wishlistContext";
import { useCallback, useMemo } from "react";
import { ProductImage } from "./OptimizedImage";
import Button, { IconButton } from "./ui/Button";
import { cn, formatCurrency } from "../lib/utils";

export default function ProductCard({ product }) {
  const { isInWishlist, toggleWishlistItem } = useContext(WishlistContext);

  const handleWishlistClick = useCallback(
    (e) => {
      e.preventDefault();
      toggleWishlistItem(product);
    },
    [toggleWishlistItem, product]
  );

  const productInWishlist = useMemo(
    () => isInWishlist(product.id),
    [isInWishlist, product.id]
  );

  const [discountPercentage, setDiscountPercentage] = useState(0);

  // Check if the product is in the user's wishlist when the component mounts
  useEffect(() => {
    // Fetch and set the discount percentage for the product
    if (product.discount) {
      setDiscountPercentage(product.discount);
    }
  }, [product.discount]);

  const displayPrice =
    product.discount > 0
      ? product.price * (1 - product.discount / 100)
      : product.price;

  return (
    <Link
      href={`/product/${product.id}`}
      className="group bg-white h-full flex flex-col shadow-sm hover:shadow-md transition-all duration-300 max-w-[300px] mx-auto"
    >
      {/* Image container */}
      <div className="relative w-full aspect-square overflow-hidden bg-gray-50 md:aspect-[4/3]">
        <ProductImage
          product={product}
          fill
          priority={false}
          className="object-contain hover:scale-105 transition-transform duration-300"
          containerClassName="w-full h-full"
        />
        {discountPercentage > 0 && (
          <div className="absolute top-0 left-0 bg-warningcolor text-white px-2 py-1 text-sm font-medium">
            SAVE {discountPercentage}%
          </div>
        )}
        <button
          onClick={handleWishlistClick}
          className="absolute top-0 right-2 p-1.5 transition-colors"
        >
          <Heart
            className={
              productInWishlist
                ? "fill-warningcolor text-warningcolor"
                : "text-warningcolor"
            }
            size={28}
          />
        </button>
      </div>

      {/* Product info section */}
      <div className="p-3 md:p-4 flex flex-col gap-2 border-t">
        <h2 className="text-sm md:text-base text-primarycolor font-medium truncate">
          {product.name}
        </h2>

        <div className="flex flex-col gap-0.5 md:gap-1">
          {/* Price section with responsive text sizes */}
          {discountPercentage > 0 ? (
            <>
              <p className="text-xs md:text-sm text-primarycolorvariant line-through">
                Ksh. {Math.floor(Number(product.price))}
              </p>
              <p className="text-base md:text-lg font-bold text-primarycolor">
                Ksh.{" "}
                {Math.floor(
                  Number(product.price) * (1 - discountPercentage / 100)
                )}
              </p>
            </>
          ) : (
            <p className="text-base md:text-lg font-bold text-primarycolor">
              Ksh. {Math.floor(Number(product.price))}
            </p>
          )}
        </div>
      </div>
    </Link>
  );
}
