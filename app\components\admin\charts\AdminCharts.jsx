"use client";

import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler
} from 'chart.js';
import { Line, Bar, Pie, Doughnut } from 'react-chartjs-2';
import { formatCurrency } from '../../../lib/utils';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler
);

// Chart color palette
const COLORS = {
  primary: '#8B5CF6',
  secondary: '#06B6D4',
  success: '#10B981',
  warning: '#F59E0B',
  danger: '#EF4444',
  info: '#3B82F6',
  light: '#F3F4F6',
  dark: '#1F2937'
};

const CHART_COLORS = [
  COLORS.primary,
  COLORS.secondary,
  COLORS.success,
  COLORS.warning,
  COLORS.danger,
  COLORS.info
];

// Default chart options
const defaultOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top',
    },
  },
  scales: {
    x: {
      grid: {
        display: false,
      },
    },
    y: {
      grid: {
        color: '#F3F4F6',
      },
    },
  },
};

export function SalesOverviewChart({ salesData = [] }) {
  // Process sales data for the last 30 days
  const processedData = React.useMemo(() => {
    const last30Days = Array.from({ length: 30 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (29 - i));
      return date.toISOString().split('T')[0];
    });

    const salesByDate = salesData.reduce((acc, order) => {
      const orderDate = new Date(order.created_at).toISOString().split('T')[0];
      if (!acc[orderDate]) {
        acc[orderDate] = { confirmed: 0, pending: 0, total: 0 };
      }
      
      const amount = order.total_amount || 0;
      acc[orderDate].total += amount;
      
      if (order.status === 'CONFIRMED' || order.status === 'DELIVERED') {
        acc[orderDate].confirmed += amount;
      } else if (order.status === 'PENDING') {
        acc[orderDate].pending += amount;
      }
      
      return acc;
    }, {});

    return {
      labels: last30Days.map(date => {
        const d = new Date(date);
        return d.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      }),
      datasets: [
        {
          label: 'Confirmed Sales',
          data: last30Days.map(date => salesByDate[date]?.confirmed || 0),
          borderColor: COLORS.success,
          backgroundColor: COLORS.success + '20',
          fill: true,
          tension: 0.4,
        },
        {
          label: 'Pending Sales',
          data: last30Days.map(date => salesByDate[date]?.pending || 0),
          borderColor: COLORS.warning,
          backgroundColor: COLORS.warning + '20',
          fill: true,
          tension: 0.4,
        }
      ]
    };
  }, [salesData]);

  const options = {
    ...defaultOptions,
    plugins: {
      ...defaultOptions.plugins,
      tooltip: {
        callbacks: {
          label: function(context) {
            return `${context.dataset.label}: ${formatCurrency(context.parsed.y)}`;
          }
        }
      }
    },
    scales: {
      ...defaultOptions.scales,
      y: {
        ...defaultOptions.scales.y,
        ticks: {
          callback: function(value) {
            return formatCurrency(value);
          }
        }
      }
    }
  };

  return <Line data={processedData} options={options} />;
}

export function OrderStatusChart({ orders = [] }) {
  const statusData = React.useMemo(() => {
    const statusCounts = orders.reduce((acc, order) => {
      const status = order.status || 'UNKNOWN';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});

    const statusLabels = {
      'PENDING': 'Pending',
      'CONFIRMED': 'Confirmed',
      'PROCESSING': 'Processing',
      'SHIPPED': 'Shipped',
      'DELIVERED': 'Delivered',
      'CANCELLED': 'Cancelled'
    };

    return {
      labels: Object.keys(statusCounts).map(status => statusLabels[status] || status),
      datasets: [{
        data: Object.values(statusCounts),
        backgroundColor: CHART_COLORS,
        borderWidth: 2,
        borderColor: '#ffffff'
      }]
    };
  }, [orders]);

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = ((context.parsed / total) * 100).toFixed(1);
            return `${context.label}: ${context.parsed} (${percentage}%)`;
          }
        }
      }
    }
  };

  return <Doughnut data={statusData} options={options} />;
}

export function TopProductsChart({ products = [] }) {
  const topProducts = React.useMemo(() => {
    // Sort products by wishlist_count and take top 10
    const sorted = [...products]
      .sort((a, b) => (b.wishlist_count || 0) - (a.wishlist_count || 0))
      .slice(0, 10);

    return {
      labels: sorted.map(product => 
        product.name.length > 20 
          ? product.name.substring(0, 20) + '...' 
          : product.name
      ),
      datasets: [{
        label: 'Wishlist Count',
        data: sorted.map(product => product.wishlist_count || 0),
        backgroundColor: COLORS.primary + '80',
        borderColor: COLORS.primary,
        borderWidth: 1
      }]
    };
  }, [products]);

  const options = {
    ...defaultOptions,
    indexAxis: 'y',
    plugins: {
      ...defaultOptions.plugins,
      legend: {
        display: false
      }
    },
    scales: {
      x: {
        grid: {
          color: '#F3F4F6',
        },
      },
      y: {
        grid: {
          display: false,
        },
      },
    }
  };

  return <Bar data={topProducts} options={options} />;
}

export function CategoryPerformanceChart({ products = [] }) {
  const categoryData = React.useMemo(() => {
    const categoryStats = products.reduce((acc, product) => {
      const category = product.category_name || 'Uncategorized';
      if (!acc[category]) {
        acc[category] = { count: 0, totalWishlist: 0 };
      }
      acc[category].count += 1;
      acc[category].totalWishlist += product.wishlist_count || 0;
      return acc;
    }, {});

    return {
      labels: Object.keys(categoryStats),
      datasets: [{
        data: Object.values(categoryStats).map(stat => stat.totalWishlist),
        backgroundColor: CHART_COLORS,
        borderWidth: 2,
        borderColor: '#ffffff'
      }]
    };
  }, [products]);

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = total > 0 ? ((context.parsed / total) * 100).toFixed(1) : 0;
            return `${context.label}: ${context.parsed} wishlists (${percentage}%)`;
          }
        }
      }
    }
  };

  return <Pie data={categoryData} options={options} />;
}

export function RevenueComparisonChart({ orders = [] }) {
  const revenueData = React.useMemo(() => {
    const last12Months = Array.from({ length: 12 }, (_, i) => {
      const date = new Date();
      date.setMonth(date.getMonth() - (11 - i));
      return {
        month: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        key: `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
      };
    });

    const revenueByMonth = orders.reduce((acc, order) => {
      const orderDate = new Date(order.created_at);
      const monthKey = `${orderDate.getFullYear()}-${String(orderDate.getMonth() + 1).padStart(2, '0')}`;
      
      if (!acc[monthKey]) {
        acc[monthKey] = 0;
      }
      
      if (order.status === 'CONFIRMED' || order.status === 'DELIVERED') {
        acc[monthKey] += order.total_amount || 0;
      }
      
      return acc;
    }, {});

    return {
      labels: last12Months.map(m => m.month),
      datasets: [{
        label: 'Monthly Revenue',
        data: last12Months.map(m => revenueByMonth[m.key] || 0),
        backgroundColor: COLORS.primary + '20',
        borderColor: COLORS.primary,
        borderWidth: 3,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: COLORS.primary,
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
        pointRadius: 6,
      }]
    };
  }, [orders]);

  const options = {
    ...defaultOptions,
    plugins: {
      ...defaultOptions.plugins,
      tooltip: {
        callbacks: {
          label: function(context) {
            return `Revenue: ${formatCurrency(context.parsed.y)}`;
          }
        }
      }
    },
    scales: {
      ...defaultOptions.scales,
      y: {
        ...defaultOptions.scales.y,
        ticks: {
          callback: function(value) {
            return formatCurrency(value);
          }
        }
      }
    }
  };

  return <Line data={revenueData} options={options} />;
}
