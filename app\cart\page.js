"use client"

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Trash2, Plus, Minus, ShoppingCart, ChevronLeft, ArrowLeft } from "lucide-react";
import Image from "next/image";
import { useCart } from "../context/cartContext";
import { useAuth } from "../hooks/useAuth";
import { toast } from "sonner";
import Link from "next/link";
import { toTitleCase } from "../lib/utils";


export default function CartPage() {
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();
  const router = useRouter();
  const { cartItems, updateCart } = useCart();
  const [cartWithDetails, setCartWithDetails] = useState([]);
  
  const handleQuantityChange = (productId, newQuantity) => {
    if (newQuantity < 1) return;
    
    const updatedItems = cartItems.map(item =>
      item.productId === productId ? { ...item, quantity: newQuantity } : item
    );
    
    updateCart(updatedItems);
  };
  
  useEffect(() => {
    setCartWithDetails(cartItems);
    setIsLoading(false);
  }, [cartItems]);

  const handleRemoveItem = (productId) => {
    const updatedItems = cartItems.filter(item => item.productId !== productId);
    updateCart(updatedItems);
  };

  const subtotal = cartWithDetails.reduce((acc, item) => {
    return acc + (item.product.price * item.quantity);
  }, 0);
  if (isLoading) {
    return <div className="p-4">Loading...</div>;
  }
  return (
    <div className="min-h-screen bg-gray-50">

      {/* Page Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-6xl mx-auto px-4 py-4 sm:py-6">
          <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
            <button
              onClick={() => router.back()}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors self-start"
            >
              <ArrowLeft className="w-4 h-4 sm:w-5 sm:h-5" />
              <span className="text-sm font-medium">Continue Shopping</span>
            </button>
            <div className="hidden sm:block h-6 w-px bg-gray-300"></div>
            <div className="flex items-center gap-3">
              <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Shopping Cart</h1>
              <span className="bg-gray-100 text-gray-600 px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm">
                {cartItems.length} {cartItems.length === 1 ? 'item' : 'items'}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto p-4">
        {cartWithDetails.length > 0 ? (
          <div className="grid lg:grid-cols-3 gap-6 lg:gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2 space-y-3 sm:space-y-4">
              {cartWithDetails.map((item) => (
                <div key={item.productId} className="bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 shadow-sm border border-gray-100">
                  <div className="flex gap-3 sm:gap-4">
                    <div className="w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 bg-gray-50 rounded-lg sm:rounded-xl overflow-hidden flex-shrink-0">
                      <Image
                        src={Array.isArray(item.product.image_url) ? item.product.image_url[0] : item.product.image_url}
                        alt={item.product.name}
                        width={96}
                        height={96}
                        className="w-full h-full object-contain p-1 sm:p-2"
                        priority
                        loading="eager"
                      />
                    </div>

                    <div className="flex-grow min-w-0">
                      <div className="flex justify-between items-start mb-2 sm:mb-3">
                        <h3 className="font-semibold text-gray-900 text-sm sm:text-base lg:text-lg line-clamp-2 pr-2">{toTitleCase(item.product.name)}</h3>
                        <button
                          onClick={() => handleRemoveItem(item.productId)}
                          className="text-gray-400 hover:text-red-500 transition-colors p-1 flex-shrink-0"
                        >
                          <Trash2 size={16} className="sm:w-[18px] sm:h-[18px]" />
                        </button>
                      </div>

                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                        <div className="space-y-1">
                          <p className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900">
                            Ksh. {Math.round(Number(item.product.price * item.quantity)).toLocaleString()}
                          </p>
                          <p className="text-xs sm:text-sm text-gray-500">
                            Ksh. {Math.round(Number(item.product.price)).toLocaleString()} each
                          </p>
                        </div>

                        <div className="flex items-center border border-gray-300 rounded-lg self-start sm:self-auto">
                          <button
                            onClick={() => handleQuantityChange(item.productId, item.quantity - 1)}
                            className="w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center text-gray-600 hover:bg-gray-50 rounded-l-lg transition-colors"
                          >
                            <Minus size={14} className="sm:w-4 sm:h-4" />
                          </button>
                          <span className="w-10 h-8 sm:w-12 sm:h-10 flex items-center justify-center border-x border-gray-300 font-medium text-sm sm:text-base">
                            {item.quantity}
                          </span>
                          <button
                            onClick={() => handleQuantityChange(item.productId, item.quantity + 1)}
                            className="w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center text-gray-600 hover:bg-gray-50 rounded-r-lg transition-colors"
                          >
                            <Plus size={14} className="sm:w-4 sm:h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 shadow-sm border border-gray-100 lg:sticky lg:top-24">
                <h2 className="text-lg sm:text-xl font-semibold text-gray-900 mb-4 sm:mb-6">Order Summary</h2>

                <div className="space-y-3 sm:space-y-4 mb-4 sm:mb-6">
                  <div className="flex justify-between text-gray-600 text-sm sm:text-base">
                    <span>Subtotal ({cartItems.length} items)</span>
                    <span>Ksh. {subtotal.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-gray-600 text-sm sm:text-base">
                    <span>Delivery</span>
                    <span className="text-gray-600">Calculated at checkout</span>
                  </div>
                  <div className="border-t border-gray-200 pt-4">
                    <div className="flex justify-between text-lg font-bold text-gray-900">
                      <span>Total</span>
                      <span>Ksh. {subtotal.toLocaleString()}</span>
                    </div>
                  </div>
                </div>

                <button
                  onClick={() => router.push("/checkout")}
                  className="w-full h-12 bg-primarycolor text-white hover:bg-primarycolor-600 rounded-xl font-medium transition-colors mb-4"
                >
                  Proceed to Checkout
                </button>

                <div className="space-y-2 text-sm text-gray-500">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Fast delivery available</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span>Secure payment</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span>Easy returns</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-16">
            <div className="max-w-md mx-auto text-center">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <ShoppingCart className="w-12 h-12 text-gray-400" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Your cart is empty</h2>
              <p className="text-gray-600 mb-8">
                Looks like you haven't added anything to your cart yet. Start shopping to fill it up!
              </p>
              <Link
                href="/"
                className="inline-flex items-center gap-2 bg-primarycolor text-white px-6 py-3 rounded-xl hover:bg-primarycolor-600 transition-colors font-medium"
              >
                Start Shopping
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );}
