"use client";

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { User, Mail, Phone, MapPin, Camera, Save, AlertCircle } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { useFormSubmission } from '../../hooks/useValidation';
import { profileUpdateSchema } from '../../lib/validations';
import Button from '../ui/Button';
import Input, { FormGroup, Textarea } from '../ui/Input';
import Card from '../ui/Card';
import { cn, getInitials } from '../../lib/utils';
import { toast } from 'sonner';

export default function ProfileForm() {
  const { userDetails, updateProfile, isLoading } = useAuth();
  const [avatarPreview, setAvatarPreview] = useState(null);
  const [avatarFile, setAvatarFile] = useState(null);

  // Form handling with validation
  const {
    data,
    errors,
    isValid,
    updateField,
    handleSubmit,
    isSubmitting,
    submitError,
    clearSubmitError,
    reset
  } = useFormSubmission(profileUpdateSchema, async (formData) => {
    try {
      // Handle avatar upload if present
      let avatarUrl = userDetails?.avatar_url;
      if (avatarFile) {
        // TODO: Implement avatar upload to storage
        // avatarUrl = await uploadAvatar(avatarFile);
      }

      await updateProfile({
        ...formData,
        avatar_url: avatarUrl
      });

      toast.success('Profile updated successfully!');
    } catch (error) {
      throw new Error(error.message || 'Failed to update profile');
    }
  });

  // Initialize form with user data
  useEffect(() => {
    if (userDetails) {
      reset({
        name: userDetails.name || '',
        email: userDetails.email || '',
        phone: userDetails.phone || '',
        address: userDetails.address || '',
        city: userDetails.city || '',
        country: userDetails.country || 'Kenya',
      });
    }
  }, [userDetails, reset]);

  const handleAvatarChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type and size
      if (!file.type.startsWith('image/')) {
        toast.error('Please select an image file');
        return;
      }
      
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast.error('Image size must be less than 5MB');
        return;
      }

      setAvatarFile(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setAvatarPreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const removeAvatar = () => {
    setAvatarFile(null);
    setAvatarPreview(null);
  };

  return (
    <div className="space-y-6">
      {/* Profile Picture Section */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Profile Picture</h2>
        
        <div className="flex items-center gap-6">
          {/* Avatar Display */}
          <div className="relative">
            <div className="w-24 h-24 bg-primarycolor rounded-full flex items-center justify-center text-white text-2xl font-bold overflow-hidden">
              {avatarPreview || userDetails?.avatar_url ? (
                <Image
                  src={avatarPreview || userDetails.avatar_url}
                  alt="Profile"
                  width={96}
                  height={96}
                  className="w-full h-full object-cover"
                />
              ) : (
                getInitials(userDetails?.name || 'User')
              )}
            </div>
            
            {/* Camera Icon Overlay */}
            <label className="absolute bottom-0 right-0 w-8 h-8 bg-primarycolor rounded-full flex items-center justify-center cursor-pointer hover:bg-primarycolor-700 transition-colors">
              <Camera className="w-4 h-4 text-white" />
              <input
                type="file"
                accept="image/*"
                onChange={handleAvatarChange}
                className="hidden"
              />
            </label>
          </div>

          {/* Avatar Actions */}
          <div className="flex-1">
            <h3 className="font-medium text-gray-900 mb-2">Change Profile Picture</h3>
            <p className="text-sm text-gray-600 mb-4">
              Upload a new profile picture. Recommended size: 400x400px. Max size: 5MB.
            </p>
            
            <div className="flex gap-3">
              <label>
                <Button variant="outline" size="sm">
                  Upload New Photo
                </Button>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleAvatarChange}
                  className="hidden"
                />
              </label>
              
              {(avatarPreview || userDetails?.avatar_url) && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={removeAvatar}
                  className="text-red-600 hover:text-red-700"
                >
                  Remove
                </Button>
              )}
            </div>
          </div>
        </div>
      </Card>

      {/* Personal Information */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Personal Information</h2>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Name */}
            <FormGroup
              label="Full Name"
              error={errors?.name}
              required
            >
              <Input
                type="text"
                placeholder="Enter your full name"
                value={data.name || ''}
                onChange={(e) => updateField('name', e.target.value)}
                leftIcon={<User className="w-5 h-5" />}
                error={!!errors?.name}
                disabled={isSubmitting}
              />
            </FormGroup>

            {/* Email */}
            <FormGroup
              label="Email Address"
              error={errors?.email}
              required
            >
              <Input
                type="email"
                placeholder="Enter your email"
                value={data.email || ''}
                onChange={(e) => updateField('email', e.target.value)}
                leftIcon={<Mail className="w-5 h-5" />}
                error={!!errors?.email}
                disabled={isSubmitting}
              />
            </FormGroup>

            {/* Phone */}
            <FormGroup
              label="Phone Number"
              error={errors?.phone}
              hint="Format: +254XXXXXXXXX or 07XXXXXXXX"
            >
              <Input
                type="tel"
                placeholder="Enter your phone number"
                value={data.phone || ''}
                onChange={(e) => updateField('phone', e.target.value)}
                leftIcon={<Phone className="w-5 h-5" />}
                error={!!errors?.phone}
                disabled={isSubmitting}
              />
            </FormGroup>

            {/* City */}
            <FormGroup
              label="City"
              error={errors?.city}
            >
              <Input
                type="text"
                placeholder="Enter your city"
                value={data.city || ''}
                onChange={(e) => updateField('city', e.target.value)}
                leftIcon={<MapPin className="w-5 h-5" />}
                error={!!errors?.city}
                disabled={isSubmitting}
              />
            </FormGroup>
          </div>

          {/* Address */}
          <FormGroup
            label="Address"
            error={errors?.address}
          >
            <Textarea
              placeholder="Enter your full address"
              value={data.address || ''}
              onChange={(e) => updateField('address', e.target.value)}
              error={!!errors?.address}
              disabled={isSubmitting}
              rows={3}
            />
          </FormGroup>

          {/* Country */}
          <FormGroup
            label="Country"
            error={errors?.country}
          >
            <Input
              type="text"
              placeholder="Enter your country"
              value={data.country || ''}
              onChange={(e) => updateField('country', e.target.value)}
              error={!!errors?.country}
              disabled={isSubmitting}
            />
          </FormGroup>

          {/* Submit Error */}
          {submitError && (
            <div className="flex items-center gap-2 p-3 bg-error-50 border border-error-200 rounded-lg text-error-700">
              <AlertCircle className="w-5 h-5 flex-shrink-0" />
              <span className="text-sm">{submitError}</span>
              <button
                type="button"
                onClick={clearSubmitError}
                className="ml-auto text-error-500 hover:text-error-700"
              >
                ×
              </button>
            </div>
          )}

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button
              type="submit"
              loading={isSubmitting || isLoading}
              disabled={!isValid}
              leftIcon={<Save className="w-5 h-5" />}
              size="lg"
            >
              Save Changes
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
}
