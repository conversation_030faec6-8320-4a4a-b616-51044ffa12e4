import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextResponse } from 'next/server'

export async function middleware(request) {
  console.log('\n--- Middleware Execution ---')
  console.log('Path:', request.nextUrl.pathname)
  
  const res = NextResponse.next()
  const supabase = createMiddlewareClient(
    { req: request, res },
    {
      cookieOptions: {
        name: 'sb-auth-token',
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        path: '/'
      }
    }
  )

  try {
    const { data: { session }, error } = await supabase.auth.getSession()
    console.log('Session Status:', !!session)
    
    if (!session && request.nextUrl.pathname.startsWith('/admin')) {
      console.log('No session - redirecting to login')
      return NextResponse.redirect(new URL('/auth/login', request.url))
    }

    if (session) {
      const { data: userData } = await supabase
        .from('users')
        .select('role')
        .eq('id', session.user.id)
        .single()

      console.log('User Role:', userData?.role)

      if (!userData || userData.role !== 'admin') {
        console.log('Not admin - redirecting to home')
        return NextResponse.redirect(new URL('/', request.url))
      }
    }

    return res
  } catch (error) {
    console.log('Middleware Error:', error)
    return NextResponse.redirect(new URL('/auth/login', request.url))
  }
}

export const config = {
  matcher: ['/admin/:path*']
}

