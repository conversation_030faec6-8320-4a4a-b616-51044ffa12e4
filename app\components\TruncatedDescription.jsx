"use client";

import { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

export default function TruncatedDescription({
  description,
  maxLines = 3,
  className = "",
  showMoreText = "Show More",
  showLessText = "Show Less",
  characterLimit = 200 // Simple character-based truncation
}) {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!description) return null;

  // Check if description is long enough to need truncation
  const needsTruncation = description.length > characterLimit;

  return (
    <div className={`relative ${className}`}>
      {/* Description Text Container with Smooth Height Animation */}
      <div
        className={`relative overflow-hidden transition-all duration-700 ease-out ${
          !isExpanded && needsTruncation
            ? 'max-h-24' // Approximately 4 lines
            : 'max-h-96' // Large enough for most descriptions
        }`}
        style={{
          transition: 'max-height 0.7s cubic-bezier(0.4, 0, 0.2, 1)'
        }}
      >
        {/* Description Text */}
        <div className="text-gray-600 leading-relaxed">
          {description}
        </div>

        {/* Subtle Gradient Overlay - positioned to show some text behind button */}
        {needsTruncation && !isExpanded && (
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white/90 via-white/60 via-white/30 to-transparent pointer-events-none" />
        )}
      </div>

      {/* Show More/Less Button with better positioning */}
      {needsTruncation && (
        <div className="mt-1 flex justify-start">
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="inline-flex items-center gap-1.5 px-3 py-1.5 bg-white/80 backdrop-blur-sm hover:bg-primarycolor/10 text-primarycolor hover:text-primarycolor-700 font-medium text-sm rounded-lg transition-all duration-300 group border border-primarycolor/20 hover:border-primarycolor/40 shadow-sm hover:shadow-md"
          >
            <span>{isExpanded ? showLessText : showMoreText}</span>
            {isExpanded ? (
              <ChevronUp className="w-4 h-4 group-hover:-translate-y-0.5 transition-transform duration-200" />
            ) : (
              <ChevronDown className="w-4 h-4 group-hover:translate-y-0.5 transition-transform duration-200" />
            )}
          </button>
        </div>
      )}
    </div>
  );
}
