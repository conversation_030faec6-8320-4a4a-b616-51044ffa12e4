import { Suspense } from "react";
import ProfileLayout from "../../components/profile/ProfileLayout";
import SecurityPage from "../../components/profile/SecurityPage";

export default function SecurityPageRoute() {
  return (
    <Suspense fallback={<div>Loading security settings...</div>}>
      <ProfileLayout 
        title="Security" 
        subtitle="Manage your password and security settings"
      >
        <SecurityPage />
      </ProfileLayout>
    </Suspense>
  );
}
