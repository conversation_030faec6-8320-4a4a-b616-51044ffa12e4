"use client";

import React, { useState, useEffect } from 'react';
import ErrorBoundary from './ErrorBoundary';

/**
 * Async Error Boundary for handling async errors and promise rejections
 * This component catches errors that occur in async operations
 */
export default function AsyncErrorBoundary({ children, fallback, onError }) {
  const [asyncError, setAsyncError] = useState(null);

  useEffect(() => {
    // Handle unhandled promise rejections
    const handleUnhandledRejection = (event) => {
      console.error('Unhandled promise rejection:', event.reason);
      
      // Report the error
      if (onError) {
        onError(event.reason, { type: 'unhandledRejection' });
      }
      
      // Set async error to trigger error boundary
      setAsyncError(event.reason);
      
      // Prevent the default browser behavior
      event.preventDefault();
    };

    // Handle general errors
    const handleError = (event) => {
      console.error('Global error:', event.error);
      
      if (onError) {
        onError(event.error, { type: 'globalError' });
      }
      
      setAsyncError(event.error);
    };

    // Add event listeners
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleError);

    // Cleanup
    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('error', handleError);
    };
  }, [onError]);

  // If there's an async error, throw it to trigger the error boundary
  if (asyncError) {
    throw asyncError;
  }

  return (
    <ErrorBoundary 
      fallback={fallback}
      onError={(error, errorInfo) => {
        if (onError) {
          onError(error, errorInfo);
        }
      }}
    >
      {children}
    </ErrorBoundary>
  );
}

/**
 * Hook for handling async errors in components
 */
export function useAsyncError() {
  const [, setError] = useState();
  
  return (error) => {
    setError(() => {
      throw error;
    });
  };
}

/**
 * Utility function to wrap async functions with error handling
 */
export function withAsyncErrorHandling(asyncFn, onError) {
  return async (...args) => {
    try {
      return await asyncFn(...args);
    } catch (error) {
      if (onError) {
        onError(error);
      }
      throw error;
    }
  };
}

/**
 * React Query error handler
 */
export function createQueryErrorHandler(onError) {
  return (error, query) => {
    console.error('Query error:', error, query);
    
    if (onError) {
      onError(error, { 
        type: 'queryError', 
        queryKey: query.queryKey,
        queryHash: query.queryHash
      });
    }
  };
}
