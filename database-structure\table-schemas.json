{"users": {"columns": [{"name": "id", "type": "string", "sample_value": "ba1a61b1-62c2-4d9a-84e5-757da1d5b380"}, {"name": "created_at", "type": "string", "sample_value": "2024-11-08T18:32:47.61691+00:00"}, {"name": "name", "type": "string", "sample_value": "<PERSON><PERSON><PERSON>"}, {"name": "email", "type": "string", "sample_value": "<EMAIL>"}, {"name": "role", "type": "string", "sample_value": "user"}], "sample": {"id": "ba1a61b1-62c2-4d9a-84e5-757da1d5b380", "created_at": "2024-11-08T18:32:47.61691+00:00", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "user"}}, "products": {"columns": [{"name": "id", "type": "string", "sample_value": "652289c4-9e46-47bd-bc2a-9c1520038e3d"}, {"name": "created_at", "type": "string", "sample_value": "2024-10-30T10:32:32.722961+00:00"}, {"name": "name", "type": "string", "sample_value": "TEA SET"}, {"name": "price", "type": "number", "sample_value": 2700}, {"name": "description", "type": "string", "sample_value": "Tea set"}, {"name": "discount", "type": "number", "sample_value": 0}, {"name": "quantity", "type": "number", "sample_value": 10}, {"name": "wishlist_count", "type": "number", "sample_value": 0}, {"name": "category_id", "type": "string", "sample_value": "0248413e-5657-4172-ba1b-3d1df405a6ad"}, {"name": "category_name", "type": "string", "sample_value": "Kitchenware"}, {"name": "image_url", "type": "object", "sample_value": ["https://jtuzvomskxxzbdqbnvzn.supabase.co/storage/v1/object/public/product_images/7f121cec-8ac8-4810-ad10-60aa4b98376c-WhatsApp%20Image%202024-10-30%20at%2011.35.46.jpeg"]}, {"name": "updated_at", "type": "string", "sample_value": "2024-10-30T10:32:32.722961+00:00"}, {"name": "category_slug", "type": "string", "sample_value": "kitchenware"}, {"name": "is_promoted", "type": "boolean", "sample_value": false}, {"name": "promotion_start_date", "type": "object", "sample_value": null}, {"name": "promotion_end_date", "type": "object", "sample_value": null}, {"name": "promotion_type", "type": "object", "sample_value": null}], "sample": {"id": "652289c4-9e46-47bd-bc2a-9c1520038e3d", "created_at": "2024-10-30T10:32:32.722961+00:00", "name": "TEA SET", "price": 2700, "description": "Tea set", "discount": 0, "quantity": 10, "wishlist_count": 0, "category_id": "0248413e-5657-4172-ba1b-3d1df405a6ad", "category_name": "Kitchenware", "image_url": ["https://jtuzvomskxxzbdqbnvzn.supabase.co/storage/v1/object/public/product_images/7f121cec-8ac8-4810-ad10-60aa4b98376c-WhatsApp%20Image%202024-10-30%20at%2011.35.46.jpeg"], "updated_at": "2024-10-30T10:32:32.722961+00:00", "category_slug": "kitchenware", "is_promoted": false, "promotion_start_date": null, "promotion_end_date": null, "promotion_type": null}}, "categories": {"columns": [{"name": "id", "type": "string", "sample_value": "0248413e-5657-4172-ba1b-3d1df405a6ad"}, {"name": "name", "type": "string", "sample_value": "Kitchenware"}, {"name": "description", "type": "string", "sample_value": "Kitchenware Products"}, {"name": "image_url", "type": "string", "sample_value": "https://jtuzvomskxxzbdqbnvzn.supabase.co/storage/v1/object/public/product_images/41da1550-98d4-415b-9073-10306ff7c8b9-Kitchenware.jpg"}, {"name": "created_at", "type": "string", "sample_value": "2024-10-29T19:26:16.26039+00:00"}, {"name": "slug", "type": "string", "sample_value": "kitchenware"}], "sample": {"id": "0248413e-5657-4172-ba1b-3d1df405a6ad", "name": "Kitchenware", "description": "Kitchenware Products", "image_url": "https://jtuzvomskxxzbdqbnvzn.supabase.co/storage/v1/object/public/product_images/41da1550-98d4-415b-9073-10306ff7c8b9-Kitchenware.jpg", "created_at": "2024-10-29T19:26:16.26039+00:00", "slug": "kitchenware"}}, "orders": {"columns": [{"name": "id", "type": "string", "sample_value": "b00102c0-9764-4d84-95c5-c72c1b305389"}, {"name": "user_id", "type": "object", "sample_value": null}, {"name": "status", "type": "string", "sample_value": "CONFIRMED"}, {"name": "total_amount", "type": "number", "sample_value": 25000}, {"name": "mpesa_code", "type": "string", "sample_value": "PAY_ON_DELIVERY"}, {"name": "delivery_cost", "type": "number", "sample_value": 200}, {"name": "billing_details", "type": "object", "sample_value": {"email": "<EMAIL>", "phone": "076674487", "county": "Nairobi", "street": "Hnhhh", "full_name": "<PERSON><PERSON>"}}, {"name": "items", "type": "object", "sample_value": [{"price": 25000, "quantity": 1, "product_id": "08b56d0d-0e57-499c-b3fc-8b95c41048a5", "product_name": "VITRON 43 SMART TV"}]}, {"name": "created_at", "type": "string", "sample_value": "2024-11-06T06:18:19.831448+00:00"}, {"name": "updated_at", "type": "string", "sample_value": "2024-11-06T06:18:19.831448+00:00"}], "sample": {"id": "b00102c0-9764-4d84-95c5-c72c1b305389", "user_id": null, "status": "CONFIRMED", "total_amount": 25000, "mpesa_code": "PAY_ON_DELIVERY", "delivery_cost": 200, "billing_details": {"email": "<EMAIL>", "phone": "076674487", "county": "Nairobi", "street": "Hnhhh", "full_name": "<PERSON><PERSON>"}, "items": [{"price": 25000, "quantity": 1, "product_id": "08b56d0d-0e57-499c-b3fc-8b95c41048a5", "product_name": "VITRON 43 SMART TV"}], "created_at": "2024-11-06T06:18:19.831448+00:00", "updated_at": "2024-11-06T06:18:19.831448+00:00"}}, "cart": {"columns": [{"name": "id", "type": "string", "sample_value": "a19a95ae-09bc-4ff4-acbe-de646eb6e063"}, {"name": "user_id", "type": "string", "sample_value": "30d60400-5b4e-476d-9ceb-fbc40cca125a"}, {"name": "product_id", "type": "string", "sample_value": "d2fbf0c1-1a0b-439f-9009-17ff4571f350"}, {"name": "quantity", "type": "number", "sample_value": 1}, {"name": "created_at", "type": "string", "sample_value": "2025-02-27T10:38:04.877202+00:00"}, {"name": "updated_at", "type": "string", "sample_value": "2025-02-27T10:38:04.877202+00:00"}, {"name": "price_at_time", "type": "object", "sample_value": null}, {"name": "total_amount", "type": "object", "sample_value": null}], "sample": {"id": "a19a95ae-09bc-4ff4-acbe-de646eb6e063", "user_id": "30d60400-5b4e-476d-9ceb-fbc40cca125a", "product_id": "d2fbf0c1-1a0b-439f-9009-17ff4571f350", "quantity": 1, "created_at": "2025-02-27T10:38:04.877202+00:00", "updated_at": "2025-02-27T10:38:04.877202+00:00", "price_at_time": null, "total_amount": null}}, "wishlists": {"columns": [{"name": "id", "type": "string", "sample_value": "2bcbc9ce-28b2-447a-9529-4d8eac43e6bb"}, {"name": "user_id", "type": "string", "sample_value": "ba1a61b1-62c2-4d9a-84e5-757da1d5b380"}, {"name": "product_id", "type": "string", "sample_value": "a426828e-03ca-49bf-a81e-81c121a9deba"}, {"name": "created_at", "type": "string", "sample_value": "2024-11-09T06:23:17.549701+00:00"}], "sample": {"id": "2bcbc9ce-28b2-447a-9529-4d8eac43e6bb", "user_id": "ba1a61b1-62c2-4d9a-84e5-757da1d5b380", "product_id": "a426828e-03ca-49bf-a81e-81c121a9deba", "created_at": "2024-11-09T06:23:17.549701+00:00"}}, "marketing_banners": {"columns": [{"name": "id", "type": "number", "sample_value": 1}, {"name": "title", "type": "string", "sample_value": "ELECTRONICS"}, {"name": "image_url", "type": "string", "sample_value": "https://jtuzvomskxxzbdqbnvzn.supabase.co/storage/v1/object/public/product_images/7fff108f-41f8-45c7-8a1b-b38f1511ff3f-Electronics%20Product%20Ad%20-%20Made%20with%20PosterMyWall.jpg"}, {"name": "link", "type": "string", "sample_value": "https://www.sherrysmerchants/categories/electronics"}, {"name": "display_order", "type": "number", "sample_value": 1}, {"name": "is_active", "type": "boolean", "sample_value": true}, {"name": "created_at", "type": "string", "sample_value": "2024-10-30T14:50:59.776704+00:00"}], "sample": {"id": 1, "title": "ELECTRONICS", "image_url": "https://jtuzvomskxxzbdqbnvzn.supabase.co/storage/v1/object/public/product_images/7fff108f-41f8-45c7-8a1b-b38f1511ff3f-Electronics%20Product%20Ad%20-%20Made%20with%20PosterMyWall.jpg", "link": "https://www.sherrysmerchants/categories/electronics", "display_order": 1, "is_active": true, "created_at": "2024-10-30T14:50:59.776704+00:00"}}, "order_items": null}