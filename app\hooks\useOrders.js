import { supabase } from '../lib/supabase';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

/**
 * Orders-related hooks
 * Handles order operations and order management
 */

export function useOrders(userId) {
  return useQuery({
    queryKey: ['orders', userId],
    queryFn: async () => {
      if (!userId) return [];
      
      const { data, error } = await supabase
        .from('orders')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data;
    },
    enabled: !!userId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useOrder(orderId) {
  return useQuery({
    queryKey: ['order', orderId],
    queryFn: async () => {
      if (!orderId) throw new Error('Order ID is required');
      
      const { data, error } = await supabase
        .from('orders')
        .select('*')
        .eq('id', orderId)
        .single();
      
      if (error) throw error;
      return data;
    },
    enabled: !!orderId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useCreateOrder() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (orderData) => {
      const { data, error } = await supabase
        .from('orders')
        .insert([{
          user_id: orderData.user_id || null,
          status: 'PENDING',
          total_amount: orderData.total_amount,
          mpesa_code: orderData.mpesa_code,
          delivery_cost: orderData.delivery_cost,
          billing_details: orderData.billing_details,
          items: orderData.items,
          created_at: new Date().toISOString()
        }])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      if (data.user_id) {
        queryClient.invalidateQueries({ queryKey: ['orders', data.user_id] });
      }
      queryClient.invalidateQueries({ queryKey: ['orders'] }); // For admin
    },
  });
}

export function useUpdateOrderStatus() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ orderId, status, notes }) => {
      const updateData = {
        status,
        updated_at: new Date().toISOString()
      };
      
      if (notes) {
        updateData.admin_notes = notes;
      }
      
      const { data, error } = await supabase
        .from('orders')
        .update(updateData)
        .eq('id', orderId)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['order', data.id] });
      if (data.user_id) {
        queryClient.invalidateQueries({ queryKey: ['orders', data.user_id] });
      }
      queryClient.invalidateQueries({ queryKey: ['orders'] }); // For admin
    },
  });
}

// Admin hooks
export function useAllOrders(filters = {}) {
  return useQuery({
    queryKey: ['orders', 'admin', filters],
    queryFn: async () => {
      let query = supabase
        .from('orders')
        .select('*')
        .order('created_at', { ascending: false });

      // Apply filters
      if (filters.status) {
        query = query.eq('status', filters.status);
      }
      
      if (filters.dateFrom) {
        query = query.gte('created_at', filters.dateFrom);
      }
      
      if (filters.dateTo) {
        query = query.lte('created_at', filters.dateTo);
      }
      
      if (filters.minAmount) {
        query = query.gte('total_amount', filters.minAmount);
      }
      
      if (filters.maxAmount) {
        query = query.lte('total_amount', filters.maxAmount);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data;
    },
    staleTime: 1 * 60 * 1000, // 1 minute for admin data
  });
}

export function useOrderStats(dateRange = '30d') {
  return useQuery({
    queryKey: ['order-stats', dateRange],
    queryFn: async () => {
      const now = new Date();
      let startDate;
      
      switch (dateRange) {
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case '90d':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      }

      const { data: orders, error } = await supabase
        .from('orders')
        .select('*')
        .gte('created_at', startDate.toISOString());

      if (error) throw error;

      // Calculate statistics
      const totalOrders = orders.length;
      const totalRevenue = orders.reduce((sum, order) => sum + order.total_amount, 0);
      const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
      
      const statusCounts = orders.reduce((acc, order) => {
        acc[order.status] = (acc[order.status] || 0) + 1;
        return acc;
      }, {});

      const completedOrders = orders.filter(order => order.status === 'COMPLETED').length;
      const conversionRate = totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0;

      // Daily breakdown
      const dailyStats = orders.reduce((acc, order) => {
        const date = new Date(order.created_at).toISOString().split('T')[0];
        if (!acc[date]) {
          acc[date] = { orders: 0, revenue: 0 };
        }
        acc[date].orders += 1;
        acc[date].revenue += order.total_amount;
        return acc;
      }, {});

      return {
        totalOrders,
        totalRevenue,
        averageOrderValue,
        conversionRate,
        statusCounts,
        dailyStats: Object.entries(dailyStats).map(([date, stats]) => ({
          date,
          ...stats
        })).sort((a, b) => a.date.localeCompare(b.date))
      };
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useOrdersByStatus(status) {
  return useQuery({
    queryKey: ['orders', 'status', status],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('orders')
        .select('*')
        .eq('status', status)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data;
    },
    enabled: !!status,
    staleTime: 1 * 60 * 1000,
  });
}

export function useRecentOrders(limit = 10) {
  return useQuery({
    queryKey: ['orders', 'recent', limit],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('orders')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit);
      
      if (error) throw error;
      return data;
    },
    staleTime: 1 * 60 * 1000,
  });
}

// Order tracking
export function useOrderTracking(orderId) {
  return useQuery({
    queryKey: ['order-tracking', orderId],
    queryFn: async () => {
      if (!orderId) return null;

      const { data: order, error } = await supabase
        .from('orders')
        .select('*')
        .eq('id', orderId)
        .single();

      if (error) throw error;

      // Generate tracking timeline based on order status and timestamps
      const timeline = [];
      
      timeline.push({
        status: 'PENDING',
        title: 'Order Placed',
        description: 'Your order has been received and is being processed',
        timestamp: order.created_at,
        completed: true
      });

      if (order.status === 'CONFIRMED' || order.status === 'SHIPPED' || order.status === 'COMPLETED') {
        timeline.push({
          status: 'CONFIRMED',
          title: 'Order Confirmed',
          description: 'Your order has been confirmed and is being prepared',
          timestamp: order.updated_at,
          completed: true
        });
      }

      if (order.status === 'SHIPPED' || order.status === 'COMPLETED') {
        timeline.push({
          status: 'SHIPPED',
          title: 'Order Shipped',
          description: 'Your order is on its way to you',
          timestamp: order.updated_at,
          completed: true
        });
      }

      if (order.status === 'COMPLETED') {
        timeline.push({
          status: 'COMPLETED',
          title: 'Order Delivered',
          description: 'Your order has been delivered successfully',
          timestamp: order.updated_at,
          completed: true
        });
      }

      if (order.status === 'CANCELLED') {
        timeline.push({
          status: 'CANCELLED',
          title: 'Order Cancelled',
          description: 'Your order has been cancelled',
          timestamp: order.updated_at,
          completed: true,
          error: true
        });
      }

      return {
        order,
        timeline,
        currentStatus: order.status,
        estimatedDelivery: order.estimated_delivery || null
      };
    },
    enabled: !!orderId,
    staleTime: 2 * 60 * 1000,
  });
}

// Order analytics for customers
export function useCustomerOrderAnalytics(userId) {
  return useQuery({
    queryKey: ['customer-order-analytics', userId],
    queryFn: async () => {
      if (!userId) return null;

      const { data: orders, error } = await supabase
        .from('orders')
        .select('*')
        .eq('user_id', userId);

      if (error) throw error;

      const totalOrders = orders.length;
      const totalSpent = orders.reduce((sum, order) => sum + order.total_amount, 0);
      const averageOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0;
      
      const completedOrders = orders.filter(order => order.status === 'COMPLETED').length;
      const pendingOrders = orders.filter(order => order.status === 'PENDING').length;
      const cancelledOrders = orders.filter(order => order.status === 'CANCELLED').length;

      // Favorite categories based on order items
      const categoryFrequency = {};
      orders.forEach(order => {
        if (order.items && Array.isArray(order.items)) {
          order.items.forEach(item => {
            // This would need to be enhanced to get actual category data
            // For now, we'll use a placeholder
            const category = 'General'; // item.category_name
            categoryFrequency[category] = (categoryFrequency[category] || 0) + item.quantity;
          });
        }
      });

      const favoriteCategories = Object.entries(categoryFrequency)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([category, count]) => ({ category, count }));

      return {
        totalOrders,
        totalSpent,
        averageOrderValue,
        completedOrders,
        pendingOrders,
        cancelledOrders,
        favoriteCategories
      };
    },
    enabled: !!userId,
    staleTime: 5 * 60 * 1000,
  });
}
