"use client";

import React from 'react';
import { cn } from '../lib/utils';

/**
 * Enhanced Loading Spinner
 */
export function LoadingSpinner({ size = "md", className = "", color = "currentColor" }) {
  const sizeClasses = {
    xs: "w-3 h-3",
    sm: "w-4 h-4",
    md: "w-6 h-6",
    lg: "w-8 h-8",
    xl: "w-12 h-12"
  };

  return (
    <div className={cn("animate-spin", sizeClasses[size], className)}>
      <svg className="w-full h-full" fill="none" viewBox="0 0 24 24">
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke={color}
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill={color}
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    </div>
  );
}

/**
 * Pulse Loading Animation
 */
export function PulseLoader({ className = "" }) {
  return (
    <div className={cn("flex space-x-2", className)}>
      <div className="w-2 h-2 bg-primarycolor rounded-full animate-pulse" style={{ animationDelay: '0ms' }}></div>
      <div className="w-2 h-2 bg-primarycolor rounded-full animate-pulse" style={{ animationDelay: '150ms' }}></div>
      <div className="w-2 h-2 bg-primarycolor rounded-full animate-pulse" style={{ animationDelay: '300ms' }}></div>
    </div>
  );
}

/**
 * Progress Bar
 */
export function ProgressBar({ progress = 0, className = "", showPercentage = false }) {
  return (
    <div className={cn("w-full bg-gray-200 rounded-full h-2", className)}>
      <div
        className="bg-primarycolor h-2 rounded-full transition-all duration-300 ease-out"
        style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
      />
      {showPercentage && (
        <div className="text-xs text-gray-600 mt-1 text-center">
          {Math.round(progress)}%
        </div>
      )}
    </div>
  );
}

/**
 * Base Skeleton Component
 */
export function Skeleton({ className = '', width, height, rounded = false }) {
  const baseClasses = 'animate-pulse bg-gray-200';
  const roundedClass = rounded ? 'rounded-full' : 'rounded';
  const sizeClasses = width && height ? '' : 'h-4 w-full';

  const style = {};
  if (width) style.width = width;
  if (height) style.height = height;

  return (
    <div
      className={cn(baseClasses, roundedClass, sizeClasses, className)}
      style={style}
    />
  );
}

/**
 * Product Card Skeleton
 */
export function ProductCardSkeleton() {
  return (
    <div className="bg-white border rounded-lg p-4 space-y-4">
      {/* Image skeleton */}
      <Skeleton height="200px" className="w-full" />
      
      {/* Title skeleton */}
      <div className="space-y-2">
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-4 w-1/2" />
      </div>
      
      {/* Price skeleton */}
      <div className="flex justify-between items-center">
        <Skeleton className="h-6 w-20" />
        <Skeleton className="h-8 w-8" rounded />
      </div>
      
      {/* Button skeleton */}
      <Skeleton className="h-10 w-full" />
    </div>
  );
}

/**
 * Product Grid Skeleton
 */
export function ProductGridSkeleton({ count = 8 }) {
  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      {Array.from({ length: count }).map((_, index) => (
        <ProductCardSkeleton key={index} />
      ))}
    </div>
  );
}

/**
 * Product Details Skeleton
 */
export function ProductDetailsSkeleton() {
  return (
    <div className="min-h-screen bg-white">
      {/* Header skeleton */}
      <div className="border-b p-4">
        <div className="flex justify-between items-center max-w-6xl mx-auto">
          <Skeleton className="h-6 w-6" />
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-6 w-6" />
        </div>
      </div>

      <div className="max-w-6xl mx-auto p-4">
        <div className="grid md:grid-cols-2 gap-8">
          {/* Image skeleton */}
          <div className="space-y-4">
            <Skeleton height="400px" className="w-full" />
            <div className="flex gap-2">
              {Array.from({ length: 4 }).map((_, i) => (
                <Skeleton key={i} height="80px" width="80px" />
              ))}
            </div>
          </div>

          {/* Details skeleton */}
          <div className="space-y-6">
            <div className="space-y-2">
              <Skeleton className="h-8 w-3/4" />
              <Skeleton className="h-6 w-1/2" />
            </div>
            
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>
            
            <div className="flex gap-4">
              <Skeleton className="h-12 w-24" />
              <Skeleton className="h-12 flex-1" />
            </div>
            
            <Skeleton className="h-12 w-full" />
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Cart Item Skeleton
 */
export function CartItemSkeleton() {
  return (
    <div className="flex gap-4 p-4 bg-white border">
      <Skeleton height="120px" width="120px" />
      <div className="flex-grow space-y-2">
        <div className="flex justify-between">
          <Skeleton className="h-5 w-2/3" />
          <Skeleton className="h-5 w-5" />
        </div>
        <Skeleton className="h-4 w-1/3" />
        <div className="flex justify-between items-center mt-4">
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-8" />
            <Skeleton className="h-6 w-8" />
            <Skeleton className="h-8 w-8" />
          </div>
          <Skeleton className="h-6 w-16" />
        </div>
      </div>
    </div>
  );
}

/**
 * Cart Page Skeleton
 */
export function CartPageSkeleton() {
  return (
    <div className="min-h-screen bg-white">
      {/* Header skeleton */}
      <div className="border-b p-4">
        <div className="flex items-center justify-between max-w-6xl mx-auto">
          <Skeleton className="h-6 w-6" />
          <Skeleton className="h-6 w-20" />
          <Skeleton className="h-6 w-6" />
        </div>
      </div>

      <div className="max-w-6xl mx-auto p-4">
        <div className="space-y-4 mb-8">
          {Array.from({ length: 3 }).map((_, index) => (
            <CartItemSkeleton key={index} />
          ))}
        </div>
        
        {/* Summary skeleton */}
        <div className="bg-white border p-6">
          <div className="space-y-4">
            <div className="flex justify-between">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-4 w-20" />
            </div>
            <div className="flex justify-between">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-16" />
            </div>
            <div className="border-t pt-4">
              <div className="flex justify-between font-bold mb-6">
                <Skeleton className="h-6 w-12" />
                <Skeleton className="h-6 w-24" />
              </div>
              <Skeleton className="h-12 w-full" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Category List Skeleton
 */
export function CategoryListSkeleton() {
  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      {Array.from({ length: 8 }).map((_, index) => (
        <div key={index} className="bg-white border rounded-lg p-4 text-center space-y-3">
          <Skeleton height="80px" width="80px" className="mx-auto" rounded />
          <Skeleton className="h-4 w-2/3 mx-auto" />
        </div>
      ))}
    </div>
  );
}

/**
 * Order History Skeleton
 */
export function OrderHistorySkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 5 }).map((_, index) => (
        <div key={index} className="bg-white border rounded-lg p-4">
          <div className="flex justify-between items-start mb-3">
            <div className="space-y-1">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-3 w-32" />
            </div>
            <Skeleton className="h-6 w-16" />
          </div>
          
          <div className="flex gap-3">
            <Skeleton height="60px" width="60px" />
            <div className="flex-grow space-y-1">
              <Skeleton className="h-4 w-2/3" />
              <Skeleton className="h-3 w-1/3" />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

/**
 * Search Results Skeleton
 */
export function SearchResultsSkeleton() {
  return (
    <div className="space-y-6">
      {/* Search header skeleton */}
      <div className="flex justify-between items-center">
        <Skeleton className="h-6 w-48" />
        <Skeleton className="h-8 w-32" />
      </div>
      
      {/* Filters skeleton */}
      <div className="flex gap-4 overflow-x-auto">
        {Array.from({ length: 5 }).map((_, index) => (
          <Skeleton key={index} className="h-8 w-20 flex-shrink-0" />
        ))}
      </div>
      
      {/* Results grid skeleton */}
      <ProductGridSkeleton count={12} />
    </div>
  );
}



/**
 * Full Page Loading Component
 */
export function PageLoading({ message = 'Loading...' }) {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50">
      <LoadingSpinner size="lg" />
      <p className="mt-4 text-gray-600">{message}</p>
    </div>
  );
}

/**
 * Button Loading State
 */
export function ButtonLoading({ children, isLoading, loadingText = 'Loading...', ...props }) {
  return (
    <button {...props} disabled={isLoading || props.disabled}>
      {isLoading ? (
        <div className="flex items-center justify-center gap-2">
          <LoadingSpinner size="sm" color="white" />
          {loadingText}
        </div>
      ) : (
        children
      )}
    </button>
  );
}

/**
 * Lazy Loading Wrapper
 */
export function LazyLoadWrapper({ children, isLoading, skeleton, error }) {
  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Error loading content</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 text-primarycolor hover:underline"
        >
          Try again
        </button>
      </div>
    );
  }

  if (isLoading) {
    return skeleton || <LoadingSpinner />;
  }

  return children;
}

/**
 * Loading Context for global loading states
 */
import { createContext, useContext, useState } from 'react';

const LoadingContext = createContext();

export function LoadingProvider({ children }) {
  const [loadingStates, setLoadingStates] = useState({});

  const setLoading = (key, isLoading) => {
    setLoadingStates(prev => ({
      ...prev,
      [key]: isLoading
    }));
  };

  const isLoading = (key) => {
    return loadingStates[key] || false;
  };

  const isAnyLoading = () => {
    return Object.values(loadingStates).some(loading => loading);
  };

  return (
    <LoadingContext.Provider value={{
      setLoading,
      isLoading,
      isAnyLoading,
      loadingStates
    }}>
      {children}
    </LoadingContext.Provider>
  );
}

export function useLoading() {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
}
