"use client";
import { useState } from 'react';
import { useParams } from 'next/navigation';
import { useProductsByCategory } from '../../hooks/useQueries';
import ProductListing from '../../components/productlisting';

import Link from 'next/link';
import { ChevronRight, Package, Grid, List, Filter } from 'lucide-react';

export default function CategoryPage() {
  const [sortBy, setSortBy] = useState('featured');
  const [viewMode, setViewMode] = useState('grid');
  const { category = '' } = useParams();
  const { data: products = [], isLoading } = useProductsByCategory(category);

  const getSortedProducts = () => {
    switch (sortBy) {
      case 'price-low':
        return [...products].sort((a, b) => Number(a.price) - Number(b.price));
      case 'price-high':
        return [...products].sort((a, b) => Number(b.price) - Number(a.price));
      case 'newest':
        return [...products].sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
      default:
        return products;
    }
  };

  const sortedProducts = getSortedProducts();

  if (isLoading) {
    return (
      <div className="bg-white">
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="h-64 bg-gray-200 rounded-2xl"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white">
      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Breadcrumb */}
        <div className="flex items-center text-sm mb-6 text-gray-600">
          <Link href="/" className="hover:text-primarycolor transition-colors">Home</Link>
          <ChevronRight className="w-4 h-4 mx-2" />
          <span className="capitalize text-gray-900 font-medium">{category}</span>
        </div>

        {/* Category Header */}
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 capitalize mb-2">
              {category}
            </h1>
            <p className="text-gray-600">
              {products.length} {products.length === 1 ? 'product' : 'products'} found
            </p>
          </div>

          {/* Controls */}
          <div className="flex items-center gap-4">
            {/* View Toggle */}
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'grid'
                    ? 'bg-white text-primarycolor shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Grid className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'list'
                    ? 'bg-white text-primarycolor shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <List className="w-4 h-4" />
              </button>
            </div>

            {/* Sort Options */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-primarycolor focus:border-transparent"
            >
              <option value="featured">Featured</option>
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
              <option value="newest">Newest First</option>
            </select>
          </div>
        </div>

        {/* Products Grid */}
        {sortedProducts.length > 0 ? (
          <ProductListing
            products={sortedProducts}
            defaultViewMode={viewMode}
            showFilters={false}
            showSearch={false}
            showViewToggle={false}
          />
        ) : (
          <div className="text-center py-16">
            <div className="max-w-md mx-auto">
              <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No products found</h3>
              <p className="text-gray-600 mb-6">
                We couldn't find any products in the "{category}" category.
              </p>
              <Link
                href="/"
                className="inline-flex items-center gap-2 bg-primarycolor text-white px-6 py-3 rounded-lg hover:bg-primarycolor-600 transition-colors"
              >
                Browse All Products
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
