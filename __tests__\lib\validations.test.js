import {
  loginSchema,
  signupSchema,
  productSchema,
  cartItemSchema,
  billingDetailsSchema,
  orderSchema,
  validateData,
  getFieldError,
  sanitizeString,
  sanitizeObject,
} from '../../app/lib/validations'

describe('Validation Schemas', () => {
  describe('loginSchema', () => {
    it('validates correct login data', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'password123',
        rememberMe: true,
      }
      
      const result = validateData(loginSchema, validData)
      expect(result.success).toBe(true)
      expect(result.data).toEqual(validData)
    })

    it('rejects invalid email', () => {
      const invalidData = {
        email: 'invalid-email',
        password: 'password123',
      }
      
      const result = validateData(loginSchema, invalidData)
      expect(result.success).toBe(false)
      expect(result.errors).toBeTruthy()
    })

    it('rejects short password', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: '123',
      }
      
      const result = validateData(loginSchema, invalidData)
      expect(result.success).toBe(false)
      
      const passwordError = getFieldError(result.errors, 'password')
      expect(passwordError).toBe('Password must be at least 6 characters')
    })

    it('handles optional rememberMe field', () => {
      const dataWithoutRememberMe = {
        email: '<EMAIL>',
        password: 'password123',
      }
      
      const result = validateData(loginSchema, dataWithoutRememberMe)
      expect(result.success).toBe(true)
    })
  })

  describe('signupSchema', () => {
    it('validates correct signup data', () => {
      const validData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'Password123',
        confirmPassword: 'Password123',
      }
      
      const result = validateData(signupSchema, validData)
      expect(result.success).toBe(true)
    })

    it('rejects mismatched passwords', () => {
      const invalidData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'Password123',
        confirmPassword: 'DifferentPassword',
      }
      
      const result = validateData(signupSchema, invalidData)
      expect(result.success).toBe(false)
      
      const confirmPasswordError = getFieldError(result.errors, 'confirmPassword')
      expect(confirmPasswordError).toBe("Passwords don't match")
    })

    it('rejects weak password', () => {
      const invalidData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'weakpassword',
        confirmPassword: 'weakpassword',
      }
      
      const result = validateData(signupSchema, invalidData)
      expect(result.success).toBe(false)
      
      const passwordError = getFieldError(result.errors, 'password')
      expect(passwordError).toContain('Password must contain')
    })

    it('rejects invalid name format', () => {
      const invalidData = {
        name: 'John123',
        email: '<EMAIL>',
        password: 'Password123',
        confirmPassword: 'Password123',
      }
      
      const result = validateData(signupSchema, invalidData)
      expect(result.success).toBe(false)
      
      const nameError = getFieldError(result.errors, 'name')
      expect(nameError).toBe('Name can only contain letters and spaces')
    })
  })

  describe('productSchema', () => {
    it('validates correct product data', () => {
      const validData = {
        name: 'Test Product',
        description: 'This is a test product description',
        price: 99.99,
        discount: 10,
        quantity: 50,
        category_id: '123e4567-e89b-12d3-a456-426614174000',
        category_name: 'Electronics',
        image_url: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
        is_promoted: true,
        promotion_start_date: '2024-01-01T00:00:00Z',
        promotion_end_date: '2024-12-31T23:59:59Z',
        promotion_type: 'percentage',
      }
      
      const result = validateData(productSchema, validData)
      expect(result.success).toBe(true)
    })

    it('rejects negative price', () => {
      const invalidData = {
        name: 'Test Product',
        description: 'This is a test product description',
        price: -10,
        quantity: 50,
        category_id: '123e4567-e89b-12d3-a456-426614174000',
        category_name: 'Electronics',
        image_url: ['https://example.com/image1.jpg'],
      }
      
      const result = validateData(productSchema, invalidData)
      expect(result.success).toBe(false)
      
      const priceError = getFieldError(result.errors, 'price')
      expect(priceError).toBe('Price must be positive')
    })

    it('rejects invalid image URLs', () => {
      const invalidData = {
        name: 'Test Product',
        description: 'This is a test product description',
        price: 99.99,
        quantity: 50,
        category_id: '123e4567-e89b-12d3-a456-426614174000',
        category_name: 'Electronics',
        image_url: ['not-a-valid-url'],
      }
      
      const result = validateData(productSchema, invalidData)
      expect(result.success).toBe(false)
    })

    it('applies default values correctly', () => {
      const minimalData = {
        name: 'Test Product',
        description: 'This is a test product description',
        price: 99.99,
        quantity: 50,
        category_id: '123e4567-e89b-12d3-a456-426614174000',
        category_name: 'Electronics',
        image_url: ['https://example.com/image1.jpg'],
      }
      
      const result = validateData(productSchema, minimalData)
      expect(result.success).toBe(true)
      expect(result.data.discount).toBe(0)
      expect(result.data.is_promoted).toBe(false)
    })
  })

  describe('cartItemSchema', () => {
    it('validates correct cart item data', () => {
      const validData = {
        productId: '123e4567-e89b-12d3-a456-426614174000',
        quantity: 2,
        product: {
          id: '123e4567-e89b-12d3-a456-426614174000',
          name: 'Test Product',
          price: 99.99,
          image_url: 'https://example.com/image.jpg',
          is_promoted: false,
        },
      }
      
      const result = validateData(cartItemSchema, validData)
      expect(result.success).toBe(true)
    })

    it('rejects zero quantity', () => {
      const invalidData = {
        productId: '123e4567-e89b-12d3-a456-426614174000',
        quantity: 0,
        product: {
          id: '123e4567-e89b-12d3-a456-426614174000',
          name: 'Test Product',
          price: 99.99,
          image_url: 'https://example.com/image.jpg',
        },
      }
      
      const result = validateData(cartItemSchema, invalidData)
      expect(result.success).toBe(false)
      
      const quantityError = getFieldError(result.errors, 'quantity')
      expect(quantityError).toBe('Quantity must be at least 1')
    })

    it('rejects excessive quantity', () => {
      const invalidData = {
        productId: '123e4567-e89b-12d3-a456-426614174000',
        quantity: 150,
        product: {
          id: '123e4567-e89b-12d3-a456-426614174000',
          name: 'Test Product',
          price: 99.99,
          image_url: 'https://example.com/image.jpg',
        },
      }
      
      const result = validateData(cartItemSchema, invalidData)
      expect(result.success).toBe(false)
      
      const quantityError = getFieldError(result.errors, 'quantity')
      expect(quantityError).toBe('Maximum quantity is 100')
    })
  })

  describe('billingDetailsSchema', () => {
    it('validates correct billing details', () => {
      const validData = {
        fullName: 'John Doe',
        email: '<EMAIL>',
        phone: '+254712345678',
        county: 'Nairobi',
        street: '123 Main Street, Apartment 4B',
      }
      
      const result = validateData(billingDetailsSchema, validData)
      expect(result.success).toBe(true)
    })

    it('validates Kenyan phone number formats', () => {
      const validFormats = ['+254712345678', '0712345678', '+254112345678', '0112345678']
      
      validFormats.forEach(phone => {
        const data = {
          fullName: 'John Doe',
          email: '<EMAIL>',
          phone,
          county: 'Nairobi',
          street: '123 Main Street',
        }
        
        const result = validateData(billingDetailsSchema, data)
        expect(result.success).toBe(true)
      })
    })

    it('rejects invalid phone number formats', () => {
      const invalidFormats = ['123456789', '+1234567890', '254712345678', '712345678']
      
      invalidFormats.forEach(phone => {
        const data = {
          fullName: 'John Doe',
          email: '<EMAIL>',
          phone,
          county: 'Nairobi',
          street: '123 Main Street',
        }
        
        const result = validateData(billingDetailsSchema, data)
        expect(result.success).toBe(false)
      })
    })
  })

  describe('orderSchema', () => {
    it('validates correct order data', () => {
      const validData = {
        user_id: '123e4567-e89b-12d3-a456-426614174000',
        total_amount: 199.98,
        mpesa_code: 'ABC123DEF456',
        delivery_cost: 50,
        billing_details: {
          fullName: 'John Doe',
          email: '<EMAIL>',
          phone: '+254712345678',
          county: 'Nairobi',
          street: '123 Main Street',
        },
        items: [
          {
            product_id: '123e4567-e89b-12d3-a456-426614174000',
            quantity: 2,
            price: 99.99,
            product_name: 'Test Product',
          },
        ],
      }
      
      const result = validateData(orderSchema, validData)
      expect(result.success).toBe(true)
    })

    it('rejects invalid M-Pesa code format', () => {
      const invalidData = {
        total_amount: 199.98,
        mpesa_code: 'invalid-code-with-special-chars!',
        delivery_cost: 50,
        billing_details: {
          fullName: 'John Doe',
          email: '<EMAIL>',
          phone: '+254712345678',
          county: 'Nairobi',
          street: '123 Main Street',
        },
        items: [
          {
            product_id: '123e4567-e89b-12d3-a456-426614174000',
            quantity: 1,
            price: 99.99,
            product_name: 'Test Product',
          },
        ],
      }
      
      const result = validateData(orderSchema, invalidData)
      expect(result.success).toBe(false)
      
      const mpesaError = getFieldError(result.errors, 'mpesa_code')
      expect(mpesaError).toBe('M-Pesa code can only contain uppercase letters and numbers')
    })

    it('rejects empty items array', () => {
      const invalidData = {
        total_amount: 199.98,
        mpesa_code: 'ABC123DEF456',
        delivery_cost: 50,
        billing_details: {
          fullName: 'John Doe',
          email: '<EMAIL>',
          phone: '+254712345678',
          county: 'Nairobi',
          street: '123 Main Street',
        },
        items: [],
      }
      
      const result = validateData(orderSchema, invalidData)
      expect(result.success).toBe(false)
      
      const itemsError = getFieldError(result.errors, 'items')
      expect(itemsError).toBe('Order must contain at least one item')
    })
  })
})

describe('Utility Functions', () => {
  describe('getFieldError', () => {
    it('returns error message for existing field', () => {
      const errors = [
        { path: ['name'], message: 'Name is required' },
        { path: ['email'], message: 'Invalid email' },
      ]
      
      expect(getFieldError(errors, 'name')).toBe('Name is required')
      expect(getFieldError(errors, 'email')).toBe('Invalid email')
    })

    it('returns null for non-existing field', () => {
      const errors = [
        { path: ['name'], message: 'Name is required' },
      ]
      
      expect(getFieldError(errors, 'email')).toBeNull()
    })

    it('returns null when errors is null', () => {
      expect(getFieldError(null, 'name')).toBeNull()
    })
  })

  describe('sanitizeString', () => {
    it('trims whitespace', () => {
      expect(sanitizeString('  hello world  ')).toBe('hello world')
    })

    it('removes angle brackets', () => {
      expect(sanitizeString('hello <script>alert("xss")</script> world')).toBe('hello scriptalert("xss")/script world')
    })

    it('handles non-string input', () => {
      expect(sanitizeString(123)).toBe(123)
      expect(sanitizeString(null)).toBe(null)
      expect(sanitizeString(undefined)).toBe(undefined)
    })
  })

  describe('sanitizeObject', () => {
    it('sanitizes object properties', () => {
      const testSchema = loginSchema
      const dirtyData = {
        email: '  <EMAIL>  ',
        password: 'password<script>',
      }
      
      const result = sanitizeObject(dirtyData, testSchema)
      expect(result.email).toBe('<EMAIL>')
      expect(result.password).toBe('passwordscript')
    })

    it('throws error for invalid data', () => {
      const testSchema = loginSchema
      const invalidData = {
        email: 'invalid-email',
        password: '123', // too short
      }
      
      expect(() => sanitizeObject(invalidData, testSchema)).toThrow('Validation failed')
    })
  })
})
