#!/usr/bin/env node

/**
 * Test Runner Script
 * 
 * This script provides a unified interface for running different types of tests
 * and generating reports.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class TestRunner {
  constructor() {
    this.projectRoot = process.cwd();
    this.testResults = {
      unit: null,
      integration: null,
      e2e: null,
      coverage: null
    };
  }

  /**
   * Run unit tests
   */
  async runUnitTests() {
    console.log('🧪 Running unit tests...\n');
    
    try {
      const output = execSync('npm test -- --testPathPattern="__tests__/(components|hooks|lib|utils)" --verbose', {
        encoding: 'utf8',
        stdio: 'pipe'
      });
      
      this.testResults.unit = {
        status: 'passed',
        output
      };
      
      console.log('✅ Unit tests passed\n');
      return true;
    } catch (error) {
      this.testResults.unit = {
        status: 'failed',
        output: error.stdout || error.message
      };
      
      console.log('❌ Unit tests failed\n');
      console.log(error.stdout);
      return false;
    }
  }

  /**
   * Run integration tests
   */
  async runIntegrationTests() {
    console.log('🔗 Running integration tests...\n');
    
    try {
      const output = execSync('npm test -- --testPathPattern="__tests__/integration" --verbose', {
        encoding: 'utf8',
        stdio: 'pipe'
      });
      
      this.testResults.integration = {
        status: 'passed',
        output
      };
      
      console.log('✅ Integration tests passed\n');
      return true;
    } catch (error) {
      this.testResults.integration = {
        status: 'failed',
        output: error.stdout || error.message
      };
      
      console.log('❌ Integration tests failed\n');
      console.log(error.stdout);
      return false;
    }
  }

  /**
   * Run E2E tests (placeholder - would integrate with actual E2E framework)
   */
  async runE2ETests() {
    console.log('🌐 Running E2E tests...\n');
    
    try {
      // This would run actual E2E tests with Playwright, Cypress, etc.
      console.log('📝 E2E tests would run here with your preferred framework');
      console.log('   - Playwright: npx playwright test');
      console.log('   - Cypress: npx cypress run');
      console.log('   - Puppeteer: node e2e-tests.js');
      
      this.testResults.e2e = {
        status: 'skipped',
        output: 'E2E tests not implemented yet'
      };
      
      console.log('⏭️  E2E tests skipped (not implemented)\n');
      return true;
    } catch (error) {
      this.testResults.e2e = {
        status: 'failed',
        output: error.message
      };
      
      console.log('❌ E2E tests failed\n');
      return false;
    }
  }

  /**
   * Generate test coverage report
   */
  async generateCoverage() {
    console.log('📊 Generating test coverage report...\n');
    
    try {
      const output = execSync('npm run test:coverage', {
        encoding: 'utf8',
        stdio: 'pipe'
      });
      
      this.testResults.coverage = {
        status: 'generated',
        output
      };
      
      console.log('✅ Coverage report generated\n');
      console.log('📁 Coverage report available at: coverage/lcov-report/index.html\n');
      return true;
    } catch (error) {
      this.testResults.coverage = {
        status: 'failed',
        output: error.stdout || error.message
      };
      
      console.log('❌ Coverage generation failed\n');
      console.log(error.stdout);
      return false;
    }
  }

  /**
   * Run linting
   */
  async runLinting() {
    console.log('🔍 Running linting...\n');
    
    try {
      const output = execSync('npm run lint', {
        encoding: 'utf8',
        stdio: 'pipe'
      });
      
      console.log('✅ Linting passed\n');
      return true;
    } catch (error) {
      console.log('❌ Linting failed\n');
      console.log(error.stdout);
      return false;
    }
  }

  /**
   * Generate test report
   */
  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0
      },
      results: this.testResults
    };

    // Calculate summary
    Object.values(this.testResults).forEach(result => {
      if (result) {
        report.summary.total++;
        if (result.status === 'passed') report.summary.passed++;
        else if (result.status === 'failed') report.summary.failed++;
        else if (result.status === 'skipped') report.summary.skipped++;
      }
    });

    // Write report to file
    const reportPath = path.join(this.projectRoot, 'test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    console.log('📋 Test Report Summary:');
    console.log(`   Total: ${report.summary.total}`);
    console.log(`   Passed: ${report.summary.passed}`);
    console.log(`   Failed: ${report.summary.failed}`);
    console.log(`   Skipped: ${report.summary.skipped}`);
    console.log(`\n📁 Full report saved to: ${reportPath}\n`);

    return report;
  }

  /**
   * Run all tests
   */
  async runAll() {
    console.log('🚀 Starting comprehensive test suite...\n');
    
    const startTime = Date.now();
    
    // Run linting first
    await this.runLinting();
    
    // Run tests in order
    await this.runUnitTests();
    await this.runIntegrationTests();
    await this.runE2ETests();
    await this.generateCoverage();
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    console.log(`⏱️  Total execution time: ${duration}s\n`);
    
    // Generate report
    const report = this.generateReport();
    
    // Exit with appropriate code
    const hasFailures = report.summary.failed > 0;
    process.exit(hasFailures ? 1 : 0);
  }

  /**
   * Run specific test type
   */
  async runSpecific(testType) {
    console.log(`🎯 Running ${testType} tests only...\n`);
    
    switch (testType) {
      case 'unit':
        await this.runUnitTests();
        break;
      case 'integration':
        await this.runIntegrationTests();
        break;
      case 'e2e':
        await this.runE2ETests();
        break;
      case 'coverage':
        await this.generateCoverage();
        break;
      case 'lint':
        await this.runLinting();
        break;
      default:
        console.log(`❌ Unknown test type: ${testType}`);
        console.log('Available types: unit, integration, e2e, coverage, lint');
        process.exit(1);
    }
    
    this.generateReport();
  }

  /**
   * Watch mode for development
   */
  async runWatch() {
    console.log('👀 Running tests in watch mode...\n');
    
    try {
      execSync('npm run test:watch', {
        stdio: 'inherit'
      });
    } catch (error) {
      console.log('❌ Watch mode failed');
      process.exit(1);
    }
  }
}

// CLI interface
async function main() {
  const testRunner = new TestRunner();
  const args = process.argv.slice(2);
  const command = args[0];

  switch (command) {
    case 'all':
      await testRunner.runAll();
      break;
    case 'unit':
    case 'integration':
    case 'e2e':
    case 'coverage':
    case 'lint':
      await testRunner.runSpecific(command);
      break;
    case 'watch':
      await testRunner.runWatch();
      break;
    case '--help':
    case '-h':
      console.log(`
Test Runner Usage:

  node scripts/test-runner.js [command]

Commands:
  all           Run all tests (unit, integration, e2e, coverage)
  unit          Run unit tests only
  integration   Run integration tests only
  e2e           Run E2E tests only
  coverage      Generate coverage report only
  lint          Run linting only
  watch         Run tests in watch mode
  --help, -h    Show this help message

Examples:
  node scripts/test-runner.js all
  node scripts/test-runner.js unit
  node scripts/test-runner.js watch
      `);
      break;
    default:
      console.log('❌ Invalid command. Use --help for usage information.');
      process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = TestRunner;
