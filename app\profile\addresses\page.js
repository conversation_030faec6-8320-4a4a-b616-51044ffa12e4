import { Suspense } from "react";
import ProfileLayout from "../../components/profile/ProfileLayout";
import AddressesPage from "../../components/profile/AddressesPage";

export default function AddressesPageRoute() {
  return (
    <Suspense fallback={<div>Loading addresses...</div>}>
      <ProfileLayout 
        title="Addresses" 
        subtitle="Manage your shipping and billing addresses"
      >
        <AddressesPage />
      </ProfileLayout>
    </Suspense>
  );
}
