"use client";

import React, { useState } from 'react';
import { 
  Settings, 
  Store, 
  Mail, 
  Bell, 
  Shield, 
  Palette, 
  Database,
  Key,
  Globe,
  DollarSign,
  Truck,
  Users,
  Save,
  RefreshCw,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import Card, { Card<PERSON>eader, CardTitle, CardContent } from '../../../components/ui/Card';
import Button from '../../../components/ui/Button';
import Input, { FormGroup } from '../../../components/ui/Input';
import { cn } from '../../../lib/utils';

export default function AdminSettingsPage() {
  const [activeTab, setActiveTab] = useState('general');
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState('');

  // Mock settings data (in a real app, this would come from database)
  const [settings, setSettings] = useState({
    general: {
      store_name: 'Pinchez Merchants',
      store_description: 'Your trusted online marketplace for quality products',
      store_email: '<EMAIL>',
      store_phone: '+************',
      store_address: 'Nairobi, Kenya',
      currency: 'KES',
      timezone: 'Africa/Nairobi'
    },
    notifications: {
      email_notifications: true,
      sms_notifications: true,
      order_notifications: true,
      low_stock_alerts: true,
      new_user_alerts: true,
      daily_reports: true
    },
    payment: {
      mpesa_enabled: true,
      card_payments_enabled: false,
      bank_transfer_enabled: true,
      mpesa_shortcode: '174379',
      mpesa_passkey: '***hidden***',
      min_order_amount: 100,
      max_order_amount: 100000
    },
    delivery: {
      default_delivery_cost: 250,
      free_delivery_threshold: 2000,
      express_delivery_surcharge: 200,
      delivery_tracking_enabled: true,
      delivery_sms_enabled: true
    },
    security: {
      two_factor_required: false,
      session_timeout: 30,
      password_expiry_days: 90,
      max_login_attempts: 5,
      admin_approval_required: true
    }
  });

  const handleSave = async (section) => {
    setIsSaving(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSaveMessage(`${section} settings saved successfully!`);
      setTimeout(() => setSaveMessage(''), 3000);
    } catch (error) {
      setSaveMessage('Error saving settings. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const updateSetting = (section, key, value) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }));
  };

  const tabs = [
    { id: 'general', label: 'General', icon: Store },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'payment', label: 'Payment', icon: DollarSign },
    { id: 'delivery', label: 'Delivery', icon: Truck },
    { id: 'security', label: 'Security', icon: Shield },
  ];

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Admin Settings</h1>
          <p className="text-gray-600">Configure your store settings and preferences</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            Reset to Defaults
          </Button>
          <Button onClick={() => handleSave(activeTab)} disabled={isSaving}>
            <Save className="w-4 h-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>

      {/* Save Message */}
      {saveMessage && (
        <Card className={cn(
          'p-4',
          saveMessage.includes('Error') ? 'bg-red-50 border-red-200' : 'bg-green-50 border-green-200'
        )}>
          <div className="flex items-center gap-3">
            {saveMessage.includes('Error') ? (
              <AlertTriangle className="w-5 h-5 text-red-600" />
            ) : (
              <CheckCircle className="w-5 h-5 text-green-600" />
            )}
            <span className={saveMessage.includes('Error') ? 'text-red-800' : 'text-green-800'}>
              {saveMessage}
            </span>
          </div>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar Navigation */}
        <div className="lg:col-span-1">
          <Card className="p-4">
            <nav className="space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={cn(
                      'w-full flex items-center gap-3 px-3 py-2 text-left rounded-lg transition-colors',
                      activeTab === tab.id
                        ? 'bg-primarycolor text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                    )}
                  >
                    <Icon className="w-4 h-4" />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </Card>
        </div>

        {/* Settings Content */}
        <div className="lg:col-span-3">
          {/* General Settings */}
          {activeTab === 'general' && (
            <Card className="p-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Store className="w-5 h-5" />
                  General Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormGroup label="Store Name">
                    <Input
                      type="text"
                      value={settings.general.store_name}
                      onChange={(e) => updateSetting('general', 'store_name', e.target.value)}
                    />
                  </FormGroup>
                  
                  <FormGroup label="Store Email">
                    <Input
                      type="email"
                      value={settings.general.store_email}
                      onChange={(e) => updateSetting('general', 'store_email', e.target.value)}
                    />
                  </FormGroup>
                </div>

                <FormGroup label="Store Description">
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primarycolor focus:border-transparent"
                    rows={3}
                    value={settings.general.store_description}
                    onChange={(e) => updateSetting('general', 'store_description', e.target.value)}
                  />
                </FormGroup>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormGroup label="Store Phone">
                    <Input
                      type="tel"
                      value={settings.general.store_phone}
                      onChange={(e) => updateSetting('general', 'store_phone', e.target.value)}
                    />
                  </FormGroup>
                  
                  <FormGroup label="Currency">
                    <select
                      value={settings.general.currency}
                      onChange={(e) => updateSetting('general', 'currency', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primarycolor focus:border-transparent"
                    >
                      <option value="KES">Kenyan Shilling (KES)</option>
                      <option value="USD">US Dollar (USD)</option>
                      <option value="EUR">Euro (EUR)</option>
                    </select>
                  </FormGroup>
                </div>

                <FormGroup label="Store Address">
                  <Input
                    type="text"
                    value={settings.general.store_address}
                    onChange={(e) => updateSetting('general', 'store_address', e.target.value)}
                  />
                </FormGroup>
              </CardContent>
            </Card>
          )}

          {/* Notification Settings */}
          {activeTab === 'notifications' && (
            <Card className="p-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="w-5 h-5" />
                  Notification Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {Object.entries(settings.notifications).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900 capitalize">
                        {key.replace(/_/g, ' ')}
                      </h4>
                      <p className="text-sm text-gray-600">
                        {key === 'email_notifications' && 'Receive notifications via email'}
                        {key === 'sms_notifications' && 'Receive notifications via SMS'}
                        {key === 'order_notifications' && 'Get notified about new orders'}
                        {key === 'low_stock_alerts' && 'Alert when products are low in stock'}
                        {key === 'new_user_alerts' && 'Notify when new users register'}
                        {key === 'daily_reports' && 'Receive daily sales reports'}
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={value}
                        onChange={(e) => updateSetting('notifications', key, e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primarycolor-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primarycolor"></div>
                    </label>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {/* Payment Settings */}
          {activeTab === 'payment' && (
            <Card className="p-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="w-5 h-5" />
                  Payment Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">Payment Methods</h4>
                  
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <h5 className="font-medium text-gray-900">M-Pesa Payments</h5>
                      <p className="text-sm text-gray-600">Accept payments via M-Pesa</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.payment.mpesa_enabled}
                        onChange={(e) => updateSetting('payment', 'mpesa_enabled', e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primarycolor-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primarycolor"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <h5 className="font-medium text-gray-900">Card Payments</h5>
                      <p className="text-sm text-gray-600">Accept credit/debit card payments</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.payment.card_payments_enabled}
                        onChange={(e) => updateSetting('payment', 'card_payments_enabled', e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primarycolor-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primarycolor"></div>
                    </label>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormGroup label="Minimum Order Amount (KES)">
                    <Input
                      type="number"
                      value={settings.payment.min_order_amount}
                      onChange={(e) => updateSetting('payment', 'min_order_amount', parseInt(e.target.value))}
                    />
                  </FormGroup>
                  
                  <FormGroup label="Maximum Order Amount (KES)">
                    <Input
                      type="number"
                      value={settings.payment.max_order_amount}
                      onChange={(e) => updateSetting('payment', 'max_order_amount', parseInt(e.target.value))}
                    />
                  </FormGroup>
                </div>

                {settings.payment.mpesa_enabled && (
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <h4 className="font-medium text-blue-900 mb-3">M-Pesa Configuration</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormGroup label="M-Pesa Shortcode">
                        <Input
                          type="text"
                          value={settings.payment.mpesa_shortcode}
                          onChange={(e) => updateSetting('payment', 'mpesa_shortcode', e.target.value)}
                        />
                      </FormGroup>
                      
                      <FormGroup label="M-Pesa Passkey">
                        <Input
                          type="password"
                          value={settings.payment.mpesa_passkey}
                          onChange={(e) => updateSetting('payment', 'mpesa_passkey', e.target.value)}
                        />
                      </FormGroup>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Delivery Settings */}
          {activeTab === 'delivery' && (
            <Card className="p-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Truck className="w-5 h-5" />
                  Delivery Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormGroup label="Default Delivery Cost (KES)">
                    <Input
                      type="number"
                      value={settings.delivery.default_delivery_cost}
                      onChange={(e) => updateSetting('delivery', 'default_delivery_cost', parseInt(e.target.value))}
                    />
                  </FormGroup>
                  
                  <FormGroup label="Free Delivery Threshold (KES)">
                    <Input
                      type="number"
                      value={settings.delivery.free_delivery_threshold}
                      onChange={(e) => updateSetting('delivery', 'free_delivery_threshold', parseInt(e.target.value))}
                    />
                  </FormGroup>
                </div>

                <FormGroup label="Express Delivery Surcharge (KES)">
                  <Input
                    type="number"
                    value={settings.delivery.express_delivery_surcharge}
                    onChange={(e) => updateSetting('delivery', 'express_delivery_surcharge', parseInt(e.target.value))}
                  />
                </FormGroup>

                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">Delivery Tracking</h4>
                      <p className="text-sm text-gray-600">Enable order tracking for customers</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.delivery.delivery_tracking_enabled}
                        onChange={(e) => updateSetting('delivery', 'delivery_tracking_enabled', e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primarycolor-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primarycolor"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">SMS Delivery Updates</h4>
                      <p className="text-sm text-gray-600">Send SMS notifications for delivery updates</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.delivery.delivery_sms_enabled}
                        onChange={(e) => updateSetting('delivery', 'delivery_sms_enabled', e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primarycolor-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primarycolor"></div>
                    </label>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Security Settings */}
          {activeTab === 'security' && (
            <Card className="p-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="w-5 h-5" />
                  Security Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormGroup label="Session Timeout (minutes)">
                    <Input
                      type="number"
                      value={settings.security.session_timeout}
                      onChange={(e) => updateSetting('security', 'session_timeout', parseInt(e.target.value))}
                    />
                  </FormGroup>
                  
                  <FormGroup label="Max Login Attempts">
                    <Input
                      type="number"
                      value={settings.security.max_login_attempts}
                      onChange={(e) => updateSetting('security', 'max_login_attempts', parseInt(e.target.value))}
                    />
                  </FormGroup>
                </div>

                <FormGroup label="Password Expiry (days)">
                  <Input
                    type="number"
                    value={settings.security.password_expiry_days}
                    onChange={(e) => updateSetting('security', 'password_expiry_days', parseInt(e.target.value))}
                  />
                </FormGroup>

                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">Two-Factor Authentication</h4>
                      <p className="text-sm text-gray-600">Require 2FA for admin accounts</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.security.two_factor_required}
                        onChange={(e) => updateSetting('security', 'two_factor_required', e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primarycolor-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primarycolor"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">Admin Approval Required</h4>
                      <p className="text-sm text-gray-600">New admin accounts require approval</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.security.admin_approval_required}
                        onChange={(e) => updateSetting('security', 'admin_approval_required', e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primarycolor-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after-border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primarycolor"></div>
                    </label>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
