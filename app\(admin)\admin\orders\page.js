"use client";
import { useState } from 'react';
import { useSupabase } from '../hooks/useSupabase';
import { toast } from 'sonner';
import { ClipboardIcon } from '@heroicons/react/24/outline';

export default function OrderManagement() {
  const { useOrders, useUpdateOrderStatus } = useSupabase();
  const { data: orders, isLoading } = useOrders();
  const updateStatus = useUpdateOrderStatus();

  const handleStatusUpdate = async (orderId, status) => {
    try {
      await updateStatus.mutateAsync({ orderId, status });
      toast.success("Order status updated");
    } catch (error) {
      toast.error("Error updating order status");
    }
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard!');
  };

  if (isLoading) return <div className='text-center text-primarycolor'>Loading orders...</div>;

  return (
    <div className="p-6 text-primarycolor">
      <h1 className="text-2xl font-bold mb-6">Order Management</h1>
      <div className="overflow-x-auto">
        <table className="min-w-full">
          <thead>
            <tr>
              <th>Order ID</th>
              <th>Customer</th>
              <th>Amount</th>
              <th>M-Pesa Code</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {orders?.map(order => (
              <tr key={order.id}>
                <td>
                  <div className="max-w-[150px] overflow-x-auto whitespace-nowrap scrollbar-thin scrollbar-thumb-gray-300">
                    {order.id}
                  </div>
                </td>
                <td>{order.billing_details.full_name}</td>
                <td>Ksh. {order.total_amount}</td>
                <td className="flex items-center justify-between gap-2 pr-2">
                  <span>{order.mpesa_code}</span>
                  <button
                    onClick={() => copyToClipboard(order.mpesa_code)}
                    className="p-1 hover:bg-gray-100 rounded-full ml-auto"
                  >
                    <ClipboardIcon className="h-4 w-4" />
                  </button>
                </td>

                <td>{order.status}</td>
                <td>
                  <select
                    value={order.status}
                    onChange={(e) => handleStatusUpdate(order.id, e.target.value)}
                    disabled={updateStatus.isLoading}
                    className="border p-1"
                  >
                    <option value="PENDING">Pending</option>
                    <option value="CONFIRMED">Confirmed</option>
                    <option value="SHIPPED">Shipped</option>
                    <option value="DELIVERED">Delivered</option>
                    <option value="CANCELLED">Cancelled</option>
                  </select>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
