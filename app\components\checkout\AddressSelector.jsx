"use client";

import { useState } from 'react';
import { Plus, MapPin, Check, Edit, Trash2 } from 'lucide-react';
import { toast } from 'sonner';

export default function AddressSelector({ 
  userAddresses = [], 
  selectedAddress, 
  onAddressSelect, 
  onAddNewAddress,
  user 
}) {
  const [showAddForm, setShowAddForm] = useState(false);

  if (!user) return null;

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Delivery Address</h3>
        <button
          onClick={() => setShowAddForm(true)}
          className="flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-primarycolor hover:text-primarycolor-700 bg-primarycolor/10 hover:bg-primarycolor/20 rounded-lg transition-colors"
        >
          <Plus className="w-4 h-4" />
          Add New
        </button>
      </div>

      {userAddresses.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 rounded-xl">
          <MapPin className="w-12 h-12 text-gray-300 mx-auto mb-3" />
          <h4 className="text-lg font-medium text-gray-900 mb-2">No saved addresses</h4>
          <p className="text-gray-600 mb-4">Add your first delivery address to continue</p>
          <button
            onClick={() => setShowAddForm(true)}
            className="inline-flex items-center gap-2 px-4 py-2 bg-primarycolor text-white rounded-lg hover:bg-primarycolor-700 transition-colors"
          >
            <Plus className="w-4 h-4" />
            Add Address
          </button>
        </div>
      ) : (
        <div className="grid gap-3">
          {userAddresses.map((address) => (
            <AddressCard
              key={address.id}
              address={address}
              isSelected={selectedAddress?.id === address.id}
              onSelect={() => onAddressSelect(address)}
            />
          ))}
        </div>
      )}

      {showAddForm && (
        <AddNewAddressModal
          onClose={() => setShowAddForm(false)}
          onSuccess={(newAddress) => {
            setShowAddForm(false);
            onAddNewAddress?.(newAddress);
            toast.success('Address added successfully!');
          }}
        />
      )}
    </div>
  );
}

function AddressCard({ address, isSelected, onSelect }) {
  return (
    <div
      onClick={onSelect}
      className={`relative p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
        isSelected
          ? 'border-primarycolor bg-primarycolor/5 shadow-md'
          : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
      }`}
    >
      {/* Selection Indicator */}
      {isSelected && (
        <div className="absolute top-3 right-3 w-6 h-6 bg-primarycolor rounded-full flex items-center justify-center">
          <Check className="w-4 h-4 text-white" />
        </div>
      )}

      {/* Address Content */}
      <div className="pr-8">
        <div className="flex items-center gap-2 mb-2">
          <h4 className="font-semibold text-gray-900">{address.title}</h4>
          {address.is_default && (
            <span className="px-2 py-0.5 text-xs font-medium bg-primarycolor text-white rounded-full">
              Default
            </span>
          )}
        </div>
        
        <p className="text-gray-700 font-medium mb-1">{address.full_name}</p>
        <p className="text-gray-600 text-sm mb-1">{address.phone}</p>
        <p className="text-gray-600 text-sm">
          {address.street_address}
          {address.city && `, ${address.city}`}
          {address.county && `, ${address.county}`}
          {address.postal_code && ` ${address.postal_code}`}
        </p>
      </div>

      {/* Hover Actions */}
      <div className="absolute top-3 right-10 opacity-0 group-hover:opacity-100 transition-opacity flex gap-1">
        <button
          onClick={(e) => {
            e.stopPropagation();
            // Handle edit
          }}
          className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <Edit className="w-4 h-4" />
        </button>
        <button
          onClick={(e) => {
            e.stopPropagation();
            // Handle delete
          }}
          className="p-1 text-gray-400 hover:text-red-500 transition-colors"
        >
          <Trash2 className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
}

function AddNewAddressModal({ onClose, onSuccess }) {
  // This would be a modal component for adding new addresses
  // For now, we'll implement a simple placeholder
  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold mb-4">Add New Address</h3>
        <p className="text-gray-600 mb-4">
          This will open the address management page where you can add a new address.
        </p>
        <div className="flex gap-3">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={() => {
              // Navigate to address management
              window.open('/profile/addresses', '_blank');
              onClose();
            }}
            className="flex-1 px-4 py-2 bg-primarycolor text-white rounded-lg hover:bg-primarycolor-700 transition-colors"
          >
            Manage Addresses
          </button>
        </div>
      </div>
    </div>
  );
}
