"use client";
import { useState } from 'react';
import { useProducts } from '../hooks/useQueries';

import EnhancedProductCard from '../components/EnhancedProductCard';
import Link from 'next/link';
import { ChevronRight, Package, Grid, List, Filter, Sparkles } from 'lucide-react';

export default function NewArrivalsPage() {
  const [sortBy, setSortBy] = useState('newest');
  const [viewMode, setViewMode] = useState('grid');
  const { data: allProducts = [], isLoading } = useProducts();

  // Filter new arrivals (products created in the last 3 months)
  const threeMonthsAgo = new Date();
  threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
  const products = allProducts.filter(p => new Date(p.created_at) > threeMonthsAgo);

  const getSortedProducts = () => {
    switch (sortBy) {
      case 'price-low':
        return [...products].sort((a, b) => Number(a.price) - Number(b.price));
      case 'price-high':
        return [...products].sort((a, b) => Number(b.price) - Number(a.price));
      case 'newest':
        return [...products].sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
      case 'popular':
        return [...products].sort((a, b) => (b.views || 0) - (a.views || 0));
      default:
        return products;
    }
  };

  const sortedProducts = getSortedProducts();

  if (isLoading) {
    return (
      <div className="bg-white">
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="h-64 bg-gray-200 rounded-2xl"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center gap-2 text-sm text-gray-600 mb-6">
          <Link href="/" className="hover:text-primarycolor transition-colors">
            Home
          </Link>
          <ChevronRight className="w-4 h-4" />
          <span className="text-gray-900 font-medium">New Arrivals</span>
        </nav>

        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-primarycolor-100 rounded-xl flex items-center justify-center">
              <Sparkles className="w-6 h-6 text-primarycolor" />
            </div>
            <div>
              <h1 className="text-2xl lg:text-3xl font-bold text-gray-900">New Arrivals</h1>
              <p className="text-gray-600">Latest products just for you</p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <span className="text-sm text-gray-600">{products.length} products</span>
          </div>
        </div>

        {/* Controls */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-900">Sort by:</span>
            </div>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primarycolor focus:border-transparent"
            >
              <option value="newest">Newest First</option>
              <option value="popular">Most Popular</option>
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
            </select>
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'grid' ? 'bg-primarycolor text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'list' ? 'bg-primarycolor text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              <List className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Products Grid */}
        {sortedProducts.length > 0 ? (
          <div className={`grid gap-4 sm:gap-6 ${
            viewMode === 'grid'
              ? 'grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5'
              : 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
          }`}>
            {sortedProducts.map((product) => (
              <EnhancedProductCard
                key={product.id}
                product={product}
                compact={viewMode === 'grid'}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No new arrivals</h3>
            <p className="text-gray-600">Check back soon for the latest products!</p>
          </div>
        )}
      </div>
    </div>
  );
}
