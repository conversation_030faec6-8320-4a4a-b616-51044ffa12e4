import { useState, useCallback } from 'react';
import { validateData, getFieldError } from '../lib/validations';

/**
 * Custom hook for form validation using Zod schemas
 * Provides validation state management and error handling
 */
export function useValidation(schema, initialData = {}) {
  const [data, setData] = useState(initialData);
  const [errors, setErrors] = useState(null);
  const [isValid, setIsValid] = useState(false);
  const [touched, setTouched] = useState({});

  // Validate all data
  const validate = useCallback((dataToValidate = data) => {
    const result = validateData(schema, dataToValidate);
    setErrors(result.errors);
    setIsValid(result.success);
    return result;
  }, [schema, data]);

  // Validate a single field
  const validateField = useCallback((fieldName, value) => {
    const fieldSchema = schema.shape[fieldName];
    if (!fieldSchema) return null;

    try {
      fieldSchema.parse(value);
      // Clear error for this field
      setErrors(prev => {
        if (!prev) return null;
        return prev.filter(error => !error.path.includes(fieldName));
      });
      return null;
    } catch (error) {
      const fieldError = error.errors[0];
      // Set error for this field
      setErrors(prev => {
        const filtered = prev ? prev.filter(error => !error.path.includes(fieldName)) : [];
        return [...filtered, fieldError];
      });
      return fieldError.message;
    }
  }, [schema]);

  // Update field value and validate
  const updateField = useCallback((fieldName, value) => {
    setData(prev => ({ ...prev, [fieldName]: value }));
    setTouched(prev => ({ ...prev, [fieldName]: true }));
    
    // Validate field if it has been touched
    if (touched[fieldName]) {
      validateField(fieldName, value);
    }
  }, [validateField, touched]);

  // Mark field as touched
  const touchField = useCallback((fieldName) => {
    setTouched(prev => ({ ...prev, [fieldName]: true }));
    validateField(fieldName, data[fieldName]);
  }, [validateField, data]);

  // Get error for a specific field
  const getError = useCallback((fieldName) => {
    return getFieldError(errors, fieldName);
  }, [errors]);

  // Check if field has error and is touched
  const hasError = useCallback((fieldName) => {
    return touched[fieldName] && !!getError(fieldName);
  }, [touched, getError]);

  // Reset form
  const reset = useCallback((newData = initialData) => {
    setData(newData);
    setErrors(null);
    setIsValid(false);
    setTouched({});
  }, [initialData]);

  // Set data and validate
  const setValidatedData = useCallback((newData) => {
    setData(newData);
    validate(newData);
  }, [validate]);

  return {
    data,
    errors,
    isValid,
    touched,
    validate,
    validateField,
    updateField,
    touchField,
    getError,
    hasError,
    reset,
    setData: setValidatedData
  };
}

/**
 * Hook for async validation (e.g., checking if email exists)
 */
export function useAsyncValidation() {
  const [isValidating, setIsValidating] = useState(false);
  const [asyncErrors, setAsyncErrors] = useState({});

  const validateAsync = useCallback(async (fieldName, value, validatorFn) => {
    setIsValidating(true);
    
    try {
      const isValid = await validatorFn(value);
      
      setAsyncErrors(prev => {
        const newErrors = { ...prev };
        if (isValid) {
          delete newErrors[fieldName];
        } else {
          newErrors[fieldName] = `${fieldName} is not available`;
        }
        return newErrors;
      });
      
      return isValid;
    } catch (error) {
      setAsyncErrors(prev => ({
        ...prev,
        [fieldName]: 'Validation failed. Please try again.'
      }));
      return false;
    } finally {
      setIsValidating(false);
    }
  }, []);

  const getAsyncError = useCallback((fieldName) => {
    return asyncErrors[fieldName] || null;
  }, [asyncErrors]);

  const clearAsyncError = useCallback((fieldName) => {
    setAsyncErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[fieldName];
      return newErrors;
    });
  }, []);

  return {
    isValidating,
    asyncErrors,
    validateAsync,
    getAsyncError,
    clearAsyncError
  };
}

/**
 * Hook for form submission with validation
 */
export function useFormSubmission(schema, onSubmit) {
  const validation = useValidation(schema);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState(null);

  const handleSubmit = useCallback(async (e) => {
    if (e) e.preventDefault();
    
    setSubmitError(null);
    setIsSubmitting(true);

    try {
      const result = validation.validate();
      
      if (!result.success) {
        // Mark all fields as touched to show errors
        const touchedFields = {};
        Object.keys(schema.shape).forEach(key => {
          touchedFields[key] = true;
        });
        validation.setTouched?.(touchedFields);
        return;
      }

      await onSubmit(result.data);
    } catch (error) {
      setSubmitError(error.message || 'An error occurred during submission');
    } finally {
      setIsSubmitting(false);
    }
  }, [validation, onSubmit, schema]);

  return {
    ...validation,
    isSubmitting,
    submitError,
    handleSubmit,
    clearSubmitError: () => setSubmitError(null)
  };
}

/**
 * Validation utilities
 */
export const validationUtils = {
  // Check if email is available (example async validator)
  checkEmailAvailability: async (email) => {
    // This would typically make an API call
    // For now, just simulate async behavior
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Simulate some emails being taken
    const takenEmails = ['<EMAIL>', '<EMAIL>'];
    return !takenEmails.includes(email.toLowerCase());
  },

  // Format phone number
  formatPhoneNumber: (phone) => {
    // Convert 07XXXXXXXX to +2547XXXXXXXX
    if (phone.startsWith('07') || phone.startsWith('01')) {
      return '+254' + phone.substring(1);
    }
    return phone;
  },

  // Sanitize input
  sanitizeInput: (input) => {
    if (typeof input !== 'string') return input;
    return input.trim().replace(/[<>]/g, '');
  }
};
