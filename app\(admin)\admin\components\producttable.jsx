"use client";
import { useSupabase } from '../hooks/useSupabase';

export default function ProductTable({ onEdit }) {
  const { useProducts, useDeleteProduct } = useSupabase();
  const { data: products, isLoading } = useProducts();
  const deleteProduct = useDeleteProduct();

  const handleDelete = async (id) => {
    try {
      await deleteProduct.mutateAsync(id);
    } catch (error) {
      toast.error("Failed to delete product");
    }
  };

  if (isLoading) return <div>Loading products...</div>;

  return (
    <table className="w-full table-auto text-primarycolor mt-4">
      <thead>
        <tr>
          <th>Name</th>
          <th>Category</th>
          <th>Price</th>
          <th>Discount</th>
          <th>Stock</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        {products?.map((product) => (
          <tr key={product.id}>
            <td>{product.name}</td>
            <td>{product.category_name}</td>
            <td>Ksh. {product.price}</td>
            <td>{product.discount}%</td>
            <td>{product.quantity}</td>
            <td className="flex gap-2">
              <button
                onClick={() => onEdit(product)}
                className="bg-primarycolor text-white px-3 py-1 rounded"
              >
                Edit
              </button>
              <button
                onClick={() => handleDelete(product.id)}
                disabled={deleteProduct.isLoading}
                className="bg-warningcolor text-white px-3 py-1 rounded disabled:opacity-50"
              >
                {deleteProduct.isLoading ? 'Deleting...' : 'Delete'}
              </button>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}
