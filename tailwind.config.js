/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",

        // Primary Brand Colors
        primarycolor: {
          DEFAULT: "var(--primarycolor)",
          50: "var(--primarycolor-50)",
          100: "var(--primarycolor-100)",
          200: "var(--primarycolor-200)",
          300: "var(--primarycolor-300)",
          400: "var(--primarycolor-400)",
          500: "var(--primarycolor-500)",
          600: "var(--primarycolor-600)",
          700: "var(--primarycolor-700)",
          800: "var(--primarycolor-800)",
          900: "var(--primarycolor-900)",
          950: "var(--primarycolor-950)",
        },

        // Secondary Brand Colors
        secondarycolor: {
          DEFAULT: "var(--secondarycolor)",
          50: "var(--secondarycolor-50)",
          100: "var(--secondarycolor-100)",
          200: "var(--secondarycolor-200)",
          300: "var(--secondarycolor-300)",
          400: "var(--secondarycolor-400)",
          500: "var(--secondarycolor-500)",
          600: "var(--secondarycolor-600)",
          700: "var(--secondarycolor-700)",
          800: "var(--secondarycolor-800)",
          900: "var(--secondarycolor-900)",
        },

        // Semantic Colors
        success: {
          DEFAULT: "var(--success)",
          50: "var(--success-50)",
          100: "var(--success-100)",
          500: "var(--success-500)",
          600: "var(--success-600)",
          700: "var(--success-700)",
        },

        warning: {
          DEFAULT: "var(--warning)",
          50: "var(--warning-50)",
          100: "var(--warning-100)",
          500: "var(--warning-500)",
          600: "var(--warning-600)",
          700: "var(--warning-700)",
        },

        error: {
          DEFAULT: "var(--error)",
          50: "var(--error-50)",
          100: "var(--error-100)",
          500: "var(--error-500)",
          600: "var(--error-600)",
          700: "var(--error-700)",
        },

        info: {
          DEFAULT: "var(--info)",
          50: "var(--info-50)",
          100: "var(--info-100)",
          500: "var(--info-500)",
          600: "var(--info-600)",
          700: "var(--info-700)",
        },

        // Legacy Support
        primarycolorvariant: "var(--primarycolorvariant)",
        secondaryvariant: "var(--secondaryvariant)",
        warningcolor: "var(--warningcolor)",
      },

      spacing: {
        'xs': 'var(--space-xs)',
        'sm': 'var(--space-sm)',
        'md': 'var(--space-md)',
        'lg': 'var(--space-lg)',
        'xl': 'var(--space-xl)',
        '2xl': 'var(--space-2xl)',
        '3xl': 'var(--space-3xl)',
      },

      borderRadius: {
        'sm': 'var(--radius-sm)',
        'md': 'var(--radius-md)',
        'lg': 'var(--radius-lg)',
        'xl': 'var(--radius-xl)',
        '2xl': 'var(--radius-2xl)',
        'primaryradius': 'var(--primaryradius)',
      },

      boxShadow: {
        'sm': 'var(--shadow-sm)',
        'md': 'var(--shadow-md)',
        'lg': 'var(--shadow-lg)',
        'xl': 'var(--shadow-xl)',
        '2xl': 'var(--shadow-2xl)',
      },

      fontSize: {
        'xs': ['var(--font-size-xs)', { lineHeight: 'var(--line-height-normal)' }],
        'sm': ['var(--font-size-sm)', { lineHeight: 'var(--line-height-normal)' }],
        'base': ['var(--font-size-base)', { lineHeight: 'var(--line-height-normal)' }],
        'lg': ['var(--font-size-lg)', { lineHeight: 'var(--line-height-normal)' }],
        'xl': ['var(--font-size-xl)', { lineHeight: 'var(--line-height-tight)' }],
        '2xl': ['var(--font-size-2xl)', { lineHeight: 'var(--line-height-tight)' }],
        '3xl': ['var(--font-size-3xl)', { lineHeight: 'var(--line-height-tight)' }],
        '4xl': ['var(--font-size-4xl)', { lineHeight: 'var(--line-height-tight)' }],
        '5xl': ['var(--font-size-5xl)', { lineHeight: 'var(--line-height-tight)' }],
      },

      fontFamily: {
        sans: ['SF Pro Display', 'system-ui', 'sans-serif'],
        'sf-pro-display': ['SF Pro Display', 'system-ui', 'sans-serif'],
        // Keep legacy support
        montserrat: ['SF Pro Display', 'system-ui', 'sans-serif'],
      },

      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
        'bounce-subtle': 'bounceSubtle 0.6s ease-out',
      },

      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        bounceSubtle: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-2px)' },
        },
      },
    },
  },
  plugins: [
    require('tailwind-scrollbar-hide')
  ],
};