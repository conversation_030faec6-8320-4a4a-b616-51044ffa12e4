import { Suspense } from "react";
import ProfileLayout from "../../components/profile/ProfileLayout";
import PaymentMethodsPage from "../../components/profile/PaymentMethodsPage";

export default function PaymentMethodsPageRoute() {
  return (
    <Suspense fallback={<div>Loading payment methods...</div>}>
      <ProfileLayout 
        title="Payment Methods" 
        subtitle="Manage your payment options for faster checkout"
      >
        <PaymentMethodsPage />
      </ProfileLayout>
    </Suspense>
  );
}
