# Pinchez Merchants (<PERSON><PERSON>'s Merchants)

A modern, production-ready e-commerce platform built with Next.js 14, featuring comprehensive product management, secure shopping cart, wishlist functionality, and advanced admin capabilities.

## 🚀 Features

### Core E-commerce Features
- **Product Catalog**: Advanced product browsing with category filtering and search
- **Shopping Cart**: Persistent cart with real-time synchronization
- **Wishlist**: Save and manage favorite products
- **Secure Checkout**: Complete order processing with M-Pesa integration
- **Order Tracking**: Real-time order status updates
- **User Profiles**: Account management and order history

### Advanced Features
- **Admin Dashboard**: Comprehensive product and order management
- **Real-time Updates**: Live inventory and order status synchronization
- **Image Optimization**: Lazy loading and responsive images
- **Error Boundaries**: Graceful error handling and recovery
- **Security**: Rate limiting, CSRF protection, input validation
- **Performance**: Optimized loading states and caching
- **Testing**: Comprehensive test suite with unit, integration, and E2E tests

## 🛠 Tech Stack

- **Frontend**: Next.js 14 (App Router), React 18, TypeScript
- **Backend**: Supabase (PostgreSQL, Auth, Real-time)
- **State Management**: React Context API, TanStack Query (React Query)
- **Styling**: Tailwind CSS, Lucide Icons
- **Validation**: Zod schemas with custom hooks
- **Testing**: Jest, React Testing Library, E2E framework ready
- **Security**: Built-in rate limiting, CSRF protection, input sanitization
- **Deployment**: Vercel with environment sync tools

## 📦 Installation & Setup

### Prerequisites

- Node.js 18+
- npm or yarn
- Supabase account
- Vercel CLI (optional, for environment sync)

### Quick Start

1. **Clone and install**:
```bash
git clone <repository-url>
cd pinchez-merchants
npm install
```

2. **Environment Setup**:
```bash
# Option 1: Manual setup
cp .env.example .env.local

# Option 2: Sync from Vercel (if deployed)
npm run sync-env:dev
```

3. **Configure environment variables**:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

4. **Start development server**:
```bash
npm run dev
```

### Environment Management

The project includes advanced environment management tools:

```bash
# Sync environment variables from Vercel
npm run sync-env              # Development environment
npm run sync-env:prod         # Production environment
npm run sync-env:preview      # Preview environment

# Or use the setup script
chmod +x scripts/env-setup.sh
./scripts/env-setup.sh
```

## 🏗 Project Architecture

```
app/
├── components/             # UI components with loading states
│   ├── ErrorBoundary.jsx  # Error handling components
│   ├── LoadingStates.jsx  # Skeleton loaders and spinners
│   ├── OptimizedImage.jsx # Image optimization component
│   └── ...
├── context/               # React Context providers
├── hooks/                 # Custom hooks (split by domain)
│   ├── useProducts.js     # Product-related operations
│   ├── useCart.js         # Cart management
│   ├── useOrders.js       # Order operations
│   ├── useWishlist.js     # Wishlist management
│   ├── useValidation.js   # Form validation
│   └── useSecurity.js     # Security utilities
├── lib/                   # Utilities and configurations
│   ├── validations.js     # Zod validation schemas
│   ├── security.js        # Security middleware and utilities
│   └── supabase.js        # Supabase configuration
├── api/                   # API routes with security middleware
└── __tests__/             # Comprehensive test suite
    ├── components/        # Component tests
    ├── hooks/             # Hook tests
    ├── lib/               # Utility tests
    ├── integration/       # Integration tests
    └── e2e/               # End-to-end test setup
```

## 🧪 Testing

The project includes a comprehensive testing strategy:

### Running Tests

```bash
# Run all tests
npm run test:all

# Run specific test types
npm run test:unit          # Unit tests
npm run test:integration   # Integration tests
npm run test:e2e          # End-to-end tests

# Development testing
npm run test:watch         # Watch mode
npm run test:coverage      # Coverage report
```

### Test Structure

- **Unit Tests**: Component and hook testing with React Testing Library
- **Integration Tests**: Feature flow testing (cart, checkout, etc.)
- **E2E Tests**: Full user journey testing (framework ready)
- **Coverage**: Minimum 70% coverage threshold

## 🔒 Security Features

### Built-in Security
- **Rate Limiting**: API endpoint protection
- **CSRF Protection**: Cross-site request forgery prevention
- **Input Validation**: Zod schema validation
- **XSS Protection**: Input sanitization
- **Security Headers**: Comprehensive security headers

### Usage Example
```javascript
// API route with security middleware
import { combineMiddleware, withRateLimit, withCSRFProtection } from '@/lib/security'

const secureHandler = combineMiddleware(
  withSecurityHeaders,
  (handler) => withRateLimit(handler, { limit: 10 }),
  withCSRFProtection
)(yourHandler)

export { secureHandler as POST }
```

## 🎨 UI/UX Features

### Loading States
- Skeleton loaders for all components
- Progressive image loading
- Optimized loading indicators
- Error boundaries with recovery options

### Responsive Design
- Mobile-first approach
- Optimized for all screen sizes
- Touch-friendly interactions
- Accessible design patterns

## 📊 Performance Optimizations

- **Image Optimization**: Lazy loading, responsive images, WebP support
- **Code Splitting**: Route-based and component-based splitting
- **Caching**: React Query with optimized cache strategies
- **Bundle Optimization**: Tree shaking and dead code elimination

## 🚀 Deployment

### Vercel Deployment

1. **Connect to Vercel**:
```bash
vercel --prod
```

2. **Environment Variables**:
Use the built-in sync tools to manage environment variables across environments.

3. **Database Setup**:
Configure your Supabase database with the provided schema.

## 🛡 Database Schema

### Core Tables
```sql
-- Users with role-based access
users (id, email, name, role, created_at)

-- Products with promotion support
products (id, name, description, price, discount, quantity, category_id, image_url, is_promoted)

-- Categories
categories (id, name, description, image_url)

-- Shopping cart with user sync
cart (id, user_id, product_id, quantity, created_at)

-- Wishlist management
wishlists (id, user_id, product_id, created_at)

-- Order management with status tracking
orders (id, user_id, status, total_amount, billing_details, items, created_at)
```

## 🔧 Development Tools

### Scripts
```bash
npm run dev              # Development server
npm run build            # Production build
npm run start            # Production server
npm run lint             # Code linting
npm run sync-env         # Environment sync
npm run test:all         # Full test suite
```

### Code Quality
- ESLint configuration
- Prettier formatting
- TypeScript support
- Git hooks (optional)

## 📈 Monitoring & Analytics

- Error tracking with error boundaries
- Performance monitoring ready
- User analytics integration points
- Admin dashboard with metrics

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Run tests: `npm run test:all`
4. Commit changes: `git commit -m 'Add amazing feature'`
5. Push to branch: `git push origin feature/amazing-feature`
6. Open a Pull Request

### Development Guidelines
- Write tests for new features
- Follow the existing code style
- Update documentation as needed
- Ensure all tests pass before submitting

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Next.js team for the amazing framework
- Supabase for the backend infrastructure
- Tailwind CSS for the styling system
- React Query team for state management
- All contributors and testers
