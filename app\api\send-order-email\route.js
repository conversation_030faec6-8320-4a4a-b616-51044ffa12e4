import { Resend } from 'resend';
import nodemailer from 'nodemailer';

const resend = new Resend(process.env.RESEND_API_KEY);

// Configure Nodemailer transporter
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: process.env.GMAIL_USER,
    pass: process.env.GMAIL_APP_PASSWORD
  }
});

export async function POST(request) {
  try {
    const { orderDetails, customerEmail } = await request.json();

    // Original Resend email
    const resendInfo = await resend.emails.send({
      from: 'Sherrys Merchants <<EMAIL>>',
      to: customerEmail,
      // cc: ['<EMAIL>', '<EMAIL>'],
      subject: `Order Confirmation #${orderDetails.id}`,
      html: `<!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <title>Order Confirmation</title>
        <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@500&display=swap" rel="stylesheet">
        <style>
          body { 
            font-family: 'Montserrat', Arial, sans-serif; 
            line-height: 1.6; 
            color: #171717;
            background-color: #ffffff;
            margin: 0;
            padding: 0;
          }
          .container { 
            max-width: 600px; 
            margin: 0 auto; 
            padding: 20px;
          }
          .header { 
            background-color: #460066; 
            padding: 30px;
            text-align: center;
            color: white;
            border-radius: 8px 8px 0 0;
          }
          .header h1 {
            margin: 0;
            font-size: 24px;
            color: #FC9AE7;
          }
          .order-details { 
            background: white;
            padding: 30px;
            border: 1px solid #eee;
            border-radius: 0 0 8px 8px;
          }
          .item { 
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
          }
          .total-section {
            margin-top: 20px;
            border-top: 2px solid #460066;
            padding-top: 20px;
          }
          .price {
            color: #460066;
            font-weight: bold;
          }
          .footer { 
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            font-size: 14px;
            color: #666;
            background-color: #f8f9fa;
            border-radius: 8px;
          }
          .button {
            background-color: #FC9AE7;
            color: #460066;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 24px;
            display: inline-block;
            margin-top: 15px;
            font-weight: bold;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Thank You for Shopping with Sherry's Merchants!</h1>
            <p>Order #${orderDetails.id}</p>
          </div>
          
          <div class="order-details">
            <h2 style="color: #460066;">Order Details</h2>
            <p><strong>Date:</strong> ${new Date().toLocaleDateString()}</p>
            <p><strong>Delivery Address:</strong><br>
            ${orderDetails.billing_details.street}<br>
            ${orderDetails.billing_details.county}, Kenya</p>
            
            <h3 style="color: #460066;">Items Ordered:</h3>
            ${orderDetails.items.map(item => `
              <div class="item">
                <p><strong>${item.product_name}</strong><br>
                Quantity: ${item.quantity}<br>
                <span class="price">KSH ${item.price.toFixed(2)}</span></p>
              </div>
            `).join('')}
            
            <div class="total-section">
              <p><strong>Subtotal:</strong> <span class="price">KSH ${orderDetails.total_amount.toFixed(2)}</span></p>
              <p><strong>Delivery Cost:</strong> <span class="price">KSH ${orderDetails.delivery_cost.toFixed(2)}</span></p>
              <p style="font-size: 18px;"><strong>Total:</strong> <span class="price">KSH ${(orderDetails.total_amount + orderDetails.delivery_cost).toFixed(2)}</span></p>
            </div>

            <div style="text-align: center; margin-top: 30px;">
              <a href="https://sherrysmerchants.com/track-order" class="button">Track Your Order</a>
            </div>
          </div>
          
          <div class="footer">
            <p>Questions about your order?<br>Contact <NAME_EMAIL></p>
            <p>© ${new Date().getFullYear()} Sherry's Merchants. All rights reserved.</p>
          </div>
        </div>
      </body>
    </html>`
    });

    // New Nodemailer admin notification
    const adminEmailContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #460066; color: white; padding: 20px; }
            .order-details { padding: 20px; }
            .item { margin: 10px 0; padding: 10px; background: #f5f5f5; }
            .total { margin-top: 20px; font-weight: bold; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>New Order Placed!</h1>
              <p>Order #${orderDetails.id}</p>
            </div>
            
            <div class="order-details">
              <h2>Customer Information</h2>
              <p>Name: ${orderDetails.billing_details.full_name}</p>
              <p>Email: ${orderDetails.billing_details.email}</p>
              <p>Phone: ${orderDetails.billing_details.phone}</p>
              <p>Address: ${orderDetails.billing_details.street}, ${orderDetails.billing_details.county}</p>
              
              <h2>Payment Details</h2>
              <p>M-PESA Code: ${orderDetails.mpesa_code}</p>
              
              <h2>Order Items</h2>
              ${orderDetails.items.map(item => `
                <div class="item">
                  <p>Product: ${item.product_name}</p>
                  <p>Quantity: ${item.quantity}</p>
                  <p>Price: KSH ${item.price.toFixed(2)}</p>
                </div>
              `).join('')}
              
              <div class="total">
                <p>Subtotal: KSH ${orderDetails.total_amount.toFixed(2)}</p>
                <p>Delivery Cost: KSH ${orderDetails.delivery_cost.toFixed(2)}</p>
                <p>Total Amount: KSH ${(orderDetails.total_amount + orderDetails.delivery_cost).toFixed(2)}</p>
              </div>
            </div>
          </div>
        </body>
      </html>
    `;

    const adminMailOptions = {
      from: process.env.GMAIL_USER,
      to: '<EMAIL>',
      subject: 'New Order has been Placed',
      html: adminEmailContent
    };

    await transporter.sendMail(adminMailOptions);

    return Response.json({ message: "Emails sent successfully" });
  } catch (error) {
    console.error(error);
    return Response.json({ error: "Failed to send email" }, { status: 500 });
  }
}
