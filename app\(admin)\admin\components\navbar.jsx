"use client";
import { useState, useEffect, useRef, useCallback } from "react";
import { Bell, Search, Menu, User } from "lucide-react";
import { useAuth } from "../../../hooks/useAuth";
import { useRouter } from "next/navigation";
import { supabase } from "../../../lib/supabase";
import { useSupabase } from "../hooks/useSupabase";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import Link from "next/link";
import EnhancedSearch from "../../../components/admin/search/EnhancedSearch";

export default function Navbar({ toggleSidebar }) {
  const { signOut, user } = useAuth();
  const { useProducts, useCategories } = useSupabase();
  const queryClient = useQueryClient();
  const router = useRouter();

  const { data: products = [] } = useProducts(0, "all");
  const { data: categories = [] } = useCategories();

  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState({
    products: [],
    categories: [],
  });
  const [notifications, setNotifications] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const notificationRef = useRef(null);
  const searchRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        notificationRef.current &&
        !notificationRef.current.contains(event.target)
      ) {
        setShowNotifications(false);
      }
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setSearchResults({ products: [], categories: [] });
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  useEffect(() => {
    const channel = supabase
      .channel("orders-channel")
      .on(
        "postgres_changes",
        {
          event: "INSERT",
          schema: "public",
          table: "orders",
        },
        (payload) => {
          const newOrder = payload.new;
          handleNewOrder(newOrder);
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  const handleNewOrder = async (newOrder) => {
    try {
      const { data: userData } = await supabase
        .from("users")
        .select("name")
        .eq("id", newOrder.user_id)
        .single();

      const notification = {
        id: crypto.randomUUID(),
        order: newOrder,
        customerName: userData?.name || "Unknown Customer",
        timestamp: new Date().toISOString(),
      };

      setNotifications((prev) => [notification, ...prev].slice(0, 50));

      if (Notification.permission === "default") {
        await Notification.requestPermission();
      }

      if (Notification.permission === "granted") {
        new Notification("New Order Received", {
          body: `Order #${newOrder.id} from ${
            userData?.name || "Unknown Customer"
          }`,
          icon: "/logo.svg",
        });
      }

      toast.success("New order received!");
    } catch (error) {
      console.error("Error processing notification:", error);
    }
  };

  const handleSearch = useCallback(
    async (e) => {
      if (e) {
        e.preventDefault();
      }

      if (!searchQuery.trim()) {
        setSearchResults({ products: [], categories: [] });
        return;
      }

      try {
        setIsSearching(true);

        const filteredProducts = products.filter(
          (product) =>
            product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            product.description
              ?.toLowerCase()
              .includes(searchQuery.toLowerCase())
        );

        const filteredCategories = categories.filter((category) =>
          category.name.toLowerCase().includes(searchQuery.toLowerCase())
        );

        setSearchResults({
          products: filteredProducts.slice(0, 5),
          categories: filteredCategories.slice(0, 5),
        });
      } finally {
        setIsSearching(false);
      }
    },
    [searchQuery, products, categories]
  );
  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      if (searchQuery) {
        handleSearch();
      }
    }, 300);

    return () => clearTimeout(delayDebounceFn);
  }, [handleSearch, searchQuery]);

  const toggleNotifications = () => {
    setShowNotifications(!showNotifications);
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push("/auth/login");
    } catch (error) {
      toast.error("Error signing out");
    }
  };

  return (
    <nav className="bg-white border-b px-4 py-3 sticky top-0 z-10">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <button
            onClick={toggleSidebar}
            className="lg:hidden text-primarycolor hover:bg-gray-50 p-2 rounded-lg"
          >
            <Menu size={24} />
          </button>

          <div className="hidden md:block w-96">
            <EnhancedSearch
              onResultSelect={(result) => {
                // Navigate to the result URL
                router.push(result.url);
              }}
            />
          </div>
        </div>

        <div className="flex items-center gap-4">
          <div className="relative" ref={notificationRef}>
            <button
              onClick={toggleNotifications}
              className="relative p-2 hover:bg-gray-50 rounded-full text-primarycolor"
            >
              <Bell size={20} className="text-primarycolor" />
              {notifications.length > 0 && (
                <span className="absolute -top-1 -right-1 bg-warningcolor text-white text-xs w-5 h-5 rounded-full flex items-center justify-center">
                  {notifications.length}
                </span>
              )}
            </button>

            {showNotifications && notifications.length > 0 && (
              <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border py-1 max-h-96 overflow-y-auto">
                {notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className="px-4 py-3 hover:bg-gray-100 border-b last:border-b-0"
                  >
                    <p className="font-medium text-secondarycolor">
                      New Order #{notification.order.id}
                    </p>
                    <p className="text-sm text-primarycolor">
                      {notification.customerName}
                    </p>
                    <p className="text-xs text-primarycolorvariant">
                      {new Date(notification.timestamp).toLocaleString()}
                    </p>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="relative">
            <button
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="flex items-center gap-2 hover:bg-gray-50 rounded-lg px-3 py-2"
            >
              <div className="w-8 h-8 bg-primarycolor rounded-full flex items-center justify-center">
                <User size={20} className="text-white" />
              </div>
              <span className="hidden md:block text-primarycolor">
                {user?.email}
              </span>
            </button>

            {showUserMenu && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border py-1">
                <button
                  onClick={handleSignOut}
                  className="w-full text-left px-4 py-2 hover:bg-gray-50 text-warningcolor"
                >
                  Sign Out
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}
