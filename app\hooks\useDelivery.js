import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '../lib/supabase';

/**
 * Delivery-related hooks for zones, costs, and delivery management
 */

// Fallback delivery costs (for backward compatibility)
const FALLBACK_DELIVERY_COSTS = {
  "Nairobi": 300,
  "Mombasa": 500,
  "Kisumu": 450,
  "Nakuru": 400,
  "Kiambu": 350,
  "Uasin G<PERSON>": 450,
  "Meru": 400,
  "Kakamega": 450,
  "Kilifi": 500,
  "Machakos": 350,
  "Nyeri": 350,
  "Kwale": 500,
  "Nyandarua": 400,
  "<PERSON><PERSON><PERSON>": 400,
  "<PERSON>ung<PERSON>": 450,
  "<PERSON><PERSON>": 400,
  "Ka<PERSON><PERSON>": 350,
  "Muranga": 350,
  "Kisii": 450,
  "<PERSON>aya": 450,
  "Bomet": 400,
  "Busia": 450,
  "Taita Taveta": 500,
  "Kirinyaga": 350,
  "<PERSON>bu": 400,
  "<PERSON><PERSON><PERSON>": 400,
  "<PERSON><PERSON>": 450,
  "Homa Bay": 450,
  "<PERSON><PERSON><PERSON>": 450,
  "<PERSON><PERSON><PERSON>": 450,
  "<PERSON><PERSON><PERSON>": 400,
  "Lai<PERSON><PERSON>": 400,
  "Trans Nzoia": 450,
  "<PERSON><PERSON>yo <PERSON>": 450,
  "Vihiga": 450,
  "Baringo": 450,
};

export function useDeliveryZones() {
  return useQuery({
    queryKey: ['delivery-zones'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('delivery_zones')
        .select('*')
        .eq('is_active', true)
        .order('name');
      
      if (error) {
        console.warn('Delivery zones not available, using fallback data:', error);
        // Return fallback data if table doesn't exist yet
        return Object.entries(FALLBACK_DELIVERY_COSTS).map(([county, cost]) => ({
          id: county.toLowerCase().replace(/\s+/g, '-'),
          name: county,
          counties: [county],
          cost,
          estimated_time: '2-5 business days',
          is_active: true
        }));
      }
      
      return data || [];
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
  });
}

export function useDeliveryCost(county) {
  const { data: deliveryZones = [] } = useDeliveryZones();
  
  return useQuery({
    queryKey: ['delivery-cost', county],
    queryFn: () => {
      if (!county) return null;
      
      // Find delivery zone that includes this county
      const zone = deliveryZones.find(zone => 
        zone.counties.some(c => 
          c.toLowerCase() === county.toLowerCase()
        )
      );
      
      if (zone) {
        return {
          cost: zone.cost,
          estimated_time: zone.estimated_time,
          zone_name: zone.name
        };
      }
      
      // Fallback to hardcoded costs
      const fallbackCost = FALLBACK_DELIVERY_COSTS[county];
      if (fallbackCost) {
        return {
          cost: fallbackCost,
          estimated_time: '2-5 business days',
          zone_name: county
        };
      }
      
      // Default cost for unknown counties
      return {
        cost: 400,
        estimated_time: '3-7 business days',
        zone_name: 'Standard Delivery'
      };
    },
    enabled: !!county && deliveryZones.length > 0,
    staleTime: 15 * 60 * 1000, // 15 minutes
  });
}

export function useCreateDeliveryZone() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (zoneData) => {
      const { data, error } = await supabase
        .from('delivery_zones')
        .insert([zoneData])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['delivery-zones'] });
    },
  });
}

export function useUpdateDeliveryZone() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, ...updateData }) => {
      const { data, error } = await supabase
        .from('delivery_zones')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['delivery-zones'] });
    },
  });
}

export function useDeleteDeliveryZone() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id) => {
      const { error } = await supabase
        .from('delivery_zones')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['delivery-zones'] });
    },
  });
}

// Utility function to get all available counties
export function useAvailableCounties() {
  const { data: deliveryZones = [] } = useDeliveryZones();
  
  return useQuery({
    queryKey: ['available-counties', deliveryZones],
    queryFn: () => {
      const counties = new Set();
      
      deliveryZones.forEach(zone => {
        zone.counties.forEach(county => {
          counties.add(county);
        });
      });
      
      // Add fallback counties if no zones available
      if (counties.size === 0) {
        Object.keys(FALLBACK_DELIVERY_COSTS).forEach(county => {
          counties.add(county);
        });
      }
      
      return Array.from(counties).sort();
    },
    enabled: true,
    staleTime: 15 * 60 * 1000,
  });
}
