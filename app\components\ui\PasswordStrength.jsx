"use client";

import React from 'react';

// Password strength calculation
export function calculatePasswordStrength(password) {
  if (!password) return { score: 0, feedback: [] };

  let score = 0;
  const feedback = [];
  const checks = {
    length: password.length >= 8,
    lowercase: /[a-z]/.test(password),
    uppercase: /[A-Z]/.test(password),
    numbers: /\d/.test(password),
    symbols: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
  };

  // Length check (most important)
  if (checks.length) {
    score += 2;
  } else {
    feedback.push('At least 8 characters');
  }

  // Character variety checks
  if (checks.lowercase) {
    score += 1;
  } else {
    feedback.push('One lowercase letter');
  }

  if (checks.uppercase) {
    score += 1;
  } else {
    feedback.push('One uppercase letter');
  }

  if (checks.numbers) {
    score += 1;
  } else {
    feedback.push('One number');
  }

  if (checks.symbols) {
    score += 1;
  } else {
    feedback.push('One special character');
  }

  // Bonus points for longer passwords
  if (password.length >= 12) score += 1;
  if (password.length >= 16) score += 1;

  return { score: Math.min(score, 5), feedback, checks };
}

export function getStrengthLevel(score) {
  if (score === 0) return { level: 'none', label: '', color: 'bg-gray-200' };
  if (score <= 2) return { level: 'weak', label: 'Weak', color: 'bg-red-500' };
  if (score <= 3) return { level: 'fair', label: 'Fair', color: 'bg-yellow-500' };
  if (score <= 4) return { level: 'good', label: 'Good', color: 'bg-blue-500' };
  return { level: 'strong', label: 'Strong', color: 'bg-green-500' };
}

export default function PasswordStrength({ password, className = '' }) {
  const { score, feedback, checks } = calculatePasswordStrength(password);
  const { level, label, color } = getStrengthLevel(score);

  if (!password) return null;

  return (
    <div className={`mt-2 ${className}`}>
      {/* Strength Bar */}
      <div className="flex gap-1 mb-2">
        {[1, 2, 3, 4, 5].map((segment) => (
          <div
            key={segment}
            className={`h-1 flex-1 rounded-full transition-colors duration-200 ${
              segment <= score ? color : 'bg-gray-200'
            }`}
          />
        ))}
      </div>

      {/* Strength Label */}
      {label && (
        <div className="flex items-center gap-2 mb-2">
          <span className={`text-xs font-medium ${
            level === 'weak' ? 'text-red-600' :
            level === 'fair' ? 'text-yellow-600' :
            level === 'good' ? 'text-blue-600' :
            level === 'strong' ? 'text-green-600' : 'text-gray-600'
          }`}>
            Password strength: {label}
          </span>
        </div>
      )}

      {/* Requirements Checklist */}
      {feedback.length > 0 && (
        <div className="space-y-1">
          <p className="text-xs text-gray-600 font-medium">Password must contain:</p>
          <ul className="space-y-1">
            <li className={`text-xs flex items-center gap-2 ${
              checks.length ? 'text-green-600' : 'text-gray-500'
            }`}>
              <svg className={`w-3 h-3 ${checks.length ? 'text-green-500' : 'text-gray-400'}`} 
                   fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              At least 8 characters
            </li>
            <li className={`text-xs flex items-center gap-2 ${
              checks.lowercase ? 'text-green-600' : 'text-gray-500'
            }`}>
              <svg className={`w-3 h-3 ${checks.lowercase ? 'text-green-500' : 'text-gray-400'}`} 
                   fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              One lowercase letter (a-z)
            </li>
            <li className={`text-xs flex items-center gap-2 ${
              checks.uppercase ? 'text-green-600' : 'text-gray-500'
            }`}>
              <svg className={`w-3 h-3 ${checks.uppercase ? 'text-green-500' : 'text-gray-400'}`} 
                   fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              One uppercase letter (A-Z)
            </li>
            <li className={`text-xs flex items-center gap-2 ${
              checks.numbers ? 'text-green-600' : 'text-gray-500'
            }`}>
              <svg className={`w-3 h-3 ${checks.numbers ? 'text-green-500' : 'text-gray-400'}`} 
                   fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              One number (0-9)
            </li>
            <li className={`text-xs flex items-center gap-2 ${
              checks.symbols ? 'text-green-600' : 'text-gray-500'
            }`}>
              <svg className={`w-3 h-3 ${checks.symbols ? 'text-green-500' : 'text-gray-400'}`} 
                   fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              One special character (!@#$%^&*)
            </li>
          </ul>
        </div>
      )}
    </div>
  );
}

// Password visibility toggle component
export function PasswordInput({ 
  value, 
  onChange, 
  placeholder = "Enter password",
  disabled = false,
  showStrength = false,
  className = "",
  ...props 
}) {
  const [showPassword, setShowPassword] = React.useState(false);

  return (
    <div className="space-y-1">
      <div className="relative">
        <input
          type={showPassword ? 'text' : 'password'}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          disabled={disabled}
          className={`block w-full px-3 py-2 pr-10 border border-gray-300 rounded-md placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primarycolor focus:border-primarycolor disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
          {...props}
        />
        <button
          type="button"
          onClick={() => setShowPassword(!showPassword)}
          className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
        >
          {showPassword ? (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
            </svg>
          ) : (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
          )}
        </button>
      </div>
      
      {showStrength && (
        <PasswordStrength password={value} />
      )}
    </div>
  );
}
