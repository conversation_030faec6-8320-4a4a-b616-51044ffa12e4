"use client";
import { useProducts, useCategories } from './hooks/useQueries';
import { useState } from 'react';
import dynamic from 'next/dynamic';
import { Search, ArrowRight, TrendingUp } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { ProductGridSkeleton, CategoryListSkeleton, LoadingSpinner } from './components/LoadingStates';
import HeroSection, { ModernCategories, PromotionalBanner } from './components/HeroSection';
import EnhancedProductCard from './components/EnhancedProductCard';
import Card, { CardHeader, CardTitle, CardContent } from './components/ui/Card';
import Button from './components/ui/Button';
import { SearchInput } from './components/ui/Input';

const ProductCarousel = dynamic(() => import('./components/productcarousel'));
const BannerCarousel = dynamic(() => import('./components/bannercarousel'));

export default function HomePage() {
  const { data: products = [], isLoading: productsLoading, error: productsError } = useProducts();
  const { data: categories = [], isLoading: categoriesLoading, error: categoriesError } = useCategories();
  const [searchTerm, setSearchTerm] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Get featured/promoted products
  const featuredProducts = products.filter(product => product.is_promoted).slice(0, 6);

  const handleInputChange = (e) => {
    const value = e.target.value;
    setSearchTerm(value);

    if (value.trim()) {
      const filteredSuggestions = products.filter(product =>
        product.name.toLowerCase().includes(value.toLowerCase())
      );
      setSuggestions(filteredSuggestions.slice(0, 5));
      setShowSuggestions(true);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  };

  const getTopDeals = () => products.filter(p => p.discount);
  const getNewArrivals = () => products.filter(p => new Date(p.created_at) > new Date('2024-01-01'));
  const getCategoryProducts = (categoryId) => products.filter(p => p.category_id === categoryId);

  return (
    <div className="min-h-screen bg-white">
      {/* Modern Hero Section */}
      <HeroSection
        featuredProducts={featuredProducts}
      />

      {/* Modern Categories */}
      <ModernCategories categories={categories} />

      {/* Banner Carousel with Modern Feel */}
      <div className="py-4 sm:py-6 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <BannerCarousel />
        </div>
      </div>



      {/* Product Carousels with Modern Cards - Original Structure */}
      <div className="bg-gray-50">
        <ProductCarousel
          title="TOP DEALS"
          products={getTopDeals()}
          category="top-deals"
          isSpecialCategory={true}
        />
      </div>

      <ProductCarousel
        title="NEW ARRIVALS"
        products={getNewArrivals()}
        category="new-arrivals"
        isSpecialCategory={true}
      />

      <div className="bg-gray-50">
        {categories.map((category, index) => (
          <div key={category.id} className={index > 0 ? "border-t border-gray-200" : ""}>
            <ProductCarousel
              title={category.name.toUpperCase()}
              products={getCategoryProducts(category.id)}
              category={category.name}
              isSpecialCategory={false}
            />
          </div>
        ))}
      </div>

      {/* WhatsApp Floating Button */}
      <div className="fixed bottom-6 right-6 flex items-center gap-3 z-50">
        <div className="bg-white px-4 py-2 rounded-lg shadow-lg hidden md:block">
          <p className="text-gray-700 font-medium">Chat with us!</p>
        </div>
        <Link
          href="https://wa.me/+254740209121"
          target="_blank"
          rel="noopener noreferrer"
          className="group relative bg-[#25D366] rounded-full p-3 shadow-lg hover:bg-[#20BA5A] transition-all cursor-pointer"
        >
          <Image
            src="/icons/whatsapp.svg"
            alt="Chat on WhatsApp"
            width={32}
            height={32}
            className="w-8 h-8"
          />
          <span className="absolute -top-10 right-0 bg-black text-white text-sm px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
            Chat Now
          </span>
        </Link>
      </div>
    </div>
  );
}
