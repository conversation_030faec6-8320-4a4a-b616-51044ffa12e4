"use client";

import React, { useState } from 'react';
import { 
  CreditCard, 
  Smartphone, 
  Building, 
  Plus, 
  Edit, 
  Trash2, 
  Star,
  Shield,
  CheckCircle
} from 'lucide-react';
import { 
  usePaymentMethods, 
  useCreatePaymentMethod, 
  useUpdatePaymentMethod, 
  useDeletePaymentMethod 
} from '../../hooks/useProfile';
import { paymentMethodSchema } from '../../lib/validations';
import Card from '../ui/Card';
import Button from '../ui/Button';
import Input, { FormGroup } from '../ui/Input';
import { cn } from '../../lib/utils';

const PAYMENT_TYPES = {
  mpesa: {
    label: 'M-Pesa',
    icon: Smartphone,
    color: 'text-green-600',
    bgColor: 'bg-green-50',
    fields: ['phone_number']
  },
  card: {
    label: 'Credit/Debit Card',
    icon: CreditCard,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50',
    fields: ['card_number', 'expiry_month', 'expiry_year', 'cardholder_name']
  },
  bank_transfer: {
    label: 'Bank Transfer',
    icon: Building,
    color: 'text-purple-600',
    bgColor: 'bg-purple-50',
    fields: ['bank_name', 'account_number', 'account_name']
  }
};

export default function PaymentMethodsPage() {
  const [showForm, setShowForm] = useState(false);
  const [editingMethod, setEditingMethod] = useState(null);
  const [selectedType, setSelectedType] = useState('mpesa');
  const [formData, setFormData] = useState({
    type: 'mpesa',
    title: '',
    details: {},
    is_default: false
  });
  const [errors, setErrors] = useState({});

  const { data: paymentMethods = [], isLoading } = usePaymentMethods();
  const createPaymentMethod = useCreatePaymentMethod();
  const updatePaymentMethod = useUpdatePaymentMethod();
  const deletePaymentMethod = useDeletePaymentMethod();

  const resetForm = () => {
    setFormData({
      type: 'mpesa',
      title: '',
      details: {},
      is_default: false
    });
    setSelectedType('mpesa');
    setErrors({});
    setEditingMethod(null);
    setShowForm(false);
  };

  const handleEdit = (method) => {
    setFormData({
      type: method.type,
      title: method.title,
      details: method.details,
      is_default: method.is_default
    });
    setSelectedType(method.type);
    setEditingMethod(method);
    setShowForm(true);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setErrors({});

    try {
      // Validate form data
      const validatedData = paymentMethodSchema.parse(formData);
      
      if (editingMethod) {
        await updatePaymentMethod.mutateAsync({ id: editingMethod.id, ...validatedData });
      } else {
        await createPaymentMethod.mutateAsync(validatedData);
      }
      
      resetForm();
    } catch (error) {
      if (error.errors) {
        const newErrors = {};
        error.errors.forEach(err => {
          newErrors[err.path[0]] = err.message;
        });
        setErrors(newErrors);
      } else {
        console.error('Payment method operation failed:', error);
      }
    }
  };

  const handleDelete = async (methodId) => {
    if (window.confirm('Are you sure you want to delete this payment method?')) {
      try {
        await deletePaymentMethod.mutateAsync(methodId);
      } catch (error) {
        console.error('Delete failed:', error);
      }
    }
  };

  const renderPaymentMethodDetails = (method) => {
    const typeConfig = PAYMENT_TYPES[method.type];
    const Icon = typeConfig.icon;

    switch (method.type) {
      case 'mpesa':
        return (
          <div className="flex items-center gap-3">
            <div className={cn('p-2 rounded-lg', typeConfig.bgColor)}>
              <Icon className={cn('w-5 h-5', typeConfig.color)} />
            </div>
            <div>
              <div className="font-medium text-gray-900">{method.title}</div>
              <div className="text-sm text-gray-600">{method.details.phone_number}</div>
            </div>
          </div>
        );
      
      case 'card':
        return (
          <div className="flex items-center gap-3">
            <div className={cn('p-2 rounded-lg', typeConfig.bgColor)}>
              <Icon className={cn('w-5 h-5', typeConfig.color)} />
            </div>
            <div>
              <div className="font-medium text-gray-900">{method.title}</div>
              <div className="text-sm text-gray-600">
                **** **** **** {method.details.card_number?.slice(-4)}
              </div>
              <div className="text-xs text-gray-500">
                Expires {method.details.expiry_month}/{method.details.expiry_year}
              </div>
            </div>
          </div>
        );
      
      case 'bank_transfer':
        return (
          <div className="flex items-center gap-3">
            <div className={cn('p-2 rounded-lg', typeConfig.bgColor)}>
              <Icon className={cn('w-5 h-5', typeConfig.color)} />
            </div>
            <div>
              <div className="font-medium text-gray-900">{method.title}</div>
              <div className="text-sm text-gray-600">{method.details.bank_name}</div>
              <div className="text-xs text-gray-500">
                {method.details.account_name} - ****{method.details.account_number?.slice(-4)}
              </div>
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  const renderFormFields = () => {
    const typeConfig = PAYMENT_TYPES[selectedType];
    
    switch (selectedType) {
      case 'mpesa':
        return (
          <FormGroup label="M-Pesa Phone Number" error={errors['details.phone_number']}>
            <Input
              type="tel"
              placeholder="e.g., +************ or **********"
              value={formData.details.phone_number || ''}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                details: { ...prev.details, phone_number: e.target.value }
              }))}
              error={!!errors['details.phone_number']}
            />
          </FormGroup>
        );
      
      case 'card':
        return (
          <>
            <FormGroup label="Cardholder Name" error={errors['details.cardholder_name']}>
              <Input
                type="text"
                placeholder="Name on card"
                value={formData.details.cardholder_name || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  details: { ...prev.details, cardholder_name: e.target.value }
                }))}
                error={!!errors['details.cardholder_name']}
              />
            </FormGroup>
            
            <FormGroup label="Card Number" error={errors['details.card_number']}>
              <Input
                type="text"
                placeholder="1234 5678 9012 3456"
                value={formData.details.card_number || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  details: { ...prev.details, card_number: e.target.value }
                }))}
                error={!!errors['details.card_number']}
              />
            </FormGroup>
            
            <div className="grid grid-cols-2 gap-4">
              <FormGroup label="Expiry Month" error={errors['details.expiry_month']}>
                <select
                  value={formData.details.expiry_month || ''}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    details: { ...prev.details, expiry_month: e.target.value }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primarycolor focus:border-transparent"
                >
                  <option value="">Month</option>
                  {Array.from({ length: 12 }, (_, i) => (
                    <option key={i + 1} value={String(i + 1).padStart(2, '0')}>
                      {String(i + 1).padStart(2, '0')}
                    </option>
                  ))}
                </select>
              </FormGroup>
              
              <FormGroup label="Expiry Year" error={errors['details.expiry_year']}>
                <select
                  value={formData.details.expiry_year || ''}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    details: { ...prev.details, expiry_year: e.target.value }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primarycolor focus:border-transparent"
                >
                  <option value="">Year</option>
                  {Array.from({ length: 10 }, (_, i) => {
                    const year = new Date().getFullYear() + i;
                    return (
                      <option key={year} value={year}>
                        {year}
                      </option>
                    );
                  })}
                </select>
              </FormGroup>
            </div>
          </>
        );
      
      case 'bank_transfer':
        return (
          <>
            <FormGroup label="Bank Name" error={errors['details.bank_name']}>
              <Input
                type="text"
                placeholder="e.g., KCB Bank, Equity Bank"
                value={formData.details.bank_name || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  details: { ...prev.details, bank_name: e.target.value }
                }))}
                error={!!errors['details.bank_name']}
              />
            </FormGroup>
            
            <FormGroup label="Account Name" error={errors['details.account_name']}>
              <Input
                type="text"
                placeholder="Account holder name"
                value={formData.details.account_name || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  details: { ...prev.details, account_name: e.target.value }
                }))}
                error={!!errors['details.account_name']}
              />
            </FormGroup>
            
            <FormGroup label="Account Number" error={errors['details.account_number']}>
              <Input
                type="text"
                placeholder="Bank account number"
                value={formData.details.account_number || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  details: { ...prev.details, account_number: e.target.value }
                }))}
                error={!!errors['details.account_number']}
              />
            </FormGroup>
          </>
        );
      
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map(i => (
          <div key={i} className="animate-pulse">
            <div className="h-24 bg-gray-200 rounded-lg"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-bold text-gray-900">Payment Methods</h2>
          <p className="text-gray-600">Manage your payment options for faster checkout</p>
        </div>
        <Button onClick={() => setShowForm(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Add Payment Method
        </Button>
      </div>

      {/* Security Notice */}
      <Card className="p-4 bg-blue-50 border-blue-200">
        <div className="flex items-start gap-3">
          <Shield className="w-5 h-5 text-blue-600 mt-0.5" />
          <div>
            <h3 className="font-medium text-blue-900">Your payment information is secure</h3>
            <p className="text-sm text-blue-700 mt-1">
              We use industry-standard encryption to protect your payment details. 
              Your sensitive information is never stored in plain text.
            </p>
          </div>
        </div>
      </Card>

      {/* Payment Methods List */}
      {paymentMethods.length === 0 ? (
        <Card className="p-8 text-center">
          <CreditCard className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Payment Methods</h3>
          <p className="text-gray-600 mb-4">
            Add a payment method to make checkout faster and more convenient.
          </p>
          <Button onClick={() => setShowForm(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Add Your First Payment Method
          </Button>
        </Card>
      ) : (
        <div className="grid gap-4">
          {paymentMethods.map((method) => (
            <Card key={method.id} className="p-6">
              <div className="flex justify-between items-center">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    {renderPaymentMethodDetails(method)}
                    {method.is_default && (
                      <span className="inline-flex items-center gap-1 px-2 py-1 bg-primarycolor text-white text-xs rounded-full ml-auto">
                        <Star className="w-3 h-3" />
                        Default
                      </span>
                    )}
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEdit(method)}
                  >
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(method.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Add/Edit Payment Method Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-900">
                  {editingMethod ? 'Edit Payment Method' : 'Add Payment Method'}
                </h2>
                <Button variant="ghost" size="sm" onClick={resetForm}>
                  ✕
                </Button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <FormGroup label="Payment Type" error={errors.type}>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    {Object.entries(PAYMENT_TYPES).map(([type, config]) => {
                      const Icon = config.icon;
                      return (
                        <button
                          key={type}
                          type="button"
                          onClick={() => {
                            setSelectedType(type);
                            setFormData(prev => ({ ...prev, type, details: {} }));
                          }}
                          className={cn(
                            'p-4 border rounded-lg text-left transition-colors',
                            selectedType === type
                              ? 'border-primarycolor bg-primarycolor-50'
                              : 'border-gray-300 hover:border-gray-400'
                          )}
                        >
                          <div className="flex items-center gap-3">
                            <Icon className={cn('w-5 h-5', config.color)} />
                            <span className="font-medium">{config.label}</span>
                          </div>
                        </button>
                      );
                    })}
                  </div>
                </FormGroup>

                <FormGroup label="Method Title" error={errors.title}>
                  <Input
                    type="text"
                    placeholder="e.g., My M-Pesa, Work Card, Personal Account"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    error={!!errors.title}
                  />
                </FormGroup>

                {renderFormFields()}

                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="is_default"
                    checked={formData.is_default}
                    onChange={(e) => setFormData(prev => ({ ...prev, is_default: e.target.checked }))}
                    className="w-4 h-4 text-primarycolor border-gray-300 rounded focus:ring-primarycolor"
                  />
                  <label htmlFor="is_default" className="text-sm text-gray-700">
                    Set as default payment method
                  </label>
                </div>

                <div className="flex gap-3 pt-4">
                  <Button
                    type="submit"
                    disabled={createPaymentMethod.isPending || updatePaymentMethod.isPending}
                    className="flex-1"
                  >
                    {createPaymentMethod.isPending || updatePaymentMethod.isPending ? 'Saving...' : 
                     editingMethod ? 'Update Method' : 'Add Method'}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={resetForm}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
}
