#!/bin/bash

# Quick Environment Setup Script for Vercel Projects
# This script provides a simple way to sync environment variables

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
check_node() {
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js first."
        exit 1
    fi
    print_success "Node.js found: $(node --version)"
}

# Check if Vercel CLI is installed
check_vercel_cli() {
    if ! command -v vercel &> /dev/null; then
        print_warning "Vercel CLI not found. Installing..."
        npm install -g vercel
    fi
    print_success "Vercel CLI found: $(vercel --version)"
}

# Check Vercel authentication
check_vercel_auth() {
    if ! vercel whoami &> /dev/null; then
        print_warning "Not authenticated with Vercel. Please login..."
        vercel login
    fi
    print_success "Vercel authentication verified"
}

# Main setup function
main() {
    print_status "Starting Vercel Environment Setup..."
    
    # Check prerequisites
    check_node
    check_vercel_cli
    check_vercel_auth
    
    # Create scripts directory if it doesn't exist
    mkdir -p scripts
    
    # Make sync script executable
    if [ -f "scripts/sync-env.js" ]; then
        chmod +x scripts/sync-env.js
        print_success "Made sync-env.js executable"
    fi
    
    # Run the sync script
    print_status "Syncing environment variables..."
    
    # Default to development environment
    ENVIRONMENT=${1:-development}
    
    if node scripts/sync-env.js "$ENVIRONMENT"; then
        print_success "Environment variables synced successfully!"
        print_status "You can now run: npm run dev"
    else
        print_error "Failed to sync environment variables"
        exit 1
    fi
}

# Show usage if help is requested
if [[ "$1" == "--help" || "$1" == "-h" ]]; then
    echo "Usage: $0 [environment]"
    echo ""
    echo "Environments:"
    echo "  development (default)"
    echo "  preview"
    echo "  production"
    echo ""
    echo "Examples:"
    echo "  $0                    # Sync development environment"
    echo "  $0 production         # Sync production environment"
    echo "  $0 preview           # Sync preview environment"
    exit 0
fi

# Run main function
main "$@"
