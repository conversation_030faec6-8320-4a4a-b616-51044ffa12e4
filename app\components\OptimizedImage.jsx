"use client";

import Image from 'next/image';
import { useState, useRef, useEffect, useCallback } from 'react';
import { Skeleton } from './LoadingStates';

/**
 * Optimized Image Component with lazy loading, error handling, and progressive loading
 */
export default function OptimizedImage({
  src,
  alt = '',
  width,
  height,
  fill = false,
  priority = false,
  className = '',
  containerClassName = '',
  fallbackSrc = '/placeholder-image.png',
  quality = 75,
  sizes,
  objectFit = 'cover',
  showSkeleton = true,
  onLoad,
  onError,
  ...props
}) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [imageSrc, setImageSrc] = useState(src);
  const [isInView, setIsInView] = useState(priority); // If priority, load immediately
  const imgRef = useRef(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority || !imgRef.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        rootMargin: '50px', // Start loading 50px before the image comes into view
        threshold: 0.1
      }
    );

    observer.observe(imgRef.current);

    return () => observer.disconnect();
  }, [priority]);

  const handleLoad = (e) => {
    setIsLoading(false);
    setHasError(false);
    onLoad?.(e);
  };

  const handleError = (e) => {
    setIsLoading(false);
    setHasError(true);
    
    // Try fallback image if available and not already using it
    if (fallbackSrc && imageSrc !== fallbackSrc) {
      setImageSrc(fallbackSrc);
      setHasError(false);
      setIsLoading(true);
    }
    
    onError?.(e);
  };

  // Generate responsive sizes if not provided
  const responsiveSizes = sizes || (
    fill 
      ? '(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw'
      : undefined
  );

  const imageProps = {
    src: imageSrc,
    alt,
    quality,
    onLoad: handleLoad,
    onError: handleError,
    className: `${className} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`,
    style: { objectFit },
    ...props
  };

  if (fill) {
    imageProps.fill = true;
    imageProps.sizes = responsiveSizes;
  } else {
    imageProps.width = width;
    imageProps.height = height;
  }

  return (
    <div 
      ref={imgRef}
      className={`relative ${containerClassName} ${fill ? 'overflow-hidden' : ''}`}
      style={!fill ? { width, height } : undefined}
    >
      {/* Loading skeleton */}
      {isLoading && showSkeleton && (
        <Skeleton 
          className="absolute inset-0 z-10" 
          width={fill ? '100%' : width}
          height={fill ? '100%' : height}
        />
      )}

      {/* Error state */}
      {hasError && !isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 text-gray-400">
          <div className="text-center">
            <svg className="w-8 h-8 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
            </svg>
            <p className="text-xs">Image not found</p>
          </div>
        </div>
      )}

      {/* Actual image - only render when in view or priority */}
      {(isInView || priority) && (
        <Image
          {...imageProps}
          alt={alt}
          priority={priority}
          sizes={responsiveSizes}
        />
      )}
    </div>
  );
}

/**
 * Product Image Component - Specialized for product images
 */
export function ProductImage({ 
  product, 
  imageIndex = 0, 
  priority = false,
  className = '',
  ...props 
}) {
  const imageUrl = Array.isArray(product.image_url) 
    ? product.image_url[imageIndex] 
    : product.image_url;

  return (
    <OptimizedImage
      src={imageUrl}
      alt={product.name}
      priority={priority}
      className={className}
      sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
      {...props}
    />
  );
}

/**
 * Avatar Image Component - For user avatars
 */
export function AvatarImage({ 
  src, 
  alt, 
  size = 40, 
  className = '',
  fallbackInitials,
  ...props 
}) {
  const [hasError, setHasError] = useState(false);

  if (hasError || !src) {
    return (
      <div 
        className={`flex items-center justify-center bg-gray-300 text-gray-600 font-medium rounded-full ${className}`}
        style={{ width: size, height: size }}
      >
        {fallbackInitials || alt?.charAt(0)?.toUpperCase() || '?'}
      </div>
    );
  }

  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={size}
      height={size}
      className={`rounded-full ${className}`}
      objectFit="cover"
      onError={() => setHasError(true)}
      showSkeleton={false}
      {...props}
    />
  );
}

/**
 * Banner Image Component - For marketing banners
 */
export function BannerImage({ 
  src, 
  alt, 
  priority = true,
  className = '',
  ...props 
}) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      fill
      priority={priority}
      className={className}
      sizes="100vw"
      quality={85}
      objectFit="cover"
      {...props}
    />
  );
}

/**
 * Gallery Image Component - For image galleries with zoom
 */
export function GalleryImage({ 
  src, 
  alt, 
  onClick,
  className = '',
  ...props 
}) {
  return (
    <div 
      className={`cursor-pointer hover:opacity-80 transition-opacity ${className}`}
      onClick={onClick}
    >
      <OptimizedImage
        src={src}
        alt={alt}
        fill
        sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
        quality={80}
        objectFit="cover"
        {...props}
      />
    </div>
  );
}

/**
 * Utility function to preload images
 */
export function preloadImage(src) {
  return new Promise((resolve, reject) => {
    const img = new window.Image();
    img.onload = resolve;
    img.onerror = reject;
    img.src = src;
  });
}

/**
 * Hook for preloading multiple images
 */
export function useImagePreloader(imageUrls) {
  const [loadedImages, setLoadedImages] = useState(new Set());
  const [isLoading, setIsLoading] = useState(false);

  const preloadImages = useCallback(async (urls = imageUrls) => {
    setIsLoading(true);

    try {
      const promises = urls.map(async (url) => {
        try {
          await preloadImage(url);
          setLoadedImages(prev => new Set([...prev, url]));
          return url;
        } catch (error) {
          console.warn(`Failed to preload image: ${url}`);
          return null;
        }
      });

      await Promise.allSettled(promises);
    } finally {
      setIsLoading(false);
    }
  }, [imageUrls]);

  useEffect(() => {
    if (imageUrls?.length > 0) {
      preloadImages();
    }
  }, [imageUrls, preloadImages]);

  return {
    loadedImages,
    isLoading,
    preloadImages
  };
}
