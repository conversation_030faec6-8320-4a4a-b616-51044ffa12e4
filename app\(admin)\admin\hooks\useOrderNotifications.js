import { useState, useEffect, useCallback } from 'react'
import { supabase } from '../../../lib/supabase'
import { toast } from 'sonner'

export function useOrderNotifications() {
    const [notifications, setNotifications] = useState([])
    const [unreadCount, setUnreadCount] = useState(0)
    const [unreadOrders, setUnreadOrders] = useState(0)

    const saveNotificationsToStorage = useCallback((notifications) => {
        try {
            const toStore = notifications.slice(0, 100)
            localStorage.setItem('orderNotifications', JSON.stringify(toStore))
        } catch (error) {
            console.error('Error saving notifications:', error)
        }
    }, [])

    const updateUnreadCount = useCallback((notifications) => {
        const unread = notifications.filter(n => !n.read).length
        setUnreadCount(unread)
    }, [])

    const handleNewOrder = useCallback(async (payload) => {
        try {
            const order = payload.new
            const { data: userData } = await supabase
                .from('users')
                .select('name')
                .eq('id', order.user_id)
                .single()

            const newNotification = {
                id: crypto.randomUUID(),
                orderId: order.id,
                mpesaCode: order.mpesa_code,
                amount: order.total_amount,
                customerName: userData?.name || 'Unknown Customer',
                timestamp: order.created_at,
                status: order.status,
                read: false
            }

            setNotifications(prev => {
                const updated = [newNotification, ...prev]
                saveNotificationsToStorage(updated)
                updateUnreadCount(updated)
                return updated
            })

            toast.message('New Order Received!', {
                description: `${userData?.name || 'Unknown Customer'} - KES ${order.total_amount}`,
            })
        } catch (error) {
            console.error('Error processing notification:', error)
        }
    }, [saveNotificationsToStorage, updateUnreadCount])

    const loadNotificationsFromStorage = useCallback(() => {
        try {
            const stored = localStorage.getItem('orderNotifications')
            if (stored) {
                const parsedNotifications = JSON.parse(stored)
                setNotifications(parsedNotifications)
                updateUnreadCount(parsedNotifications)
            }
        } catch (error) {
            console.error('Error loading notifications:', error)
        }
    }, [updateUnreadCount])

    useEffect(() => {
        // Load existing notifications
        loadNotificationsFromStorage()

        const channel = supabase
            .channel('orders-changes')
            .on(
                'postgres_changes',
                {
                    event: 'INSERT',
                    schema: 'public',
                    table: 'orders'
                },
                async (payload) => {
                    setUnreadOrders(prev => prev + 1)
                    await handleNewOrder(payload)
                }
            )
            .subscribe()

        const lastVisit = localStorage.getItem('lastOrdersVisit')

        if (lastVisit) {
            supabase
                .from('orders')
                .select('*', { count: 'exact' })
                .gt('created_at', lastVisit)
                .then(({ count }) => {
                    setUnreadOrders(count || 0)
                })
        }

        return () => {
            supabase.removeChannel(channel)
        }
    }, [handleNewOrder, loadNotificationsFromStorage])

    const resetUnreadOrders = () => {
        setUnreadOrders(0)
        localStorage.setItem('lastOrdersVisit', new Date().toISOString())
    }

    const markAsRead = useCallback((notificationId) => {
        setNotifications(prev => {
            const updated = prev.map(notification =>
                notification.id === notificationId
                    ? { ...notification, read: true }
                    : notification
            )
            saveNotificationsToStorage(updated)
            updateUnreadCount(updated)
            return updated
        })
    }, [saveNotificationsToStorage, updateUnreadCount])

    const markAllAsRead = useCallback(() => {
        setNotifications(prev => {
            const updated = prev.map(notification => ({ ...notification, read: true }))
            saveNotificationsToStorage(updated)
            updateUnreadCount(updated)
            return updated
        })
    }, [saveNotificationsToStorage, updateUnreadCount])

    const clearNotifications = () => {
        setNotifications([])
        localStorage.removeItem('orderNotifications')
        setUnreadCount(0)
    }

    return {
        notifications,
        unreadCount,
        unreadOrders,
        resetUnreadOrders,
        markAsRead,
        markAllAsRead,
        clearNotifications
    }
}
