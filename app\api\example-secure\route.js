import { NextResponse } from 'next/server';
import { 
  withRateLimit, 
  withCSRFProtection, 
  withRequestValidation, 
  withSecurityHeaders,
  combineMiddleware 
} from '../../lib/security';
import { z } from 'zod';

// Example validation schema
const exampleSchema = z.object({
  name: z.string().min(1).max(100),
  email: z.string().email(),
  message: z.string().min(10).max(1000)
});

// Example secure API handler
async function handler(req) {
  try {
    // Access validated body
    const { name, email, message } = req.validatedBody || {};
    
    // Your business logic here
    console.log('Processing secure request:', { name, email, message });
    
    return NextResponse.json({
      success: true,
      message: 'Request processed successfully',
      data: { name, email }
    });
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Apply security middleware
const secureHandler = combineMiddleware(
  withSecurityHeaders,
  (handler) => withRateLimit(handler, { 
    limit: 10, // 10 requests per window
    windowMs: 60 * 1000 // 1 minute window
  }),
  withCSRFProtection,
  (handler) => withRequestValidation(handler, exampleSchema)
)(handler);

export { secureHandler as POST };
