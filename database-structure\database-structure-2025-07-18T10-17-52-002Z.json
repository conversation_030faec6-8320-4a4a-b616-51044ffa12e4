{"generated_at": "2025-07-18T10:17:46.332Z", "supabase_url": "https://jtuzvomskxxzbdqbnvzn.supabase.co", "analysis": {"tables": {}, "table_access": {"users": {"accessible": true, "rowCount": 28}, "products": {"accessible": true, "rowCount": 91}, "categories": {"accessible": true, "rowCount": 5}, "orders": {"accessible": true, "rowCount": 947}, "cart": {"accessible": true, "rowCount": 8}, "wishlists": {"accessible": true, "rowCount": 7}, "marketing_banners": {"accessible": true, "rowCount": 2}, "order_items": {"accessible": true, "rowCount": null}}, "codebase_usage": {"tables_used": ["users", "products", "categories", "orders", "cart", "wishlists", "marketing_banners", "order_items"], "common_queries": ["products with categories join", "user orders with product details", "cart items with product info", "wishlist items", "dashboard sales data (RPC)"]}, "rls_policies": []}}