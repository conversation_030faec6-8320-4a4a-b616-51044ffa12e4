"use client";

import React, { forwardRef, useState } from 'react';
import { cn } from '../../lib/utils';
import { Eye, EyeOff, AlertCircle, CheckCircle } from 'lucide-react';

const inputVariants = {
  variant: {
    default: 'border-gray-300 focus:border-primarycolor-500 focus:ring-primarycolor-500',
    error: 'border-error-500 focus:border-error-500 focus:ring-error-500',
    success: 'border-success-500 focus:border-success-500 focus:ring-success-500',
    warning: 'border-warning-500 focus:border-warning-500 focus:ring-warning-500',
  },
  size: {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-3 py-2 text-base',
    lg: 'px-4 py-3 text-lg',
  }
};

const Input = forwardRef(({
  className,
  type = 'text',
  variant = 'default',
  size = 'md',
  error,
  success,
  warning,
  disabled,
  leftIcon,
  rightIcon,
  placeholder,
  ...props
}, ref) => {
  const [showPassword, setShowPassword] = useState(false);
  
  // Determine variant based on state
  let currentVariant = variant;
  if (error) currentVariant = 'error';
  else if (success) currentVariant = 'success';
  else if (warning) currentVariant = 'warning';

  const baseClasses = 'w-full rounded-lg border bg-white transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50';
  const variantClasses = inputVariants.variant[currentVariant];
  const sizeClasses = inputVariants.size[size];
  
  const hasLeftIcon = leftIcon || error || success || warning;
  const hasRightIcon = rightIcon || type === 'password';
  
  const paddingClasses = cn(
    hasLeftIcon && 'pl-10',
    hasRightIcon && 'pr-10'
  );

  const inputType = type === 'password' && showPassword ? 'text' : type;

  return (
    <div className="relative">
      {/* Left Icon */}
      {hasLeftIcon && (
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          {error && <AlertCircle className="w-5 h-5 text-error-500" />}
          {success && <CheckCircle className="w-5 h-5 text-success-500" />}
          {warning && <AlertCircle className="w-5 h-5 text-warning-500" />}
          {leftIcon && !error && !success && !warning && (
            <span className="text-gray-400">{leftIcon}</span>
          )}
        </div>
      )}

      <input
        type={inputType}
        className={cn(
          baseClasses,
          variantClasses,
          sizeClasses,
          paddingClasses,
          className
        )}
        ref={ref}
        disabled={disabled}
        placeholder={placeholder}
        {...props}
      />

      {/* Right Icon */}
      {hasRightIcon && (
        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
          {type === 'password' && (
            <button
              type="button"
              className="text-gray-400 hover:text-gray-600 focus:outline-none"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <EyeOff className="w-5 h-5" />
              ) : (
                <Eye className="w-5 h-5" />
              )}
            </button>
          )}
          {rightIcon && type !== 'password' && (
            <span className="text-gray-400">{rightIcon}</span>
          )}
        </div>
      )}
    </div>
  );
});

Input.displayName = 'Input';

// Textarea Component
export const Textarea = forwardRef(({
  className,
  variant = 'default',
  error,
  success,
  warning,
  rows = 4,
  ...props
}, ref) => {
  // Determine variant based on state
  let currentVariant = variant;
  if (error) currentVariant = 'error';
  else if (success) currentVariant = 'success';
  else if (warning) currentVariant = 'warning';

  const baseClasses = 'w-full rounded-lg border bg-white px-3 py-2 text-base transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 resize-none';
  const variantClasses = inputVariants.variant[currentVariant];

  return (
    <textarea
      className={cn(baseClasses, variantClasses, className)}
      rows={rows}
      ref={ref}
      {...props}
    />
  );
});

Textarea.displayName = 'Textarea';

// Select Component
export const Select = forwardRef(({
  className,
  variant = 'default',
  size = 'md',
  error,
  success,
  warning,
  children,
  placeholder,
  ...props
}, ref) => {
  // Determine variant based on state
  let currentVariant = variant;
  if (error) currentVariant = 'error';
  else if (success) currentVariant = 'success';
  else if (warning) currentVariant = 'warning';

  const baseClasses = 'w-full rounded-lg border bg-white transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 appearance-none bg-no-repeat bg-right bg-[length:16px_16px] pr-10';
  const variantClasses = inputVariants.variant[currentVariant];
  const sizeClasses = inputVariants.size[size];

  return (
    <div className="relative">
      <select
        className={cn(baseClasses, variantClasses, sizeClasses, className)}
        ref={ref}
        {...props}
        style={{
          backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
        }}
      >
        {placeholder && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        {children}
      </select>
    </div>
  );
});

Select.displayName = 'Select';

// Form Group Component
export function FormGroup({
  label,
  error,
  success,
  warning,
  hint,
  required,
  children,
  className,
  ...props
}) {
  const id = props.id || `form-group-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <div className={cn('space-y-1', className)} {...props}>
      {label && (
        <label
          htmlFor={id}
          className={cn(
            'block text-sm font-medium',
            error ? 'text-error-700' : 'text-gray-700'
          )}
        >
          {label}
          {required && <span className="text-error-500 ml-1">*</span>}
        </label>
      )}
      
      {React.cloneElement(children, { id, error, success, warning })}
      
      {hint && !error && !success && !warning && (
        <p className="text-sm text-gray-500">{hint}</p>
      )}
      
      {error && (
        <p className="text-sm text-error-600 flex items-center gap-1">
          <AlertCircle className="w-4 h-4" />
          {error}
        </p>
      )}
      
      {success && (
        <p className="text-sm text-success-600 flex items-center gap-1">
          <CheckCircle className="w-4 h-4" />
          {success}
        </p>
      )}
      
      {warning && (
        <p className="text-sm text-warning-600 flex items-center gap-1">
          <AlertCircle className="w-4 h-4" />
          {warning}
        </p>
      )}
    </div>
  );
}

// Search Input Component
export function SearchInput({
  onSearch,
  placeholder = "Search...",
  className,
  ...props
}) {
  const [value, setValue] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    onSearch?.(value);
  };

  return (
    <form onSubmit={handleSubmit} className={cn('relative', className)}>
      <Input
        type="search"
        placeholder={placeholder}
        value={value}
        onChange={(e) => setValue(e.target.value)}
        leftIcon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        }
        {...props}
      />
    </form>
  );
}

export default Input;
