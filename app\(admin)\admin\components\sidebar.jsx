"use client"
import Link from 'next/link'
import { useEffect, useState, useCallback } from 'react'
import { usePathname } from 'next/navigation'
import { useOrderNotifications } from '../hooks/useOrderNotifications'
import {
  Package,
  Tags,
  ShoppingBag,
  BarChart2,
  Settings,
  Truck,
  Users,
  Home,
  ImageIcon
} from 'lucide-react'

export default function Sidebar({ isOpen, setIsOpen }) {
  const [isMobile, setIsMobile] = useState(false)
  const pathname = usePathname()
  const { unreadOrders, resetUnreadOrders } = useOrderNotifications()

  const handleResize = useCallback(() => {
    setIsMobile(window.innerWidth < 1024)
  }, [])

  useEffect(() => {
    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [handleResize])

  useEffect(() => {
    if (pathname === '/admin/orders') {
        resetUnreadOrders()
    }
  }, [pathname, resetUnreadOrders])

  const handleLinkClick = useCallback(() => {
    if (isMobile) {
      setIsOpen(false)
    }
  }, [isMobile, setIsOpen])

  const menuItems = [
    { icon: Home, label: 'Dashboard', path: '/admin' },
    { icon: Package, label: 'Products', path: '/admin/products' },
    { icon: Tags, label: 'Categories', path: '/admin/categories' },
    { icon: ImageIcon, label: 'Marketing', path: '/admin/marketing' },
    { icon: ShoppingBag, label: 'Orders', path: '/admin/orders', badge: unreadOrders > 0 ? unreadOrders : null },
    { icon: Users, label: 'Customers', path: '/admin/customers' },
    { icon: Truck, label: 'Delivery', path: '/admin/delivery' },
    { icon: BarChart2, label: 'Reports', path: '/admin/reports' },
    { icon: Settings, label: 'Settings', path: '/admin/settings' },
  ]

  return (
    <>
      <aside
        className={`
          fixed lg:static top-0 left-0 h-screen bg-white border-r z-30
          transform transition-transform duration-300 ease-in-out
          ${isMobile ? (isOpen ? 'translate-x-0' : '-translate-x-full') : 'translate-x-0'}
          ${isMobile ? 'w-64' : 'w-64'}
        `}
      >
        <div className="p-6">
          <h1 className="text-xl font-bold text-primarycolor">SHERRY&apos;S MERCHANTS</h1>
          <p className="text-sm text-gray-500">Admin Dashboard</p>
        </div>

        <nav className="flex-1 px-4">
          {menuItems.map((item) => {
            const Icon = item.icon
            const isActive = pathname === item.path

            return (
              <Link
                key={item.path}
                href={item.path}
                onClick={handleLinkClick}
                prefetch={true}
                className={`flex items-center gap-3 px-4 py-3 mb-1 rounded-lg transition-colors relative ${
                  isActive
                    ? 'bg-primarycolor text-white'
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <Icon size={20} />
                <span>{item.label}</span>
                {item.badge && (
                  <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs w-5 h-5 rounded-full flex items-center justify-center">
                    {item.badge}
                  </span>
                )}
              </Link>
            )
          })}
        </nav>

        <div className="p-4 border-t">
          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-sm text-gray-600">Need help?</p>
            <Link href="/documentation" className="text-primarycolor text-sm font-medium">
              View Documentation
            </Link>
          </div>
        </div>
      </aside>
      
      {isMobile && isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-20"
          onClick={handleLinkClick}
        />
      )}
    </>
  )
}
