"use client";

import { useMemo } from 'react';
import Image from 'next/image';
import { Truck, Package, Clock, MapPin } from 'lucide-react';
import { toTitleCase } from '../../lib/utils';
import { useDeliveryCost } from '../../hooks/useDelivery';

export default function EnhancedOrderSummary({ 
  cartItems = [], 
  deliveryAddress,
  className = "" 
}) {
  const county = deliveryAddress?.county;
  const { data: deliveryInfo } = useDeliveryCost(county);

  const subtotal = useMemo(() => {
    return cartItems.reduce((sum, item) => {
      return sum + (Number(item.product?.price || 0) * Number(item.quantity || 0));
    }, 0);
  }, [cartItems]);

  const deliveryCost = deliveryInfo?.cost || 0;
  const total = subtotal + deliveryCost;

  return (
    <div className={`bg-white rounded-xl border border-gray-200 shadow-sm ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
          <Package className="w-5 h-5 text-primarycolor" />
          Order Summary
        </h2>
      </div>

      {/* Items List */}
      <div className="p-6 space-y-4 max-h-64 overflow-y-auto">
        {cartItems.map((item) => (
          <OrderItem key={item.productId} item={item} />
        ))}
      </div>

      {/* Delivery Information */}
      {deliveryAddress && (
        <div className="px-6 py-4 bg-gray-50 border-y border-gray-100">
          <div className="flex items-start gap-3">
            <MapPin className="w-5 h-5 text-primarycolor mt-0.5" />
            <div className="flex-1">
              <h4 className="font-medium text-gray-900 mb-1">Delivery Address</h4>
              <p className="text-sm text-gray-600">
                {deliveryAddress.full_name}
              </p>
              <p className="text-sm text-gray-600">
                {deliveryAddress.street_address}
                {deliveryAddress.city && `, ${deliveryAddress.city}`}
                {deliveryAddress.county && `, ${deliveryAddress.county}`}
              </p>
            </div>
          </div>
          
          {deliveryInfo && (
            <div className="flex items-center gap-3 mt-3 pt-3 border-t border-gray-200">
              <Truck className="w-4 h-4 text-primarycolor" />
              <span className="text-sm text-gray-600">
                {deliveryInfo.zone_name}
              </span>
              {deliveryInfo.estimated_time && (
                <>
                  <Clock className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-600">
                    {deliveryInfo.estimated_time}
                  </span>
                </>
              )}
            </div>
          )}
        </div>
      )}

      {/* Order Totals */}
      <div className="p-6 space-y-3">
        <div className="flex justify-between text-gray-600">
          <span>Subtotal ({cartItems.length} items)</span>
          <span>Ksh. {Math.round(subtotal).toLocaleString()}</span>
        </div>
        
        <div className="flex justify-between text-gray-600">
          <span>Delivery</span>
          <span>
            {deliveryCost > 0 
              ? `Ksh. ${Math.round(deliveryCost).toLocaleString()}`
              : 'Calculated at checkout'
            }
          </span>
        </div>
        
        <div className="border-t border-gray-200 pt-3">
          <div className="flex justify-between text-lg font-semibold text-gray-900">
            <span>Total</span>
            <span>Ksh. {Math.round(total).toLocaleString()}</span>
          </div>
        </div>
      </div>
    </div>
  );
}

function OrderItem({ item }) {
  const product = item.product;
  const quantity = item.quantity;
  const itemTotal = Number(product?.price || 0) * Number(quantity || 0);

  return (
    <div className="flex gap-3">
      {/* Product Image */}
      <div className="w-16 h-16 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
        <Image
          src={Array.isArray(product?.image_url) ? product.image_url[0] : product?.image_url || '/placeholder.jpg'}
          alt={product?.name || 'Product'}
          width={64}
          height={64}
          className="w-full h-full object-contain p-1"
        />
      </div>

      {/* Product Details */}
      <div className="flex-1 min-w-0">
        <h4 className="font-medium text-gray-900 text-sm line-clamp-2">
          {toTitleCase(product?.name || 'Unknown Product')}
        </h4>
        <div className="flex items-center justify-between mt-1">
          <span className="text-sm text-gray-600">Qty: {quantity}</span>
          <span className="font-semibold text-gray-900">
            Ksh. {Math.round(itemTotal).toLocaleString()}
          </span>
        </div>
        <div className="text-xs text-gray-500 mt-1">
          Ksh. {Math.round(Number(product?.price || 0)).toLocaleString()} each
        </div>
      </div>
    </div>
  );
}
