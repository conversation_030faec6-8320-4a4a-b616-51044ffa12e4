"use client";

import React from 'react';
import { cn } from '../../lib/utils';
import { LoadingSpinner } from '../LoadingStates';

const buttonVariants = {
  variant: {
    primary: 'bg-primarycolor hover:bg-primarycolor-700 text-white shadow-md hover:shadow-lg',
    secondary: 'bg-secondarycolor hover:bg-secondarycolor-600 text-white shadow-md hover:shadow-lg',
    outline: 'border-2 border-primarycolor text-primarycolor hover:bg-primarycolor hover:text-white',
    ghost: 'text-primarycolor hover:bg-primarycolor-50',
    success: 'bg-success hover:bg-success-600 text-white shadow-md hover:shadow-lg',
    warning: 'bg-warning hover:bg-warning-600 text-white shadow-md hover:shadow-lg',
    error: 'bg-error hover:bg-error-600 text-white shadow-md hover:shadow-lg',
    info: 'bg-info hover:bg-info-600 text-white shadow-md hover:shadow-lg',
  },
  size: {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
    xl: 'px-8 py-4 text-xl',
    icon: 'p-2',
  },
  radius: {
    none: 'rounded-none',
    sm: 'rounded-sm',
    md: 'rounded-md',
    lg: 'rounded-lg',
    xl: 'rounded-xl',
    full: 'rounded-full',
  }
};

export default function Button({
  children,
  variant = 'primary',
  size = 'md',
  radius = 'lg',
  loading = false,
  disabled = false,
  fullWidth = false,
  leftIcon,
  rightIcon,
  className,
  onClick,
  type = 'button',
  ...props
}) {
  const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primarycolor-300 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed active:scale-95';
  
  const variantClasses = buttonVariants.variant[variant];
  const sizeClasses = buttonVariants.size[size];
  const radiusClasses = buttonVariants.radius[radius];
  const widthClasses = fullWidth ? 'w-full' : '';
  
  const isDisabled = disabled || loading;

  return (
    <button
      type={type}
      className={cn(
        baseClasses,
        variantClasses,
        sizeClasses,
        radiusClasses,
        widthClasses,
        className
      )}
      disabled={isDisabled}
      onClick={onClick}
      {...props}
    >
      {loading && (
        <LoadingSpinner size="sm" className="mr-2" />
      )}
      
      {!loading && leftIcon && (
        <span className="mr-2 flex-shrink-0">
          {leftIcon}
        </span>
      )}
      
      <span className={loading ? 'opacity-70' : ''}>
        {children}
      </span>
      
      {!loading && rightIcon && (
        <span className="ml-2 flex-shrink-0">
          {rightIcon}
        </span>
      )}
    </button>
  );
}

// Button Group Component
export function ButtonGroup({ children, className, ...props }) {
  return (
    <div 
      className={cn('inline-flex rounded-lg shadow-sm', className)}
      role="group"
      {...props}
    >
      {React.Children.map(children, (child, index) => {
        if (!React.isValidElement(child)) return child;
        
        const isFirst = index === 0;
        const isLast = index === React.Children.count(children) - 1;
        
        return React.cloneElement(child, {
          className: cn(
            child.props.className,
            !isFirst && '-ml-px',
            isFirst && 'rounded-r-none',
            isLast && 'rounded-l-none',
            !isFirst && !isLast && 'rounded-none'
          )
        });
      })}
    </div>
  );
}

// Icon Button Component
export function IconButton({
  icon,
  'aria-label': ariaLabel,
  size = 'md',
  variant = 'ghost',
  className,
  ...props
}) {
  return (
    <Button
      variant={variant}
      size="icon"
      className={cn(
        size === 'sm' && 'w-8 h-8',
        size === 'md' && 'w-10 h-10',
        size === 'lg' && 'w-12 h-12',
        className
      )}
      aria-label={ariaLabel}
      {...props}
    >
      {icon}
    </Button>
  );
}

// Floating Action Button
export function FloatingActionButton({
  icon,
  className,
  ...props
}) {
  return (
    <Button
      variant="primary"
      size="icon"
      radius="full"
      className={cn(
        'fixed bottom-6 right-6 w-14 h-14 shadow-xl hover:shadow-2xl z-50',
        'transform hover:scale-110 transition-all duration-200',
        className
      )}
      {...props}
    >
      {icon}
    </Button>
  );
}

// Link Button (styled like button but behaves like link)
export function LinkButton({
  href,
  children,
  external = false,
  className,
  ...props
}) {
  const Component = external ? 'a' : 'Link';
  const linkProps = external 
    ? { href, target: '_blank', rel: 'noopener noreferrer' }
    : { href };

  return (
    <Component
      {...linkProps}
      className={cn(
        'inline-flex items-center justify-center font-medium transition-all duration-200',
        'text-primarycolor hover:text-primarycolor-700 underline-offset-4 hover:underline',
        className
      )}
      {...props}
    >
      {children}
    </Component>
  );
}
