import { supabase } from '../lib/supabase';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

/**
 * Cart-related hooks
 * Handles cart operations for authenticated users
 */

export function useCartItems(userId) {
  return useQuery({
    queryKey: ['cart', userId],
    queryFn: async () => {
      if (!userId) return [];
      
      const { data, error } = await supabase
        .from('cart')
        .select('*, products(*)')
        .eq('user_id', userId);
      
      if (error) throw error;
      return data;
    },
    enabled: !!userId,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
}

export function useSyncCartToDb() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ userId, cartItems }) => {
      if (!userId) return;

      try {
        if (cartItems.length === 0) {
          // Clear all cart items for user
          const { error } = await supabase
            .from('cart')
            .delete()
            .eq('user_id', userId);
          
          if (error) throw error;
          return;
        }

        const cartData = cartItems.map(item => ({
          user_id: userId,
          product_id: item.productId,
          quantity: item.quantity,
          price_at_time: item.product.price,
          updated_at: new Date().toISOString()
        }));

        // Batch upsert for better performance
        const { error } = await supabase
          .from('cart')
          .upsert(cartData, {
            onConflict: 'user_id,product_id',
            ignoreDuplicates: false
          });
      
        if (error) throw error;

        // Clean up removed items
        const currentProductIds = cartItems.map(item => item.productId);
        if (currentProductIds.length > 0) {
          const { error: cleanupError } = await supabase
            .from('cart')
            .delete()
            .eq('user_id', userId)
            .not('product_id', 'in', `(${currentProductIds.join(',')})`);
          
          if (cleanupError) throw cleanupError;
        }
      } catch (error) {
        console.error('Error syncing cart to database:', error);
        throw error;
      }
    },
    onSuccess: (_, { userId }) => {
      queryClient.invalidateQueries({ queryKey: ['cart', userId] });
    },
  });
}

export function useAddToCart() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ userId, productId, quantity }) => {
      const { data, error } = await supabase
        .from('cart')
        .upsert(
          { 
            user_id: userId, 
            product_id: productId, 
            quantity,
            updated_at: new Date().toISOString()
          }, 
          { onConflict: ['user_id', 'product_id'] }
        )
        .select();
      
      if (error) throw error;
      return data;
    },
    onSuccess: (_, { userId }) => {
      queryClient.invalidateQueries({ queryKey: ['cart', userId] });
    },
  });
}

export function useUpdateCartItem() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ userId, productId, quantity }) => {
      if (quantity <= 0) {
        // Remove item if quantity is 0 or negative
        const { error } = await supabase
          .from('cart')
          .delete()
          .match({ user_id: userId, product_id: productId });
        
        if (error) throw error;
        return null;
      }

      const { data, error } = await supabase
        .from('cart')
        .update({ 
          quantity,
          updated_at: new Date().toISOString()
        })
        .match({ user_id: userId, product_id: productId })
        .select();
      
      if (error) throw error;
      return data;
    },
    onSuccess: (_, { userId }) => {
      queryClient.invalidateQueries({ queryKey: ['cart', userId] });
    },
  });
}

export function useRemoveFromCart() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ userId, productId }) => {
      if (!userId) return;
      
      const { error } = await supabase
        .from('cart')
        .delete()
        .match({ user_id: userId, product_id: productId });
      
      if (error) throw error;
    },
    onSuccess: (_, { userId }) => {
      queryClient.invalidateQueries({ queryKey: ['cart', userId] });
    },
  });
}

export function useClearCart() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (userId) => {
      if (!userId) return;
      
      const { error } = await supabase
        .from('cart')
        .delete()
        .eq('user_id', userId);
      
      if (error) throw error;
    },
    onSuccess: (_, userId) => {
      queryClient.invalidateQueries({ queryKey: ['cart', userId] });
    },
  });
}

// Cart analytics
export function useCartAnalytics(userId) {
  return useQuery({
    queryKey: ['cart-analytics', userId],
    queryFn: async () => {
      if (!userId) return null;

      // Get cart items with product details
      const { data: cartItems, error } = await supabase
        .from('cart')
        .select(`
          *,
          products (
            name,
            price,
            category_name,
            discount
          )
        `)
        .eq('user_id', userId);

      if (error) throw error;

      // Calculate analytics
      const totalItems = cartItems.reduce((sum, item) => sum + item.quantity, 0);
      const totalValue = cartItems.reduce((sum, item) => {
        const price = item.products.price;
        const discount = item.products.discount || 0;
        const discountedPrice = price * (1 - discount / 100);
        return sum + (discountedPrice * item.quantity);
      }, 0);

      const categories = [...new Set(cartItems.map(item => item.products.category_name))];
      const averageItemValue = totalItems > 0 ? totalValue / totalItems : 0;

      return {
        totalItems,
        totalValue,
        averageItemValue,
        uniqueProducts: cartItems.length,
        categories: categories.length,
        categoryBreakdown: categories.map(category => ({
          name: category,
          items: cartItems.filter(item => item.products.category_name === category).length
        }))
      };
    },
    enabled: !!userId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Abandoned cart recovery
export function useAbandonedCarts() {
  return useQuery({
    queryKey: ['abandoned-carts'],
    queryFn: async () => {
      // Get carts that haven't been updated in the last 24 hours
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
      
      const { data, error } = await supabase
        .from('cart')
        .select(`
          user_id,
          updated_at,
          products (
            name,
            price,
            image_url
          )
        `)
        .lt('updated_at', oneDayAgo)
        .order('updated_at', { ascending: false });

      if (error) throw error;

      // Group by user
      const abandonedCarts = data.reduce((acc, item) => {
        if (!acc[item.user_id]) {
          acc[item.user_id] = {
            userId: item.user_id,
            lastUpdated: item.updated_at,
            items: []
          };
        }
        acc[item.user_id].items.push(item.products);
        return acc;
      }, {});

      return Object.values(abandonedCarts);
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Cart recommendations
export function useCartRecommendations(userId) {
  return useQuery({
    queryKey: ['cart-recommendations', userId],
    queryFn: async () => {
      if (!userId) return [];

      // Get current cart items
      const { data: cartItems, error: cartError } = await supabase
        .from('cart')
        .select('product_id, products(category_name)')
        .eq('user_id', userId);

      if (cartError) throw cartError;

      if (cartItems.length === 0) return [];

      // Get categories from cart
      const cartCategories = [...new Set(cartItems.map(item => item.products.category_name))];
      const cartProductIds = cartItems.map(item => item.product_id);

      // Find related products from same categories
      const { data: recommendations, error: recError } = await supabase
        .from('products')
        .select('*')
        .in('category_name', cartCategories)
        .not('id', 'in', `(${cartProductIds.join(',')})`)
        .limit(6);

      if (recError) throw recError;

      return recommendations.map(product => ({
        ...product,
        image_url: Array.isArray(product.image_url) ? product.image_url : [product.image_url]
      }));
    },
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}
