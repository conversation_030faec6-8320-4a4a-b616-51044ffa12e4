"use client";

import React, { useRef, useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ChevronLeft, ChevronRight, ShoppingBag } from 'lucide-react';
import Card from './ui/Card';
import { IconButton } from './ui/Button';
import EnhancedProductCard from './EnhancedProductCard';
import { cn, getCategoryPath } from '../lib/utils';

export default function CategoryCarousel({ 
  title = "Shop by Category",
  categories = [],
  products = [],
  showProducts = true,
  className 
}) {
  const scrollContainerRef = useRef(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);

  const checkScrollButtons = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  useEffect(() => {
    checkScrollButtons();
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScrollButtons);
      return () => container.removeEventListener('scroll', checkScrollButtons);
    }
  }, [categories]);

  const scroll = (direction) => {
    if (scrollContainerRef.current) {
      const scrollAmount = 300;
      const newScrollLeft = scrollContainerRef.current.scrollLeft + 
        (direction === 'left' ? -scrollAmount : scrollAmount);
      
      scrollContainerRef.current.scrollTo({
        left: newScrollLeft,
        behavior: 'smooth'
      });
    }
  };

  if (!categories.length) return null;

  return (
    <section className={cn("py-8", className)}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              {title}
            </h2>
            <p className="text-gray-600">
              Discover products across different categories
            </p>
          </div>

          {/* Navigation Buttons */}
          <div className="flex items-center gap-2">
            <IconButton
              icon={<ChevronLeft className="w-5 h-5" />}
              variant="outline"
              size="sm"
              onClick={() => scroll('left')}
              disabled={!canScrollLeft}
              aria-label="Scroll left"
            />
            <IconButton
              icon={<ChevronRight className="w-5 h-5" />}
              variant="outline"
              size="sm"
              onClick={() => scroll('right')}
              disabled={!canScrollRight}
              aria-label="Scroll right"
            />
          </div>
        </div>

        {/* Categories Carousel */}
        <div className="relative">
          <div
            ref={scrollContainerRef}
            className="flex gap-4 overflow-x-auto scrollbar-hide pb-4"
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {categories.map((category) => {
              const categoryProducts = products.filter(
                product => product.category_id === category.id
              ).slice(0, 4);

              return (
                <div key={category.id} className="flex-shrink-0 w-80">
                  <Card className="p-6 h-full hover:shadow-lg transition-shadow">
                    {/* Category Header */}
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-12 h-12 bg-primarycolor-50 rounded-lg flex items-center justify-center">
                        {category.image_url ? (
                          <Image
                            src={category.image_url}
                            alt={category.name}
                            width={32}
                            height={32}
                            className="w-8 h-8 object-cover rounded"
                          />
                        ) : (
                          <ShoppingBag className="w-6 h-6 text-primarycolor" />
                        )}
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900">
                          {category.name}
                        </h3>
                        <p className="text-sm text-gray-600">
                          {categoryProducts.length} products
                        </p>
                      </div>
                    </div>

                    {/* Category Products Preview */}
                    {showProducts && categoryProducts.length > 0 && (
                      <div className="grid grid-cols-2 gap-3 mb-4">
                        {categoryProducts.map((product) => (
                          <div key={product.id} className="relative group">
                            <Link href={`/product/${product.id}`}>
                              <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                                <Image
                                  src={Array.isArray(product.image_url)
                                    ? product.image_url[0]
                                    : product.image_url
                                  }
                                  alt={product.name}
                                  width={200}
                                  height={200}
                                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                                />
                              </div>
                              <div className="mt-2">
                                <p className="text-xs font-medium text-gray-900 line-clamp-1">
                                  {product.name}
                                </p>
                                <p className="text-xs text-primarycolor font-semibold">
                                  Ksh. {Math.floor(product.price)}
                                </p>
                              </div>
                            </Link>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* View All Button */}
                    <Link
                      href={getCategoryPath(category)}
                      className="block w-full text-center py-2 px-4 bg-primarycolor-50 text-primarycolor font-medium rounded-lg hover:bg-primarycolor-100 transition-colors"
                    >
                      View All in {category.name}
                    </Link>
                  </Card>
                </div>
              );
            })}
          </div>

          {/* Gradient Overlays */}
          {canScrollLeft && (
            <div className="absolute left-0 top-0 bottom-0 w-8 bg-gradient-to-r from-white to-transparent pointer-events-none z-10" />
          )}
          {canScrollRight && (
            <div className="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-white to-transparent pointer-events-none z-10" />
          )}
        </div>
      </div>
    </section>
  );
}

// Simple Category Grid Component
export function CategoryGrid({ categories = [], className }) {
  if (!categories.length) return null;

  return (
    <section className={cn("py-8", className)}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
          {categories.map((category) => (
            <Link
              key={category.id}
              href={getCategoryPath(category)}
              className="group"
            >
              <Card className="p-4 text-center hover:shadow-md transition-all duration-200 group-hover:-translate-y-1">
                <div className="w-16 h-16 mx-auto mb-3 bg-primarycolor-50 rounded-full flex items-center justify-center group-hover:bg-primarycolor-100 transition-colors">
                  {category.image_url ? (
                    <Image
                      src={category.image_url}
                      alt={category.name}
                      width={40}
                      height={40}
                      className="w-10 h-10 object-cover rounded-full"
                    />
                  ) : (
                    <ShoppingBag className="w-8 h-8 text-primarycolor" />
                  )}
                </div>
                <h3 className="font-medium text-gray-900 group-hover:text-primarycolor transition-colors text-sm">
                  {category.name}
                </h3>
              </Card>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}

// Product Carousel Component
export function ProductCarousel({ 
  title,
  products = [],
  viewAllLink,
  className 
}) {
  const scrollContainerRef = useRef(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);

  const checkScrollButtons = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  useEffect(() => {
    checkScrollButtons();
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScrollButtons);
      return () => container.removeEventListener('scroll', checkScrollButtons);
    }
  }, [products]);

  const scroll = (direction) => {
    if (scrollContainerRef.current) {
      const scrollAmount = 300;
      const newScrollLeft = scrollContainerRef.current.scrollLeft + 
        (direction === 'left' ? -scrollAmount : scrollAmount);
      
      scrollContainerRef.current.scrollTo({
        left: newScrollLeft,
        behavior: 'smooth'
      });
    }
  };

  if (!products.length) return null;

  return (
    <section className={cn("py-8", className)}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            {title}
          </h2>

          <div className="flex items-center gap-3">
            {viewAllLink && (
              <Link
                href={viewAllLink}
                className="text-primarycolor hover:text-primarycolor-700 font-medium"
              >
                View All →
              </Link>
            )}

            {/* Navigation Buttons */}
            <div className="flex items-center gap-2">
              <IconButton
                icon={<ChevronLeft className="w-5 h-5" />}
                variant="outline"
                size="sm"
                onClick={() => scroll('left')}
                disabled={!canScrollLeft}
                aria-label="Scroll left"
              />
              <IconButton
                icon={<ChevronRight className="w-5 h-5" />}
                variant="outline"
                size="sm"
                onClick={() => scroll('right')}
                disabled={!canScrollRight}
                aria-label="Scroll right"
              />
            </div>
          </div>
        </div>

        {/* Products Carousel */}
        <div className="relative">
          <div
            ref={scrollContainerRef}
            className="flex gap-6 overflow-x-auto scrollbar-hide pb-4"
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {products.map((product) => (
              <div key={product.id} className="flex-shrink-0 w-64">
                <EnhancedProductCard
                  product={product}
                  compact
                />
              </div>
            ))}
          </div>

          {/* Gradient Overlays */}
          {canScrollLeft && (
            <div className="absolute left-0 top-0 bottom-0 w-8 bg-gradient-to-r from-white to-transparent pointer-events-none z-10" />
          )}
          {canScrollRight && (
            <div className="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-white to-transparent pointer-events-none z-10" />
          )}
        </div>
      </div>
    </section>
  );
}
