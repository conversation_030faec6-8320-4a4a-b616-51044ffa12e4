"use client";
import { useState, useEffect } from 'react';
import { useSupabase } from '../hooks/useSupabase';
import Image from 'next/image';
import { toast } from 'sonner';

export default function CategoryForm({ initialData, onSave }) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    image_url: ''
  });

  const { useAddCategory, useUpdateCategory, uploadProductImage } = useSupabase();
  const addCategory = useAddCategory();
  const updateCategory = useUpdateCategory();
  const [imageFile, setImageFile] = useState(null);

  useEffect(() => {
    if (initialData) {
      setFormData({
        name: initialData.name,
        description: initialData.description,
        image_url: initialData.image_url
      });
    }
  }, [initialData]);
  

  const handleSubmit = async (e) => {
    e.preventDefault();
    let imageUrl = formData.image_url;
    const slug = formData.name.toLowerCase().replace(/\s+/g, '-');

    if (imageFile) {
      try {
        imageUrl = await uploadProductImage(imageFile);
        console.log('Image uploaded successfully:', imageUrl);
      } catch (error) {
        console.error('Image upload error:', error);
        toast.error(`Image upload error: ${error.message}`);
        return;
      }
    }
  
    const categoryData = {
      ...formData,
      image_url: imageUrl,
      slug: slug

    };
  
    try {
      await onSave(categoryData);
      setFormData({ name: '', description: '', image_url: '' });
      setImageFile(null);
    } catch (error) {
      console.error('Category save error:', error);
      toast.error(`Failed to save category: ${error.message}`);
    }
  };
  

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <input
        type="text"
        placeholder="Category Name"
        value={formData.name}
        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
        className="w-full p-2 border"
        required
      />

      <textarea
        placeholder="Category Description"
        value={formData.description}
        onChange={(e) => setFormData({ ...formData, description: e.target.value })}
        className="w-full p-2 border"
      />

      <div className="space-y-2">
        <label className="block text-sm font-medium text-primarycolor">Category Image</label>
        <input
          type="file"
          accept="image/*"
          onChange={(e) => setImageFile(e.target.files[0])}
          className="w-full p-2 border"
        />
        {formData.image_url && (
          <Image 
            src={formData.image_url} 
            alt="Preview" 
            width={80}
            height={80}
            className="object-contain"
          />
        )}
      </div>

      <button
        type="submit"
        disabled={addCategory.isLoading || updateCategory.isLoading}
        className="w-full bg-primarycolor text-white p-3 rounded disabled:opacity-50"
      >
        {(addCategory.isLoading || updateCategory.isLoading) ? 'Saving...' : 
          initialData ? 'Update Category' : 'Add Category'}
      </button>
    </form>
  );
}
