import { z } from 'zod';

// Common validation patterns
const phoneRegex = /^(\+254|0)[17]\d{8}$/; // Kenyan phone number format
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// User Authentication Schemas
export const loginSchema = z.object({
  email: z
    .string()
    .min(1, 'Email is required')
    .regex(emailRegex, 'Please enter a valid email address')
    .max(255, 'Email is too long'),
  password: z
    .string()
    .min(6, 'Password must be at least 6 characters')
    .max(128, 'Password is too long'),
  rememberMe: z.boolean().optional()
});

export const signupSchema = z.object({
  name: z
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name is too long')
    .regex(/^[a-zA-Z\u00C0-\u017F\s'-\.]+$/, 'Name contains invalid characters'),
  email: z
    .string()
    .min(1, 'Email is required')
    .regex(emailRegex, 'Please enter a valid email address')
    .max(255, 'Email is too long'),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password is too long')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number'),
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
});

// Profile Update Schema
export const profileUpdateSchema = z.object({
  name: z
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name is too long')
    .regex(/^[a-zA-Z\u00C0-\u017F\s'-\.]+$/, 'Name contains invalid characters'),
  email: z
    .string()
    .min(1, 'Email is required')
    .regex(emailRegex, 'Please enter a valid email address')
    .max(255, 'Email is too long'),
  phone: z
    .string()
    .regex(phoneRegex, 'Please enter a valid Kenyan phone number')
    .optional()
    .or(z.literal('')),
  address: z
    .string()
    .max(500, 'Address is too long')
    .optional()
    .or(z.literal(''))
});

// Product Schemas
export const productSchema = z.object({
  name: z
    .string()
    .min(1, 'Product name is required')
    .max(200, 'Product name is too long')
    .trim(),
  description: z
    .string()
    .min(10, 'Description must be at least 10 characters')
    .max(2000, 'Description is too long')
    .trim(),
  price: z
    .number()
    .positive('Price must be positive')
    .max(1000000, 'Price is too high')
    .multipleOf(0.01, 'Price must have at most 2 decimal places'),
  discount: z
    .number()
    .min(0, 'Discount cannot be negative')
    .max(100, 'Discount cannot exceed 100%')
    .optional()
    .default(0),
  quantity: z
    .number()
    .int('Quantity must be a whole number')
    .min(0, 'Quantity cannot be negative')
    .max(10000, 'Quantity is too high'),
  category_id: z
    .string()
    .uuid('Invalid category ID'),
  category_name: z
    .string()
    .min(1, 'Category name is required'),
  image_url: z
    .array(z.string().url('Invalid image URL'))
    .min(1, 'At least one image is required')
    .max(10, 'Maximum 10 images allowed'),
  is_promoted: z.boolean().optional().default(false),
  promotion_start_date: z
    .string()
    .datetime()
    .optional()
    .nullable(),
  promotion_end_date: z
    .string()
    .datetime()
    .optional()
    .nullable(),
  promotion_type: z
    .enum(['percentage', 'fixed_amount', 'buy_one_get_one'])
    .optional()
    .nullable()
});

// Category Schema
export const categorySchema = z.object({
  name: z
    .string()
    .min(1, 'Category name is required')
    .max(100, 'Category name is too long')
    .trim(),
  description: z
    .string()
    .max(500, 'Description is too long')
    .trim()
    .optional(),
  image_url: z
    .string()
    .url('Invalid image URL')
    .optional()
});

// Cart Item Schema
export const cartItemSchema = z.object({
  productId: z
    .string()
    .uuid('Invalid product ID'),
  quantity: z
    .number()
    .int('Quantity must be a whole number')
    .min(1, 'Quantity must be at least 1')
    .max(100, 'Maximum quantity is 100'),
  product: z.object({
    id: z.string().uuid(),
    name: z.string(),
    price: z.number().positive(),
    image_url: z.union([z.string().url(), z.array(z.string().url())]),
    is_promoted: z.boolean().optional()
  })
});

// Checkout/Order Schemas
export const billingDetailsSchema = z.object({
  fullName: z
    .string()
    .min(2, 'Full name must be at least 2 characters')
    .max(100, 'Full name is too long')
    .regex(/^[a-zA-Z\s]+$/, 'Name can only contain letters and spaces'),
  email: z
    .string()
    .regex(emailRegex, 'Please enter a valid email address'),
  phone: z
    .string()
    .regex(phoneRegex, 'Please enter a valid Kenyan phone number (e.g., +************ or 0712345678)'),
  county: z
    .string()
    .min(1, 'County is required'),
  street: z
    .string()
    .min(5, 'Street address must be at least 5 characters')
    .max(200, 'Street address is too long')
});

export const orderSchema = z.object({
  user_id: z
    .string()
    .uuid('Invalid user ID')
    .optional()
    .nullable(),
  total_amount: z
    .number()
    .positive('Total amount must be positive'),
  mpesa_code: z
    .string()
    .min(10, 'M-Pesa code must be at least 10 characters')
    .max(20, 'M-Pesa code is too long')
    .regex(/^[A-Z0-9]+$/, 'M-Pesa code can only contain uppercase letters and numbers'),
  delivery_cost: z
    .number()
    .min(0, 'Delivery cost cannot be negative'),
  billing_details: billingDetailsSchema,
  items: z
    .array(z.object({
      product_id: z.string().uuid(),
      quantity: z.number().int().positive(),
      price: z.number().positive(),
      product_name: z.string()
    }))
    .min(1, 'Order must contain at least one item')
});

// Search Schema
export const searchSchema = z.object({
  query: z
    .string()
    .min(1, 'Search query is required')
    .max(100, 'Search query is too long')
    .trim(),
  category: z
    .string()
    .optional(),
  minPrice: z
    .number()
    .min(0, 'Minimum price cannot be negative')
    .optional(),
  maxPrice: z
    .number()
    .positive('Maximum price must be positive')
    .optional(),
  sortBy: z
    .enum(['name', 'price', 'created_at', 'popularity'])
    .optional()
    .default('created_at'),
  sortOrder: z
    .enum(['asc', 'desc'])
    .optional()
    .default('desc')
}).refine((data) => {
  if (data.minPrice && data.maxPrice) {
    return data.minPrice <= data.maxPrice;
  }
  return true;
}, {
  message: "Minimum price cannot be greater than maximum price",
  path: ["maxPrice"]
});

// Contact/Support Schema
export const contactSchema = z.object({
  name: z
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name is too long'),
  email: z
    .string()
    .regex(emailRegex, 'Please enter a valid email address'),
  subject: z
    .string()
    .min(5, 'Subject must be at least 5 characters')
    .max(200, 'Subject is too long'),
  message: z
    .string()
    .min(10, 'Message must be at least 10 characters')
    .max(1000, 'Message is too long')
});

// Admin User Schema
export const adminUserSchema = z.object({
  name: z
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name is too long'),
  email: z
    .string()
    .regex(emailRegex, 'Please enter a valid email address'),
  role: z
    .enum(['admin', 'user'])
    .default('user')
});

// User Address Schema
export const userAddressSchema = z.object({
  title: z
    .string()
    .min(1, 'Address title is required')
    .max(100, 'Title is too long'),
  full_name: z
    .string()
    .min(2, 'Full name must be at least 2 characters')
    .max(200, 'Full name is too long'),
  phone: z
    .string()
    .regex(phoneRegex, 'Please enter a valid Kenyan phone number'),
  street_address: z
    .string()
    .min(5, 'Street address must be at least 5 characters')
    .max(500, 'Street address is too long'),
  city: z
    .string()
    .min(2, 'City must be at least 2 characters')
    .max(100, 'City name is too long'),
  county: z
    .string()
    .min(2, 'County is required')
    .max(100, 'County name is too long'),
  postal_code: z
    .string()
    .max(20, 'Postal code is too long')
    .optional(),
  country: z
    .string()
    .default('Kenya'),
  is_default: z
    .boolean()
    .default(false)
});

// Payment Method Schema
export const paymentMethodSchema = z.object({
  type: z
    .enum(['mpesa', 'card', 'bank_transfer'], {
      errorMap: () => ({ message: 'Please select a valid payment method type' })
    }),
  title: z
    .string()
    .min(1, 'Payment method title is required')
    .max(100, 'Title is too long'),
  details: z
    .object({})
    .passthrough(), // Allow any additional properties
  is_default: z
    .boolean()
    .default(false)
});

// User Settings Schema
export const userSettingsSchema = z.object({
  notification_preferences: z
    .object({
      order_updates: z.boolean().default(true),
      promotions: z.boolean().default(true),
      newsletter: z.boolean().default(false),
      sms_notifications: z.boolean().default(true),
      email_notifications: z.boolean().default(true)
    })
    .optional(),
  privacy_settings: z
    .object({
      profile_visibility: z.enum(['public', 'private']).default('private'),
      show_online_status: z.boolean().default(false)
    })
    .optional(),
  display_preferences: z
    .object({
      theme: z.enum(['light', 'dark', 'system']).default('system'),
      language: z.string().default('en'),
      currency: z.string().default('KES')
    })
    .optional()
});

// Security Settings Schema
export const securitySettingsSchema = z.object({
  current_password: z
    .string()
    .min(1, 'Current password is required'),
  new_password: z
    .string()
    .min(8, 'New password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number'),
  confirm_password: z
    .string()
}).refine((data) => data.new_password === data.confirm_password, {
  message: "Passwords don't match",
  path: ["confirm_password"]
});

// Utility functions for validation
export const validateData = (schema, data) => {
  try {
    return {
      success: true,
      data: schema.parse(data),
      errors: null
    };
  } catch (error) {
    return {
      success: false,
      data: null,
      errors: error.errors
    };
  }
};

export const getFieldError = (errors, fieldName) => {
  if (!errors) return null;
  const fieldError = errors.find(error => error.path.includes(fieldName));
  return fieldError ? fieldError.message : null;
};

// Sanitization helpers
export const sanitizeString = (str) => {
  if (typeof str !== 'string') return str;
  return str.trim().replace(/[<>]/g, '');
};

export const sanitizeObject = (obj, schema) => {
  const result = validateData(schema, obj);
  if (result.success) {
    return result.data;
  }
  throw new Error('Validation failed: ' + result.errors.map(e => e.message).join(', '));
};
