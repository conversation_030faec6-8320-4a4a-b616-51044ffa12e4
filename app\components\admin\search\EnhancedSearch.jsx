"use client";

import React, { useState, useEffect, useRef } from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  Search, 
  Filter, 
  X, 
  Clock, 
  TrendingUp,
  Package,
  Users,
  ShoppingCart,
  Tag,
  Calendar,
  DollarSign,
  ArrowRight
} from 'lucide-react';
import { supabase } from '../../../lib/supabase';
import { formatCurrency, formatDate } from '../../../lib/utils';
import { cn } from '../../../lib/utils';

const SEARCH_CATEGORIES = {
  all: { label: 'All', icon: Search },
  products: { label: 'Products', icon: Package },
  orders: { label: 'Orders', icon: ShoppingCart },
  customers: { label: 'Customers', icon: Users },
  categories: { label: 'Categories', icon: Tag }
};

export default function EnhancedSearch({ onResultSelect, className }) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [recentSearches, setRecentSearches] = useState([]);
  const searchRef = useRef(null);
  const resultsRef = useRef(null);

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('admin-recent-searches');
    if (saved) {
      setRecentSearches(JSON.parse(saved));
    }
  }, []);

  // Save search term to recent searches
  const saveRecentSearch = (term, category) => {
    if (!term.trim()) return;
    
    const newSearch = {
      term: term.trim(),
      category,
      timestamp: Date.now()
    };
    
    const updated = [newSearch, ...recentSearches.filter(s => 
      s.term !== newSearch.term || s.category !== newSearch.category
    )].slice(0, 10);
    
    setRecentSearches(updated);
    localStorage.setItem('admin-recent-searches', JSON.stringify(updated));
  };

  // Enhanced search query
  const { data: searchResults = [], isLoading } = useQuery({
    queryKey: ['admin-search', searchTerm, selectedCategory],
    queryFn: async () => {
      if (!searchTerm.trim()) return [];

      const results = [];
      const term = searchTerm.toLowerCase();

      try {
        // Search Products
        if (selectedCategory === 'all' || selectedCategory === 'products') {
          const { data: products } = await supabase
            .from('products')
            .select(`
              id, name, description, price, stock_quantity, 
              category_id, categories(name)
            `)
            .or(`name.ilike.%${term}%,description.ilike.%${term}%`)
            .limit(10);

          if (products) {
            results.push(...products.map(product => ({
              id: product.id,
              type: 'product',
              title: product.name,
              subtitle: product.categories?.name || 'Uncategorized',
              description: product.description,
              metadata: {
                price: product.price,
                stock: product.stock_quantity
              },
              url: `/admin/products?id=${product.id}`
            })));
          }
        }

        // Search Orders
        if (selectedCategory === 'all' || selectedCategory === 'orders') {
          const { data: orders } = await supabase
            .from('orders')
            .select(`
              id, total_amount, status, created_at, mpesa_code,
              billing_details
            `)
            .or(`id.ilike.%${term}%,mpesa_code.ilike.%${term}%`)
            .limit(10);

          if (orders) {
            results.push(...orders.map(order => ({
              id: order.id,
              type: 'order',
              title: `Order #${order.id.slice(-8).toUpperCase()}`,
              subtitle: `M-Pesa: ${order.mpesa_code}`,
              description: `${order.status} - ${formatCurrency(order.total_amount)}`,
              metadata: {
                status: order.status,
                amount: order.total_amount,
                date: order.created_at
              },
              url: `/admin/orders?id=${order.id}`
            })));
          }
        }

        // Search Customers
        if (selectedCategory === 'all' || selectedCategory === 'customers') {
          const { data: customers } = await supabase
            .from('users')
            .select('id, name, email, role, created_at')
            .or(`name.ilike.%${term}%,email.ilike.%${term}%`)
            .limit(10);

          if (customers) {
            results.push(...customers.map(customer => ({
              id: customer.id,
              type: 'customer',
              title: customer.name || 'Unnamed User',
              subtitle: customer.email,
              description: `${customer.role} - Joined ${formatDate(customer.created_at)}`,
              metadata: {
                role: customer.role,
                joinDate: customer.created_at
              },
              url: `/admin/customers?id=${customer.id}`
            })));
          }
        }

        // Search Categories
        if (selectedCategory === 'all' || selectedCategory === 'categories') {
          const { data: categories } = await supabase
            .from('categories')
            .select('id, name, description')
            .ilike('name', `%${term}%`)
            .limit(5);

          if (categories) {
            results.push(...categories.map(category => ({
              id: category.id,
              type: 'category',
              title: category.name,
              subtitle: 'Category',
              description: category.description || 'No description',
              metadata: {},
              url: `/admin/categories?id=${category.id}`
            })));
          }
        }

        return results;
      } catch (error) {
        console.error('Search error:', error);
        return [];
      }
    },
    enabled: searchTerm.length >= 2,
    staleTime: 30 * 1000, // 30 seconds
  });

  // Handle search input
  const handleSearch = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    setIsOpen(value.length >= 2 || recentSearches.length > 0);
  };

  // Handle result selection
  const handleResultSelect = (result) => {
    saveRecentSearch(searchTerm, selectedCategory);
    setIsOpen(false);
    setSearchTerm('');
    
    if (onResultSelect) {
      onResultSelect(result);
    } else {
      // Navigate to the result URL
      window.location.href = result.url;
    }
  };

  // Handle recent search selection
  const handleRecentSearchSelect = (recentSearch) => {
    setSearchTerm(recentSearch.term);
    setSelectedCategory(recentSearch.category);
    setIsOpen(true);
  };

  // Clear recent searches
  const clearRecentSearches = () => {
    setRecentSearches([]);
    localStorage.removeItem('admin-recent-searches');
  };

  // Close search when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  const getTypeIcon = (type) => {
    switch (type) {
      case 'product': return Package;
      case 'order': return ShoppingCart;
      case 'customer': return Users;
      case 'category': return Tag;
      default: return Search;
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'product': return 'text-blue-600 bg-blue-50';
      case 'order': return 'text-green-600 bg-green-50';
      case 'customer': return 'text-purple-600 bg-purple-50';
      case 'category': return 'text-orange-600 bg-orange-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div ref={searchRef} className={cn('relative', className)}>
      {/* Search Input */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-4 w-4 text-gray-400" />
        </div>
        <input
          type="text"
          value={searchTerm}
          onChange={handleSearch}
          onFocus={() => setIsOpen(searchTerm.length >= 2 || recentSearches.length > 0)}
          placeholder="Search products, orders, customers..."
          className="block w-full pl-10 pr-12 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primarycolor focus:border-transparent"
        />
        {searchTerm && (
          <button
            onClick={() => {
              setSearchTerm('');
              setIsOpen(false);
            }}
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
          >
            <X className="h-4 w-4 text-gray-400 hover:text-gray-600" />
          </button>
        )}
      </div>

      {/* Search Results Dropdown */}
      {isOpen && (
        <div 
          ref={resultsRef}
          className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto"
        >
          {/* Category Filter */}
          <div className="p-3 border-b border-gray-100">
            <div className="flex gap-2 overflow-x-auto">
              {Object.entries(SEARCH_CATEGORIES).map(([key, category]) => {
                const Icon = category.icon;
                return (
                  <button
                    key={key}
                    onClick={() => setSelectedCategory(key)}
                    className={cn(
                      'flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium whitespace-nowrap',
                      selectedCategory === key
                        ? 'bg-primarycolor text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    )}
                  >
                    <Icon className="w-3 h-3" />
                    {category.label}
                  </button>
                );
              })}
            </div>
          </div>

          {/* Search Results */}
          {searchTerm.length >= 2 && (
            <div className="p-2">
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primarycolor"></div>
                </div>
              ) : searchResults.length > 0 ? (
                <div className="space-y-1">
                  {searchResults.map((result) => {
                    const Icon = getTypeIcon(result.type);
                    return (
                      <button
                        key={`${result.type}-${result.id}`}
                        onClick={() => handleResultSelect(result)}
                        className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 text-left"
                      >
                        <div className={cn('p-2 rounded-lg', getTypeColor(result.type))}>
                          <Icon className="w-4 h-4" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-gray-900 truncate">
                            {result.title}
                          </div>
                          <div className="text-sm text-gray-600 truncate">
                            {result.subtitle}
                          </div>
                          <div className="text-xs text-gray-500 truncate">
                            {result.description}
                          </div>
                        </div>
                        <ArrowRight className="w-4 h-4 text-gray-400" />
                      </button>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Search className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-600">No results found for &quot;{searchTerm}&quot;</p>
                  <p className="text-sm text-gray-500">Try adjusting your search terms</p>
                </div>
              )}
            </div>
          )}

          {/* Recent Searches */}
          {searchTerm.length < 2 && recentSearches.length > 0 && (
            <div className="p-2">
              <div className="flex items-center justify-between px-2 py-1 mb-2">
                <h4 className="text-sm font-medium text-gray-700 flex items-center gap-2">
                  <Clock className="w-3 h-3" />
                  Recent Searches
                </h4>
                <button
                  onClick={clearRecentSearches}
                  className="text-xs text-gray-500 hover:text-gray-700"
                >
                  Clear
                </button>
              </div>
              <div className="space-y-1">
                {recentSearches.slice(0, 5).map((recent, index) => {
                  const categoryConfig = SEARCH_CATEGORIES[recent.category];
                  const Icon = categoryConfig.icon;
                  return (
                    <button
                      key={index}
                      onClick={() => handleRecentSearchSelect(recent)}
                      className="w-full flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 text-left"
                    >
                      <Icon className="w-4 h-4 text-gray-400" />
                      <div className="flex-1">
                        <span className="text-sm text-gray-900">{recent.term}</span>
                        <span className="text-xs text-gray-500 ml-2">
                          in {categoryConfig.label}
                        </span>
                      </div>
                      <span className="text-xs text-gray-400">
                        {new Date(recent.timestamp).toLocaleDateString()}
                      </span>
                    </button>
                  );
                })}
              </div>
            </div>
          )}

          {/* Search Tips */}
          {searchTerm.length < 2 && recentSearches.length === 0 && (
            <div className="p-4 text-center">
              <Search className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-600 mb-1">Start typing to search</p>
              <p className="text-xs text-gray-500">
                Search across products, orders, customers, and categories
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
