"use client";

import React from 'react';
import Image from 'next/image';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  Eye,
  MoreHorizontal,
  Calendar,
  Filter,
  BarChart3,
  PieChart
} from 'lucide-react';
import Card, { CardHeader, CardTitle, CardContent, StatsCard } from '../ui/Card';
import Button, { IconButton } from '../ui/Button';
import { formatCurrency, formatDate } from '../../lib/utils';
import { cn } from '../../lib/utils';
import {
  SalesOverviewChart,
  OrderStatusChart,
  TopProductsChart,
  CategoryPerformanceChart,
  RevenueComparisonChart
} from './charts/AdminCharts';

export default function EnhancedDashboard({ dashboardData, isLoading }) {
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded-lg animate-pulse" />
          ))}
        </div>
        <div className="h-96 bg-gray-200 rounded-lg animate-pulse" />
      </div>
    );
  }

  const stats = [
    {
      title: 'Total Revenue',
      value: formatCurrency(dashboardData?.totals?.confirmed_sales || 0),
      change: '+12.5%',
      changeType: 'positive',
      icon: DollarSign,
      description: 'From confirmed orders'
    },
    {
      title: 'Pending Revenue',
      value: formatCurrency(dashboardData?.totals?.pending_sales || 0),
      change: '+8.2%',
      changeType: 'positive',
      icon: TrendingUp,
      description: 'Awaiting confirmation'
    },
    {
      title: 'Total Orders',
      value: dashboardData?.totals?.total_orders || 0,
      change: '+15.3%',
      changeType: 'positive',
      icon: ShoppingCart,
      description: 'All time orders'
    },
    {
      title: 'Active Products',
      value: dashboardData?.totals?.active_products || 0,
      change: '+2.1%',
      changeType: 'positive',
      icon: Package,
      description: 'Currently listed'
    }
  ];

  const recentOrders = dashboardData?.recentOrders || [];
  const topProducts = dashboardData?.topProducts || [];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Welcome back! Here&apos;s what&apos;s happening with your store today.
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button variant="outline" leftIcon={<Calendar className="w-4 h-4" />}>
            Last 30 days
          </Button>
          <Button variant="outline" leftIcon={<Filter className="w-4 h-4" />}>
            Filter
          </Button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <StatsCard
            key={index}
            title={stat.title}
            value={stat.value}
            change={stat.change}
            changeType={stat.changeType}
            icon={<stat.icon className="w-6 h-6" />}
          />
        ))}
      </div>

      {/* Charts and Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sales Overview Chart */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5 text-primarycolor" />
                  Sales Overview
                </CardTitle>
                <p className="text-sm text-gray-600 mt-1">
                  Daily sales performance over the last 30 days
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <Calendar className="w-4 h-4 mr-2" />
                  Last 30 days
                </Button>
                <Button variant="outline" size="sm">
                  <Filter className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <SalesOverviewChart salesData={dashboardData?.recent_orders || []} />
            </div>
          </CardContent>
        </Card>

        {/* Order Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="w-5 h-5 text-primarycolor" />
              Order Status
            </CardTitle>
            <p className="text-sm text-gray-600">
              Distribution of order statuses
            </p>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <OrderStatusChart orders={dashboardData?.recent_orders || []} />
            </div>
          </CardContent>
        </Card>

        {/* Top Products */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-primarycolor" />
              Top Products
            </CardTitle>
            <p className="text-sm text-gray-600">
              Most wishlisted products
            </p>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <TopProductsChart products={dashboardData?.top_products || []} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Additional Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Comparison */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="w-5 h-5 text-primarycolor" />
              Monthly Revenue
            </CardTitle>
            <p className="text-sm text-gray-600">
              Revenue trends over the last 12 months
            </p>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <RevenueComparisonChart orders={dashboardData?.recent_orders || []} />
            </div>
          </CardContent>
        </Card>

        {/* Category Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="w-5 h-5 text-primarycolor" />
              Category Performance
            </CardTitle>
            <p className="text-sm text-gray-600">
              Wishlist distribution by category
            </p>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <CategoryPerformanceChart products={dashboardData?.top_products || []} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Orders */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Recent Orders</CardTitle>
              <Button variant="ghost" size="sm">
                View All
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentOrders.length > 0 ? (
                recentOrders.slice(0, 5).map((order, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-primarycolor-100 rounded-full flex items-center justify-center">
                        <ShoppingCart className="w-5 h-5 text-primarycolor" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">
                          Order #{order.id?.slice(0, 8) || 'N/A'}
                        </p>
                        <p className="text-sm text-gray-600">
                          {order.user_name || 'Customer'} • {formatDate(order.created_at)}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900">
                        {formatCurrency(order.total_amount || 0)}
                      </p>
                      <span className={cn(
                        "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",
                        order.status === 'confirmed' 
                          ? "bg-green-100 text-green-800"
                          : order.status === 'pending'
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-gray-100 text-gray-800"
                      )}>
                        {order.status || 'pending'}
                      </span>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <ShoppingCart className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-600">No recent orders</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Top Products */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Top Products</CardTitle>
              <Button variant="ghost" size="sm">
                View All
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topProducts.length > 0 ? (
                topProducts.slice(0, 5).map((product, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-gray-100 rounded-lg overflow-hidden">
                        {product.image_url ? (
                          <Image
                            src={Array.isArray(product.image_url) ? product.image_url[0] : product.image_url}
                            alt={product.name}
                            width={48}
                            height={48}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-primarycolor-100 flex items-center justify-center">
                            <Package className="w-6 h-6 text-primarycolor" />
                          </div>
                        )}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 line-clamp-1">
                          {product.name}
                        </p>
                        <p className="text-sm text-gray-600">
                          {product.sales_count || 0} sold
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900">
                        {formatCurrency(product.price || 0)}
                      </p>
                      <p className="text-sm text-gray-600">
                        {formatCurrency((product.price || 0) * (product.sales_count || 0))} revenue
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <Package className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-600">No product data available</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" className="h-20 flex-col gap-2">
              <Package className="w-6 h-6" />
              <span>Add Product</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col gap-2">
              <ShoppingCart className="w-6 h-6" />
              <span>View Orders</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col gap-2">
              <Users className="w-6 h-6" />
              <span>Manage Users</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col gap-2">
              <TrendingUp className="w-6 h-6" />
              <span>Analytics</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
