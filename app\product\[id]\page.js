"use client";
import React, { useEffect, useState, useCallback } from "react";
import { useContext } from "react";
import { WishlistContext } from "../../context/wishlistContext";
import { useSupabase } from "../../hooks/useSupabase";
import { useParams, useRouter } from "next/navigation";
import Image from "next/image";
import { ChevronLeft, Heart, ShoppingCart } from "lucide-react";
import { useAuth } from "../../hooks/useAuth";
import { useCart } from "../../context/cartContext";
import { useProduct } from "../../hooks/useQueries";
import { toast } from "sonner";
import * as fbq from "../../utils/facebookPixel";
import { toTitleCase } from "../../lib/utils";
import TruncatedDescription from "../../components/TruncatedDescription";

export default function ProductPage() {
  const { cartItems, updateCart } = useCart();
  const { user } = useAuth();
  const [quantity, setQuantity] = useState(1);
  const { isInWishlist, toggleWishlistItem } = useContext(WishlistContext);
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const params = useParams();
  const router = useRouter();

  const { data: product, isLoading } = useProduct(params.id);

  // In the handleAddToCart function
  const handleAddToCart = async () => {
    if (isAddingToCart || !product) return;
    setIsAddingToCart(true);

    try {
      const discountedPrice = product.price * (1 - product.discount / 100);
      const productToAdd = {
        id: product.id,
        name: product.name,
        price: discountedPrice,
        image_url: product.image_url,
        is_promoted: false, // Regular products are not promoted
        discount: product.discount,
      };

      const existingItem = cartItems.find(
        (item) => item.product.id === product.id
      );
      let updatedCart;

      if (existingItem) {
        updatedCart = cartItems.map((item) =>
          item.product.id === product.id
            ? {
                ...item,
                quantity: item.quantity + quantity,
                total_amount: discountedPrice * (item.quantity + quantity),
              }
            : item
        );
      } else {
        const newItem = {
          productId: product.id,
          quantity,
          total_amount: discountedPrice * quantity,
          product: productToAdd,
        };
        updatedCart = [...cartItems, newItem];
      }

      updateCart(updatedCart);
      toast.success("Product added to cart successfully!");
    } catch (error) {
      console.error("Error adding to cart:", error);
      toast.error("Failed to add product to cart");
    } finally {
      setIsAddingToCart(false);
    }
  };

  const handleWishlistClick = useCallback(() => {
    toggleWishlistItem(product);
  }, [toggleWishlistItem, product]);

  if (isLoading)
    return <p className="text-primarycolor text-center">Loading...</p>;
  if (!product)
    return <p className="text-primarycolor text-center">Product not found</p>;

  return (
    <div className="min-h-screen bg-white">
      {/* Modern Header */}
      <div className="bg-white border-b border-gray-100 sticky top-0 z-10">
        <div className="flex justify-between items-center p-4 max-w-6xl mx-auto">
          <button
            onClick={() => router.back()}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ChevronLeft className="w-5 h-5" />
            <span className="text-sm font-medium">Back</span>
          </button>
          <h1 className="text-lg font-semibold text-gray-900">
            Product Details
          </h1>
          <button
            onClick={handleWishlistClick}
            className="w-10 h-10 bg-gray-50 rounded-full flex items-center justify-center hover:bg-gray-100 transition-colors"
          >
            <Heart
              className={`w-5 h-5 ${
                isInWishlist(product?.id)
                  ? "fill-red-500 text-red-500"
                  : "text-gray-600"
              }`}
            />
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto grid lg:grid-cols-2 gap-8 p-4 lg:p-8">
        {/* Product Image */}
        <div className="space-y-4">
          {/* Main Image */}
          <div className="relative aspect-square bg-gray-50 rounded-2xl overflow-hidden">
            <Image
              src={
                Array.isArray(product.image_url)
                  ? product.image_url[currentImageIndex]
                  : product.image_url
              }
              alt={product.name}
              fill
              priority
              className="object-contain p-4"
              sizes="(max-width: 1024px) 100vw, 50vw"
            />
          </div>

          {/* Thumbnail Gallery */}
          {Array.isArray(product.image_url) && product.image_url.length > 1 && (
            <div className="flex gap-3 overflow-x-auto pb-2">
              {product.image_url.map((url, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentImageIndex(index)}
                  className={`relative w-20 h-20 rounded-xl overflow-hidden border-2 flex-shrink-0 transition-all ${
                    currentImageIndex === index
                      ? "border-primarycolor"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                >
                  <Image
                    src={url}
                    alt={`${product.name} - Image ${index + 1}`}
                    fill
                    className="object-contain p-1"
                    sizes="80px"
                  />
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className="flex flex-col space-y-6">
          {/* Product Title */}
          <div>
            <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">
              {toTitleCase(product.name)}
            </h1>
            {product.category_name && (
              <p className="text-gray-600">{product.category_name}</p>
            )}
          </div>

          {/* Price Section */}
          <div className="space-y-2">
            <div className="flex items-baseline gap-3">
              {product.discount > 0 ? (
                <>
                  <span className="text-3xl font-bold text-gray-900">
                    Ksh. {Math.round(Number(product.price) * (1 - product.discount / 100)).toLocaleString()}
                  </span>
                  <span className="text-xl text-gray-500 line-through">
                    Ksh. {Math.round(Number(product.price)).toLocaleString()}
                  </span>
                  <span className="bg-red-100 text-red-800 px-2 py-1 rounded-lg text-sm font-medium">
                    -{product.discount}% OFF
                  </span>
                </>
              ) : (
                <span className="text-3xl font-bold text-gray-900">
                  Ksh. {Math.round(Number(product.price)).toLocaleString()}
                </span>
              )}
            </div>
            {product.discount > 0 && (
              <p className="text-green-600 font-medium">
                You save Ksh. {Math.round(Number(product.price) * (product.discount / 100)).toLocaleString()}
              </p>
            )}
          </div>

          {/* Product Description */}
          {product.description && (
            <div className="space-y-3">
              <h3 className="text-lg font-semibold text-gray-900">Description</h3>
              <TruncatedDescription
                description={product.description}
                maxLines={4}
                characterLimit={100}
                className="text-gray-600"
              />
            </div>
          )}

          {/* Quantity Selector */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-900">Quantity</label>
            <div className="flex items-center gap-3">
              <div className="flex items-center border border-gray-300 rounded-lg">
                <button
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  className="w-10 h-10 flex items-center justify-center text-gray-600 hover:bg-gray-50 rounded-l-lg transition-colors"
                >
                  -
                </button>
                <span className="w-12 h-10 flex items-center justify-center border-x border-gray-300 text-gray-900 font-medium">
                  {quantity}
                </span>
                <button
                  onClick={() => setQuantity(quantity + 1)}
                  className="w-10 h-10 flex items-center justify-center text-gray-600 hover:bg-gray-50 rounded-r-lg transition-colors"
                >
                  +
                </button>
              </div>
              <span className="text-sm text-gray-500">
                {product.quantity > 0 ? `${product.quantity} available` : 'Out of stock'}
              </span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <button
              onClick={() => {
                handleAddToCart();
                fbq.standardEvent("InitiateCheckout", {
                  content_ids: [product.id],
                  content_name: product.name,
                  value: product.price * quantity,
                  currency: "KES",
                });
                router.push("/checkout");
              }}
              disabled={product.quantity === 0 || isAddingToCart}
              className="w-full h-12 bg-primarycolor text-white hover:bg-primarycolor-600 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center gap-2 rounded-xl font-medium transition-colors"
            >
              {isAddingToCart ? "Processing..." : "Buy Now"}
            </button>

            <button
              onClick={() => {
                handleAddToCart();
                fbq.standardEvent("AddToCart", {
                  content_ids: [product.id],
                  content_name: product.name,
                  value: product.price * quantity,
                  currency: "KES",
                });
              }}
              disabled={isAddingToCart || product.quantity === 0}
              className="w-full h-12 bg-white border-2 border-primarycolor text-primarycolor hover:bg-primarycolor hover:text-white disabled:bg-gray-100 disabled:border-gray-300 disabled:text-gray-400 disabled:cursor-not-allowed flex items-center justify-center gap-2 rounded-xl font-medium transition-all"
            >
              <ShoppingCart className="w-5 h-5" />
              {isAddingToCart ? "Adding..." : "Add to Cart"}
            </button>
          </div>

          {/* Product Features */}
          <div className="space-y-4 pt-6 border-t border-gray-200">
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>Free Delivery</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>Secure Payment</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <span>Easy Returns</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                <span>24/7 Support</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
