"use client";

import React from 'react';
import { cn } from '../../lib/utils';

const cardVariants = {
  variant: {
    default: 'bg-white border border-gray-200 shadow-sm',
    elevated: 'bg-white border border-gray-200 shadow-md hover:shadow-lg',
    outlined: 'bg-white border-2 border-gray-200',
    filled: 'bg-gray-50 border border-gray-200',
    gradient: 'bg-gradient-to-br from-primarycolor-50 to-secondarycolor-50 border border-primarycolor-200',
  },
  padding: {
    none: 'p-0',
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6',
    xl: 'p-8',
  },
  radius: {
    none: 'rounded-none',
    sm: 'rounded-sm',
    md: 'rounded-md',
    lg: 'rounded-lg',
    xl: 'rounded-xl',
    '2xl': 'rounded-2xl',
  }
};

export default function Card({
  children,
  variant = 'default',
  padding = 'md',
  radius = 'lg',
  hover = false,
  clickable = false,
  className,
  onClick,
  ...props
}) {
  const baseClasses = 'transition-all duration-200';
  const variantClasses = cardVariants.variant[variant];
  const paddingClasses = cardVariants.padding[padding];
  const radiusClasses = cardVariants.radius[radius];
  
  const interactionClasses = cn(
    hover && 'hover:shadow-md hover:-translate-y-0.5',
    clickable && 'cursor-pointer hover:shadow-lg active:scale-[0.98]'
  );

  return (
    <div
      className={cn(
        baseClasses,
        variantClasses,
        paddingClasses,
        radiusClasses,
        interactionClasses,
        className
      )}
      onClick={clickable ? onClick : undefined}
      {...props}
    >
      {children}
    </div>
  );
}

// Card Header Component
export function CardHeader({ 
  children, 
  className,
  divider = false,
  ...props 
}) {
  return (
    <div 
      className={cn(
        'flex items-center justify-between',
        divider && 'border-b border-gray-200 pb-4 mb-4',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

// Card Title Component
export function CardTitle({ 
  children, 
  className,
  size = 'lg',
  ...props 
}) {
  const sizeClasses = {
    sm: 'text-sm font-medium',
    md: 'text-base font-semibold',
    lg: 'text-lg font-semibold',
    xl: 'text-xl font-bold',
    '2xl': 'text-2xl font-bold',
  };

  return (
    <h3 
      className={cn(
        'text-gray-900',
        sizeClasses[size],
        className
      )}
      {...props}
    >
      {children}
    </h3>
  );
}

// Card Description Component
export function CardDescription({ 
  children, 
  className,
  ...props 
}) {
  return (
    <p 
      className={cn(
        'text-sm text-gray-600 mt-1',
        className
      )}
      {...props}
    >
      {children}
    </p>
  );
}

// Card Content Component
export function CardContent({ 
  children, 
  className,
  ...props 
}) {
  return (
    <div 
      className={cn('flex-1', className)}
      {...props}
    >
      {children}
    </div>
  );
}

// Card Footer Component
export function CardFooter({ 
  children, 
  className,
  divider = false,
  justify = 'end',
  ...props 
}) {
  const justifyClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
    around: 'justify-around',
  };

  return (
    <div 
      className={cn(
        'flex items-center gap-2',
        justifyClasses[justify],
        divider && 'border-t border-gray-200 pt-4 mt-4',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

// Stats Card Component
export function StatsCard({
  title,
  value,
  change,
  changeType = 'neutral',
  icon,
  className,
  ...props
}) {
  const changeColors = {
    positive: 'text-success-600 bg-success-50',
    negative: 'text-error-600 bg-error-50',
    neutral: 'text-gray-600 bg-gray-50',
  };

  return (
    <Card variant="elevated" className={cn('relative overflow-hidden', className)} {...props}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
          {change && (
            <div className={cn(
              'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mt-2',
              changeColors[changeType]
            )}>
              {change}
            </div>
          )}
        </div>
        {icon && (
          <div className="flex-shrink-0 p-3 bg-primarycolor-50 rounded-lg">
            <div className="text-primarycolor-600">
              {icon}
            </div>
          </div>
        )}
      </div>
    </Card>
  );
}

// Feature Card Component
export function FeatureCard({
  icon,
  title,
  description,
  action,
  className,
  ...props
}) {
  return (
    <Card 
      variant="elevated" 
      hover 
      className={cn('text-center', className)} 
      {...props}
    >
      {icon && (
        <div className="flex justify-center mb-4">
          <div className="p-3 bg-primarycolor-50 rounded-lg">
            <div className="text-primarycolor-600">
              {icon}
            </div>
          </div>
        </div>
      )}
      
      <CardTitle size="md" className="mb-2">
        {title}
      </CardTitle>
      
      {description && (
        <CardDescription className="mb-4">
          {description}
        </CardDescription>
      )}
      
      {action && (
        <CardFooter justify="center">
          {action}
        </CardFooter>
      )}
    </Card>
  );
}

// Product Card Wrapper (for consistency)
export function ProductCard({
  children,
  className,
  ...props
}) {
  return (
    <Card
      variant="elevated"
      padding="none"
      hover
      clickable
      className={cn('overflow-hidden group', className)}
      {...props}
    >
      {children}
    </Card>
  );
}


