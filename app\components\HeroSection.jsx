"use client";

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowRight, ShoppingBag, Star, MapPin, Search } from 'lucide-react';
import Button from './ui/Button';
import Card from './ui/Card';
import { getCategoryPath } from '../lib/utils';
import { BannerImage } from './OptimizedImage';
import { cn } from '../lib/utils';

export default function HeroSection({ featuredProducts = [] }) {
  return (
    <section className="bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">





      </div>
    </section>
  );
}

// Modern Categories Section - Horizontal Grid
export function ModernCategories({ categories = [] }) {
  if (!categories.length) return null;

  // Category icons mapping - you can customize these
  const getCategoryIcon = (categoryName) => {
    const name = categoryName.toLowerCase();
    if (name.includes('phone') || name.includes('mobile')) return '📱';
    if (name.includes('laptop') || name.includes('computer')) return '💻';
    if (name.includes('camera')) return '📷';
    if (name.includes('audio') || name.includes('headphone')) return '🎧';
    if (name.includes('gaming') || name.includes('console')) return '🎮';
    if (name.includes('kitchen')) return '🍳';
    if (name.includes('electronics')) return '⚡';
    return '🛍️';
  };

  return (
    <section className="py-6 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Categories</h2>
          <Link
            href="/categories"
            className="text-sm text-gray-600 hover:text-primarycolor transition-colors flex items-center gap-1"
          >
            See all
            <ArrowRight className="w-4 h-4" />
          </Link>
        </div>

        <div className="overflow-x-auto scrollbar-hide">
          <div className="flex gap-4 sm:gap-6 lg:gap-8 xl:gap-10 pb-2" style={{ width: 'max-content' }}>
            {categories.map((category) => (
              <Link
                key={category.id}
                href={getCategoryPath(category)}
                className="group flex flex-col items-center flex-shrink-0"
              >
                <div className="w-14 h-14 sm:w-16 sm:h-16 lg:w-32 lg:h-20 xl:w-36 xl:h-24 bg-gray-50 rounded-xl sm:rounded-2xl lg:rounded-3xl flex items-center justify-center mb-2 sm:mb-3 lg:mb-4 group-hover:bg-primarycolor-50 transition-colors">
                  {category.image_url ? (
                    <Image
                      src={category.image_url}
                      alt={category.name}
                      width={48}
                      height={48}
                      className="w-7 h-7 sm:w-8 sm:h-8 lg:w-12 lg:h-12 xl:w-14 xl:h-14 object-contain"
                    />
                  ) : (
                    <span className="text-xl sm:text-2xl lg:text-4xl xl:text-5xl">{getCategoryIcon(category.name)}</span>
                  )}
                </div>
                <span className="text-xs sm:text-sm lg:text-base xl:text-lg text-gray-700 text-center font-medium group-hover:text-primarycolor transition-colors whitespace-nowrap max-w-[80px] sm:max-w-[90px] lg:max-w-[140px] xl:max-w-[160px] truncate">
                  {category.name}
                </span>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}

// Promotional Banner
export function PromotionalBanner({ 
  title = "Special Offer",
  subtitle = "Get up to 50% off on selected items",
  ctaText = "Shop Now",
  ctaLink = "/products",
  backgroundImage,
  className 
}) {
  return (
    <section className={cn("py-16 relative overflow-hidden", className)}>
      {backgroundImage && (
        <div className="absolute inset-0">
          <BannerImage
            src={backgroundImage}
            alt="Promotional Banner"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-primarycolor/80" />
        </div>
      )}
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            {title}
          </h2>
          <p className="text-xl text-primarycolor-100 mb-8">
            {subtitle}
          </p>
          <Button
            variant="secondary"
            size="lg"
            className="shadow-lg hover:shadow-xl"
            rightIcon={<ArrowRight className="w-5 h-5" />}
          >
            <Link href={ctaLink}>
              {ctaText}
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
