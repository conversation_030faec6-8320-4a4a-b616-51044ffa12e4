"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Mail, Lock, Eye, EyeOff, AlertCircle } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { useFormSubmission } from '../../hooks/useValidation';
import { loginSchema } from '../../lib/validations';
import Button from '../ui/Button';
import Input, { FormGroup } from '../ui/Input';
import AuthLayout, { SocialLoginButton, AuthDivider } from './AuthLayout';
import { setCookie, getCookie, hasCookie } from 'cookies-next';

export default function LoginForm() {
  const { signIn, userDetails, isLoading } = useAuth();
  const router = useRouter();
  const [rememberMe, setRememberMe] = useState(false);
  const [cookieConsent, setCookieConsent] = useState(false);

  // Form handling with validation
  const {
    data,
    errors,
    isValid,
    updateField,
    handleSubmit,
    isSubmitting,
    submitError,
    clearSubmitError
  } = useFormSubmission(loginSchema, async (formData) => {
    try {
      await signIn(formData.email, formData.password);
      
      // Handle remember me
      if (formData.rememberMe && cookieConsent) {
        setCookie('rememberedEmail', formData.email, { 
          maxAge: 30 * 24 * 60 * 60, // 30 days
          sameSite: 'strict'
        });
      }
    } catch (error) {
      throw new Error(error.message || 'Login failed. Please try again.');
    }
  });

  // Check cookie consent and load remembered email
  useEffect(() => {
    const checkCookieConsent = () => {
      if (hasCookie('cookieConsent')) {
        setCookieConsent(true);
        const rememberedEmail = getCookie('rememberedEmail');
        if (rememberedEmail) {
          updateField('email', rememberedEmail);
          setRememberMe(true);
        }
      }
    };
    checkCookieConsent();
  }, [updateField]);

  // Redirect after successful login
  useEffect(() => {
    if (userDetails && !isLoading) {
      const redirectPath = userDetails.role === 'admin' ? '/admin' : '/';
      router.replace(redirectPath);
    }
  }, [userDetails, router, isLoading]);

  const handleSocialLogin = (provider) => {
    // TODO: Implement social login
    console.log(`Login with ${provider}`);
  };

  return (
    <AuthLayout
      title="Welcome Back"
      subtitle="Sign in to your account to continue shopping"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Email Field */}
        <FormGroup
          label="Email Address"
          error={errors?.email}
          required
        >
          <Input
            type="email"
            placeholder="Enter your email"
            value={data.email || ''}
            onChange={(e) => updateField('email', e.target.value)}
            leftIcon={<Mail className="w-5 h-5" />}
            error={!!errors?.email}
            disabled={isSubmitting}
          />
        </FormGroup>

        {/* Password Field */}
        <FormGroup
          label="Password"
          error={errors?.password}
          required
        >
          <Input
            type="password"
            placeholder="Enter your password"
            value={data.password || ''}
            onChange={(e) => updateField('password', e.target.value)}
            leftIcon={<Lock className="w-5 h-5" />}
            error={!!errors?.password}
            disabled={isSubmitting}
          />
        </FormGroup>

        {/* Remember Me & Forgot Password */}
        <div className="flex items-center justify-between">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={rememberMe}
              onChange={(e) => {
                setRememberMe(e.target.checked);
                updateField('rememberMe', e.target.checked);
              }}
              className="w-4 h-4 text-primarycolor border-gray-300 rounded focus:ring-primarycolor focus:ring-2"
              disabled={isSubmitting}
            />
            <span className="ml-2 text-sm text-gray-600">Remember me</span>
          </label>
          
          <Link
            href="/auth/forgot-password"
            className="text-sm text-primarycolor hover:text-primarycolor-700 font-medium"
          >
            Forgot password?
          </Link>
        </div>

        {/* Submit Error */}
        {submitError && (
          <div className="flex items-center gap-2 p-3 bg-error-50 border border-error-200 rounded-lg text-error-700">
            <AlertCircle className="w-5 h-5 flex-shrink-0" />
            <span className="text-sm">{submitError}</span>
            <button
              type="button"
              onClick={clearSubmitError}
              className="ml-auto text-error-500 hover:text-error-700"
            >
              ×
            </button>
          </div>
        )}

        {/* Submit Button */}
        <Button
          type="submit"
          fullWidth
          size="lg"
          loading={isSubmitting || isLoading}
          disabled={!isValid}
        >
          Sign In
        </Button>

        {/* Social Login */}
        <AuthDivider />
        
        <div className="space-y-3">
          <SocialLoginButton
            provider="Google"
            icon={
              <svg className="w-5 h-5" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
            }
            onClick={() => handleSocialLogin('Google')}
            disabled={isSubmitting}
          />
          
          <SocialLoginButton
            provider="Facebook"
            icon={
              <svg className="w-5 h-5" fill="#1877F2" viewBox="0 0 24 24">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
            }
            onClick={() => handleSocialLogin('Facebook')}
            disabled={isSubmitting}
          />
        </div>

        {/* Sign Up Link */}
        <div className="text-center pt-4 border-t border-gray-200">
          <p className="text-sm text-gray-600">
            Don&apos;t have an account?{' '}
            <Link
              href="/auth/signup"
              className="font-medium text-primarycolor hover:text-primarycolor-700 transition-colors"
            >
              Sign up for free
            </Link>
          </p>
        </div>
      </form>
    </AuthLayout>
  );
}
