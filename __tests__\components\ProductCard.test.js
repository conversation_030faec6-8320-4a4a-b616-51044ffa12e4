import { render, screen, fireEvent, waitFor } from '../utils/test-utils'
import ProductCard from '../../app/components/productcard'
import { mockProduct, mockPromotedProduct, mockUser } from '../utils/test-utils'

// Mock the WishlistContext
const mockToggleWishlistItem = jest.fn()
const mockIsInWishlist = jest.fn()

jest.mock('../../app/context/wishlistContext', () => ({
  WishlistContext: {
    Consumer: ({ children }) => children({
      toggleWishlistItem: mockToggleWishlistItem,
      isInWishlist: mockIsInWishlist,
    }),
  },
}))

describe('ProductCard', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockIsInWishlist.mockReturnValue(false)
  })

  it('renders product information correctly', () => {
    render(<ProductCard product={mockProduct} />)
    
    expect(screen.getByText(mockProduct.name)).toBeInTheDocument()
    expect(screen.getByText(`Ksh. ${Math.floor(mockProduct.price)}`)).toBeInTheDocument()
    expect(screen.getByRole('img', { name: mockProduct.name })).toBeInTheDocument()
  })

  it('displays discount information for discounted products', () => {
    const discountedProduct = { ...mockProduct, discount: 20 }
    render(<ProductCard product={discountedProduct} />)
    
    expect(screen.getByText('SAVE 20%')).toBeInTheDocument()
    
    // Should show both original and discounted price
    const originalPrice = Math.floor(mockProduct.price)
    const discountedPrice = Math.floor(mockProduct.price * 0.8)
    
    expect(screen.getByText(`Ksh. ${originalPrice}`)).toBeInTheDocument()
    expect(screen.getByText(`Ksh. ${discountedPrice}`)).toBeInTheDocument()
  })

  it('handles wishlist toggle correctly', async () => {
    render(<ProductCard product={mockProduct} />, { 
      user: mockUser, 
      isAuthenticated: true 
    })
    
    const wishlistButton = screen.getByRole('button')
    fireEvent.click(wishlistButton)
    
    await waitFor(() => {
      expect(mockToggleWishlistItem).toHaveBeenCalledWith(mockProduct)
    })
  })

  it('shows filled heart when product is in wishlist', () => {
    mockIsInWishlist.mockReturnValue(true)
    
    render(<ProductCard product={mockProduct} />, { 
      user: mockUser, 
      isAuthenticated: true 
    })
    
    const heartIcon = screen.getByRole('button').querySelector('svg')
    expect(heartIcon).toHaveClass('fill-warningcolor')
  })

  it('shows empty heart when product is not in wishlist', () => {
    mockIsInWishlist.mockReturnValue(false)
    
    render(<ProductCard product={mockProduct} />, { 
      user: mockUser, 
      isAuthenticated: true 
    })
    
    const heartIcon = screen.getByRole('button').querySelector('svg')
    expect(heartIcon).not.toHaveClass('fill-warningcolor')
  })

  it('navigates to product page when clicked', () => {
    render(<ProductCard product={mockProduct} />)
    
    const productLink = screen.getByRole('link')
    expect(productLink).toHaveAttribute('href', `/product/${mockProduct.id}`)
  })

  it('handles array of images correctly', () => {
    const productWithImageArray = {
      ...mockProduct,
      image_url: ['image1.jpg', 'image2.jpg']
    }
    
    render(<ProductCard product={productWithImageArray} />)
    
    const image = screen.getByRole('img')
    expect(image).toHaveAttribute('src', expect.stringContaining('image1.jpg'))
  })

  it('handles single image string correctly', () => {
    const productWithSingleImage = {
      ...mockProduct,
      image_url: 'single-image.jpg'
    }
    
    render(<ProductCard product={productWithSingleImage} />)
    
    const image = screen.getByRole('img')
    expect(image).toHaveAttribute('src', expect.stringContaining('single-image.jpg'))
  })

  it('prevents event propagation when wishlist button is clicked', async () => {
    const mockPreventDefault = jest.fn()
    
    render(<ProductCard product={mockProduct} />, { 
      user: mockUser, 
      isAuthenticated: true 
    })
    
    const wishlistButton = screen.getByRole('button')
    
    // Create a mock event
    const mockEvent = {
      preventDefault: mockPreventDefault,
      stopPropagation: jest.fn(),
    }
    
    fireEvent.click(wishlistButton, mockEvent)
    
    await waitFor(() => {
      expect(mockToggleWishlistItem).toHaveBeenCalled()
    })
  })

  it('applies hover effects correctly', () => {
    render(<ProductCard product={mockProduct} />)
    
    const productCard = screen.getByRole('link')
    expect(productCard).toHaveClass('hover:shadow-md')
    
    const image = screen.getByRole('img')
    expect(image).toHaveClass('hover:scale-105')
  })

  it('displays promoted product correctly', () => {
    render(<ProductCard product={mockPromotedProduct} />)
    
    expect(screen.getByText(mockPromotedProduct.name)).toBeInTheDocument()
    // Add specific promoted product assertions if needed
  })

  it('handles missing product data gracefully', () => {
    const incompleteProduct = {
      id: 'test-id',
      name: 'Test Product',
      price: 100,
    }
    
    expect(() => {
      render(<ProductCard product={incompleteProduct} />)
    }).not.toThrow()
  })

  it('formats price correctly', () => {
    const productWithDecimalPrice = {
      ...mockProduct,
      price: 1234.56
    }
    
    render(<ProductCard product={productWithDecimalPrice} />)
    
    // Should floor the price
    expect(screen.getByText('Ksh. 1234')).toBeInTheDocument()
  })

  it('calculates discount price correctly', () => {
    const productWithDiscount = {
      ...mockProduct,
      price: 1000,
      discount: 25
    }
    
    render(<ProductCard product={productWithDiscount} />)
    
    // Original price should be shown with strikethrough
    expect(screen.getByText('Ksh. 1000')).toBeInTheDocument()
    
    // Discounted price should be 750 (25% off 1000)
    expect(screen.getByText('Ksh. 750')).toBeInTheDocument()
  })
})
