"use client";
import { useState } from "react";
import Image from "next/image";
import ProductForm from "../components/productform";
import { useSupabase } from "../hooks/useSupabase";
import { toast } from "sonner";
import {
  Copy,
  ExternalLink,
  ChevronRight,
  Package,
  ChevronLeft,
} from "lucide-react";

export default function ProductManagement() {
  const [currentPage, setCurrentPage] = useState(0);
  const [sortBy, setSortBy] = useState("all");
  const { useProducts, useDeleteProduct, useTotalProducts } = useSupabase();
  const { data: products, isLoading } = useProducts(currentPage, sortBy);
  const { data: totalCount = 0 } = useTotalProducts(sortBy);
  const deleteProduct = useDeleteProduct();
  const [selectedProduct, setSelectedProduct] = useState(null);

  const ITEMS_PER_PAGE = 10;
  const totalPages = Math.ceil(totalCount / ITEMS_PER_PAGE);

  const getPromotedProductLink = (productId) => {
    return `https://www.sherrysmerchants.com/ads/${productId}`;
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    toast.success("Link copied to clipboard!");
  };

  const handleDelete = async (id) => {
    const willDelete = window.confirm(
      "Are you sure you want to delete this product? This action cannot be undone."
    );

    if (willDelete) {
      try {
        await deleteProduct.mutateAsync(id);
        toast.success("Product deleted successfully");
      } catch (error) {
        toast.error("Error deleting product");
      }
    }
  };

  const filteredProducts = products;

  if (isLoading)
    return <div className="p-6 text-center text-primarycolor">Loading...</div>;

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6 text-primarycolor">
        Product Management
      </h1>
      <ProductForm product={selectedProduct} />

      <div className="mt-8">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-primarycolor">
            Product List ({totalCount} total)
          </h2>
          <select
            value={sortBy}
            onChange={(e) => {
              setSortBy(e.target.value);
              setCurrentPage(0);
            }}
            className="border border-primarycolor rounded-lg px-4 py-2 text-primarycolor"
          >
            <option value="all">All Products</option>
            <option value="promoted">Promoted Only</option>
            <option value="regular">Regular Products</option>
          </select>
        </div>

        {filteredProducts?.length === 0 ? (
          <div className="text-center py-16">
            <Package className="w-16 h-16 text-primarycolor/20 mx-auto mb-4" />
            <p className="text-lg text-primarycolor">No products found.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredProducts?.map((product) => (
              <div
                key={product.id}
                className="flex flex-col border border-primarycolor/20 p-6 rounded-lg shadow-sm bg-white"
              >
                <div className="relative w-full h-[300px] mb-6">
                  {Array.isArray(product.image_url) ? (
                    <div className="relative h-full">
                      <Image
                        src={product.image_url[0]}
                        alt={product.name}
                        fill
                        className="object-cover rounded-lg"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      />
                      {product.image_url.length > 1 && (
                        <div className="absolute bottom-2 right-2 bg-white/80 backdrop-blur-sm px-2 py-1 rounded-lg text-xs font-medium text-primarycolor">
                          +{product.image_url.length - 1} more
                        </div>
                      )}
                    </div>
                  ) : (
                    <Image
                      src={product.image_url}
                      alt={product.name}
                      fill
                      className="object-cover rounded-lg"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                  )}
                </div>

                <div className="flex-grow space-y-4">
                  <div>
                    <h3 className="font-medium text-xl text-primarycolor mb-2">
                      {product.name}
                    </h3>
                    <p className="text-lg font-semibold text-gray-600">
                      Ksh. {product.price}
                    </p>
                  </div>

                  {product.is_promoted && (
                    <div className="p-4 bg-warningcolor/10 rounded-lg space-y-4">
                      <div className="flex flex-col space-y-2">
                        <span className="text-warningcolor font-medium">
                          {product.promotion_type}
                        </span>
                        <span className="text-sm text-warningcolor">
                          Ends:{" "}
                          {new Date(
                            product.promotion_end_date
                          ).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex flex-col gap-3">
                        <button
                          onClick={() =>
                            copyToClipboard(getPromotedProductLink(product.id))
                          }
                          className="flex items-center justify-center gap-2 px-4 py-3 bg-white border-2 border-warningcolor text-warningcolor rounded-lg hover:bg-warningcolor hover:text-white transition-colors text-sm font-medium"
                        >
                          <Copy className="w-4 h-4" /> Copy Ad Link
                        </button>
                        <a
                          href={getPromotedProductLink(product.id)}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center justify-center gap-2 px-4 py-3 bg-white border-2 border-warningcolor text-warningcolor rounded-lg hover:bg-warningcolor hover:text-white transition-colors text-sm font-medium"
                        >
                          <ExternalLink className="w-4 h-4" /> Preview
                        </a>
                      </div>
                    </div>
                  )}

                  <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                    <div className="space-x-3">
                      <button
                        onClick={() => setSelectedProduct(product)}
                        className="text-blue-600 hover:text-blue-800 font-medium"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleDelete(product.id)}
                        disabled={deleteProduct.isLoading}
                        className="text-red-600 hover:text-red-800 font-medium"
                      >
                        Delete
                      </button>
                    </div>
                    <span className="text-sm font-medium text-gray-500">
                      Stock: {product.quantity}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        <div className="mt-8 flex items-center justify-between border-t border-primarycolor/20 pt-4">
          <div className="flex items-center gap-2">
            <span className="text-sm text-primarycolor">
              Showing {currentPage * ITEMS_PER_PAGE + 1} -{" "}
              {Math.min((currentPage + 1) * ITEMS_PER_PAGE, totalCount)} of{" "}
              {totalCount}
            </span>
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={() => setCurrentPage((p) => Math.max(0, p - 1))}
              disabled={currentPage === 0}
              className="p-2 border border-primarycolor rounded-lg disabled:opacity-50"
            >
              <ChevronLeft className="w-5 h-5 text-primarycolor" />
            </button>

            {[...Array(totalPages)]
              .map((_, idx) => (
                <button
                  key={idx}
                  onClick={() => setCurrentPage(idx)}
                  className={`w-10 h-10 rounded-lg ${
                    currentPage === idx
                      ? "bg-primarycolor text-white"
                      : "border border-primarycolor text-primarycolor"
                  }`}
                >
                  {idx + 1}
                </button>
              ))
              .slice(
                Math.max(0, currentPage - 2),
                Math.min(totalPages, currentPage + 3)
              )}

            <button
              onClick={() =>
                setCurrentPage((p) => Math.min(totalPages - 1, p + 1))
              }
              disabled={currentPage === totalPages - 1}
              className="p-2 border border-primarycolor rounded-lg disabled:opacity-50"
            >
              <ChevronRight className="w-5 h-5 text-primarycolor" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
