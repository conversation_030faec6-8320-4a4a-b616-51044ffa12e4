import { Suspense } from "react";
import ProfileLayout from "../../components/profile/ProfileLayout";
import OrdersPage from "../../components/profile/OrdersPage";

export default function OrdersPageRoute() {
  return (
    <Suspense fallback={<div>Loading orders...</div>}>
      <ProfileLayout 
        title="Orders" 
        subtitle="View and track your order history"
      >
        <OrdersPage />
      </ProfileLayout>
    </Suspense>
  );
}
