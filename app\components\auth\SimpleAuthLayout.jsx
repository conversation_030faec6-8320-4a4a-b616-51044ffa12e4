"use client";

import React from 'react';
import Link from 'next/link';

export default function SimpleAuthLayout({ 
  children, 
  title, 
  subtitle, 
  showBackButton = true 
}) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primarycolor-50 via-white to-secondarycolor-50 flex">
      {/* Left Side - Branding */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-primarycolor to-primarycolor-800 relative overflow-hidden">
        <div className="relative z-10 flex flex-col justify-center px-12 text-white">
          <div className="max-w-md">
            {/* Logo */}
            <div className="mb-8">
              <Link href="/" className="flex items-center gap-3">
                <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center">
                  <span className="text-2xl font-bold text-primarycolor">P</span>
                </div>
                <span className="text-2xl font-bold">Pinchez Merchants</span>
              </Link>
            </div>

            <h1 className="text-4xl font-bold mb-6">
              Welcome to Your Shopping Paradise
            </h1>
            
            <p className="text-primarycolor-100 text-lg mb-8 leading-relaxed">
              Join thousands of satisfied customers who trust us for quality products, 
              fast delivery, and exceptional service.
            </p>
          </div>
        </div>
      </div>

      {/* Right Side - Auth Form */}
      <div className="flex-1 flex flex-col justify-center px-6 sm:px-12 lg:px-16">
        <div className="w-full max-w-md mx-auto">
          {/* Back Button */}
          {showBackButton && (
            <div className="mb-8">
              <button
                onClick={() => window.history.back()}
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Back
              </button>
            </div>
          )}

          {/* Mobile Logo */}
          <div className="lg:hidden mb-8 text-center">
            <Link href="/" className="inline-flex items-center gap-3">
              <div className="w-10 h-10 bg-primarycolor rounded-xl flex items-center justify-center">
                <span className="text-xl font-bold text-white">P</span>
              </div>
              <span className="text-xl font-bold text-gray-900">Pinchez Merchants</span>
            </Link>
          </div>

          {/* Form Card */}
          <div className="bg-white p-8 rounded-lg shadow-md">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                {title}
              </h2>
              {subtitle && (
                <p className="text-gray-600">
                  {subtitle}
                </p>
              )}
            </div>

            {children}
          </div>

          {/* Footer */}
          <div className="mt-8 text-center text-sm text-gray-500">
            <p>
              By continuing, you agree to our{' '}
              <Link href="/terms" className="text-primarycolor hover:underline">
                Terms of Service
              </Link>{' '}
              and{' '}
              <Link href="/privacy" className="text-primarycolor hover:underline">
                Privacy Policy
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
