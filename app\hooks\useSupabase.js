import { supabase } from '../lib/supabase';

export const useSupabase = () => {
  // Product Operations
  const fetchProducts = async () => {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories!products_category_name_fkey (*)
      `);
    if (error) throw error;
    // Ensure image_url is always an array
    return data.map(product => ({
      ...product,
      image_url: Array.isArray(product.image_url) ? product.image_url : [product.image_url]
    }));
  };

  const fetchCategories = async () => {
    const { data, error } = await supabase
      .from('categories')
      .select('*');
    if (error) throw error;
    return data;
  };

  const fetchProductById = async (id) => {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories!products_category_name_fkey (
          id,
          name,
          description,
          image_url
        )
      `)
      .eq('id', id)
      .single();
    if (error) throw error;
    return {
      ...data,
      image_url: Array.isArray(data.image_url) ? data.image_url : [data.image_url]
    };
  };
  const fetchProductsByCategory = async (categorySlug) => {
    // Add this check
    if (!categorySlug) return [];
    
    // First get the category by slug
    const { data: category } = await supabase
      .from('categories')
      .select('name')
      .eq('slug', categorySlug.toLowerCase())
      .single();

    if (category) {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('category_name', category.name);
      if (error) throw error;
      return data;
    }
    return [];
  };
  // Cart Operations - Only for authenticated users
  const syncCartToDb = async (userId, cartItems) => {
    if (!userId) return;

    try {
      if (cartItems.length === 0) {
        // Clear all cart items for user
        const { error } = await supabase
          .from('cart')
          .delete()
          .eq('user_id', userId);

        if (error) throw error;
        return;
      }

      const cartData = cartItems.map(item => ({
        user_id: userId,
        product_id: item.productId,
        quantity: item.quantity,
        price_at_time: item.product.price,
        updated_at: new Date().toISOString()
      }));

      // Batch upsert for better performance
      const { error } = await supabase
        .from('cart')
        .upsert(cartData, {
          onConflict: 'user_id,product_id',
          ignoreDuplicates: false
        });

      if (error) throw error;

      // Clean up removed items
      const currentProductIds = cartItems.map(item => item.productId);
      if (currentProductIds.length > 0) {
        const { error: cleanupError } = await supabase
          .from('cart')
          .delete()
          .eq('user_id', userId)
          .not('product_id', 'in', `(${currentProductIds.join(',')})`);

        if (cleanupError) throw cleanupError;
      }
    } catch (error) {
      console.error('Error syncing cart to database:', error);
      throw error;
    }
  };
  const fetchCartItems = async (userId) => {
    if (!userId) return [];
    const { data, error } = await supabase
      .from('cart')
      .select('*, products(*)')
      .eq('user_id', userId);
    if (error) throw error;
    return data;
  };

  const deleteFromCart = async (userId, productId) => {
    if (!userId) return;
    const { error } = await supabase
      .from('cart')
      .delete()
      .match({ user_id: userId, product_id: productId });
    if (error) throw error;
  };

  // Order Operations
  const createOrder = async (orderData) => {
    const { data, error } = await supabase
      .from('orders')
      .insert([{
        user_id: orderData.user_id || null,
        status: 'PENDING',
        total_amount: orderData.total_amount,
        mpesa_code: orderData.mpesa_code,
        delivery_cost: orderData.delivery_cost,
        billing_details: orderData.billing_details,
        items: orderData.items,
        created_at: new Date().toISOString()
      }])
      .select()
      .single();
    if (error) throw error;
    return data;
  };

  const fetchOrders = async (userId) => {
    if (!userId) return [];
    const { data, error } = await supabase
      .from('orders')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });
    if (error) throw error;
    return data;
  };

  // Wishlist Operations - Only for authenticated users
  const fetchWishlistItems = async (userId) => {
    if (!userId) return [];
    const { data, error } = await supabase
      .from('wishlists')
      .select('*, products(*)')
      .eq('user_id', userId);
    if (error) throw error;
    return data;
  };

  const addToWishlist = async (userId, productId) => {
    if (!userId) return;
    const { error } = await supabase
      .from('wishlists')
      .insert([{ user_id: userId, product_id: productId }]);
    if (error) throw error;
  };

  const deleteFromWishlist = async (userId, productId) => {
    if (!userId) return;
    const { error } = await supabase
      .from('wishlists')
      .delete()
      .match({ user_id: userId, product_id: productId });
    if (error) throw error;
  };

  // Clear cart after successful order
  const clearUserCart = async (userId) => {
    if (!userId) return;
    const { error } = await supabase
      .from('cart')
      .delete()
      .eq('user_id', userId);
    if (error) throw error;
  };

  // Products Management
  const createProduct = async (productData) => {
    const { data, error } = await supabase
      .from('products')
      .insert([{
        name: productData.name,
        price: productData.price,
        description: productData.description,
        discount: productData.discount || 0,
        quantity: productData.quantity,
        category_id: productData.category_id,
        category_name: productData.category_name,
        image_url: productData.image_url,
        is_promoted: productData.is_promoted || false,
        promotion_start_date: productData.promotion_start_date || null,
        promotion_end_date: productData.promotion_end_date || null,
        promotion_type: productData.promotion_type || null
      }])
      .select()
      .single();
    if (error) throw error;
    return data;
  };

  const updateProduct = async (id, productData) => {
    const { data, error } = await supabase
      .from('products')
      .update({
        name: productData.name,
        price: productData.price,
        description: productData.description,
        discount: productData.discount,
        quantity: productData.quantity,
        category_id: productData.category_id,
        category_name: productData.category_name,
        image_url: productData.image_url,
        is_promoted: productData.is_promoted,
        promotion_start_date: productData.promotion_start_date,
        promotion_end_date: productData.promotion_end_date,
        promotion_type: productData.promotion_type
      })
      .eq('id', id)
      .select();
    if (error) throw error;
    return data;
  };

  const deleteProduct = async (id) => {
    const { error } = await supabase
      .from('products')
      .delete()
      .eq('id', id);
    if (error) throw error;
  };

  // Categories Management
  const createCategory = async (categoryData) => {
    const { data, error } = await supabase
      .from('categories')
      .insert([{
        name: categoryData.name,
        description: categoryData.description,
        image_url: categoryData.image_url
      }])
      .select();
    if (error) throw error;
    return data;
  };

  const fetchPromotedProducts = async () => {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories!products_category_name_fkey (*)
      `)
      .eq('is_promoted', true)
      .gte('promotion_end_date', new Date().toISOString())
      .order('created_at', { ascending: false });
    if (error) throw error;
    return data;
  };

  const fetchRelatedPromotedProducts = async (categoryName, currentProductId) => {
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .eq('category_name', categoryName)
      .eq('is_promoted', true)
      .neq('id', currentProductId)
      .limit(6);

    if (error) {
      console.error('Error fetching related products:', error);
      return [];
    }

    return data;
  };
  const updateCategory = async (id, categoryData) => {
    const { data, error } = await supabase
      .from('categories')
      .update({
        name: categoryData.name,
        description: categoryData.description,
        image_url: categoryData.image_url
      })
      .eq('id', id)
      .select();
    if (error) throw error;
    return data;
  };

  const deleteCategory = async (id) => {
    const { error } = await supabase
      .from('categories')
      .delete()
      .eq('id', id);
    if (error) throw error;
  };

  // Orders Management
  const fetchAllOrders = async () => {
    const { data, error } = await supabase
      .from('orders')
      .select('*')
      .order('created_at', { ascending: false });
    if (error) throw error;
    return data;
  };

  const updateOrderStatus = async (orderId, status) => {
    const { data, error } = await supabase
      .from('orders')
      .update({ status })
      .eq('id', orderId)
      .select();
    if (error) throw error;
    return data;
  };

  const fetchMarketingBanners = async () => {
    const { data, error } = await supabase
      .from('marketing_banners')
      .select('*')
      .order('display_order', { ascending: true });
    if (error) throw error;
    return data;
  };
  

  return {
    fetchProducts,
    fetchCategories,
    fetchProductById,
    fetchProductsByCategory,
    syncCartToDb,
    fetchCartItems,
    deleteFromCart,
    createOrder,
    fetchOrders,
    fetchWishlistItems,
    addToWishlist,
    deleteFromWishlist,
    clearUserCart,
    createProduct,
    updateProduct,
    deleteProduct,
    createCategory,
    updateCategory,
    deleteCategory,
    fetchAllOrders,
    updateOrderStatus,
    fetchMarketingBanners,
    fetchPromotedProducts,
    fetchRelatedPromotedProducts
  };
};