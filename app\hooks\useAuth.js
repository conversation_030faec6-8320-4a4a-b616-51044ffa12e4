import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '../lib/supabase';
import { useRouter } from 'next/navigation';

export const useAuth = () => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userDetails, setUserDetails] = useState(null);
  const [isLoading, setIsLoading] = useState(true); // Add loading state
  const router = useRouter();

  const fetchUserDetails = useCallback(async (userId, retryCount = 0) => {
    console.log('=== fetchUserDetails START ===');
    console.log('Fetching user details for userId:', userId, 'retry:', retryCount);

    // Remove caching logic that could cause stale data issues
    // Always fetch fresh data to ensure consistency

    try {
      console.log('Making Supabase query to users table...');
      const { data, error } = await supabase
        .from('users')
        .select('id, name, email, role')  // Explicitly select role
        .eq('id', userId)
        .single();

      console.log('Supabase query response:', { data, error });
      console.log('Query completed, data type:', typeof data, 'error type:', typeof error);

      if (error) {
        console.error('Error fetching user details:', {
          error,
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint
        });

        // Provide specific error handling
        if (error.code === 'PGRST116') {
          console.log('PGRST116: No rows found - user profile does not exist in users table');
          return { id: userId, name: '', email: '', role: 'user' };
        } else if (error.code === '42501') {
          console.log('42501: Permission denied - RLS policy blocking access');
          return { id: userId, name: '', email: '', role: 'user' };
        } else if (error.code === '42P01') {
          console.log('42P01: Table does not exist');
          return { id: userId, name: '', email: '', role: 'user' };
        }

        throw error;
      }

      if (data) {
        console.log('User details fetched successfully:', data);
        console.log('=== fetchUserDetails SUCCESS ===');
        return data;
      } else {
        console.log('No data returned from query, creating default user profile');
        const defaultData = { id: userId, name: '', email: '', role: 'user' };
        console.log('=== fetchUserDetails DEFAULT ===');
        return defaultData;
      }
    } catch (error) {
      console.error('Failed to fetch user details (catch block):', {
        error,
        message: error.message,
        stack: error.stack
      });

      // Retry on network errors
      if (retryCount < 2 && (error.message.includes('network') || error.message.includes('fetch'))) {
        console.log(`Retrying fetchUserDetails due to network error in ${(retryCount + 1) * 1000}ms...`);
        await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 1000));
        return fetchUserDetails(userId, retryCount + 1);
      }

      // Return basic user data as fallback
      const fallbackData = { id: userId, name: '', email: '', role: 'user' };
      console.log('Returning fallback data:', fallbackData);
      console.log('=== fetchUserDetails FALLBACK ===');
      return fallbackData;
    }
  }, []);

  useEffect(() => {
    const getSession = async () => {
      console.log('Getting initial session...');
      setIsLoading(true);

      try {
        const { data: { session } } = await supabase.auth.getSession();
        console.log('Initial session check:', session?.user?.id);

        setUser(session?.user || null);
        setIsAuthenticated(!!session?.user);

        if (session?.user) {
          console.log('Fetching user details for initial session');
          const details = await fetchUserDetails(session.user.id);
          console.log('Setting user details from initial session:', details);
          setUserDetails(details);
        } else {
          setUserDetails(null);
        }
      } catch (error) {
        console.error('Error getting initial session:', error);
        setUser(null);
        setIsAuthenticated(false);
        setUserDetails(null);
      } finally {
        setIsLoading(false);
      }
    };

    getSession();

    const { data: listener } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session?.user?.id);

      // Only process SIGNED_IN and SIGNED_OUT events to avoid duplicates
      if (event === 'SIGNED_IN' || event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED') {
        console.log('Processing auth event:', event);
        setIsLoading(true);

        try {
          setUser(session?.user || null);
          setIsAuthenticated(!!session?.user);

          if (session?.user) {
            console.log('Fetching user details for auth event:', event);
            const details = await fetchUserDetails(session.user.id);
            console.log('Setting user details from auth event:', details);
            setUserDetails(details);
          } else {
            setUserDetails(null);
            sessionStorage.clear();
          }
        } catch (error) {
          console.error('Error processing auth state change:', error);
        } finally {
          setIsLoading(false);
        }
      } else {
        console.log('Ignoring auth event:', event);
      }
    });

    return () => {
      listener?.subscription.unsubscribe();
    };
  }, [fetchUserDetails]);

  const signIn = async (email, password) => {
    setIsLoading(true);

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
        options: {
          persistSession: true,
          cookieOptions: {
            name: 'sb-auth-token',
            path: '/',
            sameSite: 'lax',
            secure: process.env.NODE_ENV === 'production'
          }
        }
      });

      if (error) throw error;

      // The auth state change listener will handle setting user details
      // and updating loading state, so we don't need to do it here
      return data;
    } catch (error) {
      setIsLoading(false); // Only set loading false on error
      throw error;
    }
  };

  const signOut = async () => {
    setUser(null);
    setIsAuthenticated(false);
    setUserDetails(null);
    
    localStorage.removeItem('cart');
    localStorage.removeItem('wishlist');
    sessionStorage.clear();
    
    await supabase.auth.signOut();
    router.replace('/auth/login');
  };

  const signUp = async (email, password, name) => {
    // Validate email format before sending to Supabase
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new Error('Please enter a valid email address');
    }

    const { data, error } = await supabase.auth.signUp({
      email: email.trim().toLowerCase(),
      password,
      options: {
        data: {
          name: name.trim()
        }
      }
    });

    if (error) {
      // Provide more user-friendly error messages
      if (error.message.includes('invalid format')) {
        throw new Error('Please enter a valid email address');
      } else if (error.message.includes('already registered')) {
        throw new Error('An account with this email already exists. Please try logging in instead.');
      } else if (error.message.includes('password')) {
        throw new Error('Password must be at least 6 characters long');
      }
      throw error;
    }

    const userId = data?.user?.id;
    if (!userId) throw new Error('User ID not found after signup');

    console.log('Auth user created successfully:', {
      userId,
      email: data.user.email,
      confirmed: data.user.email_confirmed_at !== null
    });

    // Insert user profile data
    const profileData = {
      id: userId,
      name: name.trim(),
      email: email.trim().toLowerCase(),
      role: 'user' // Add default role
    };

    console.log('Attempting to insert profile data:', profileData);

    const { data: insertData, error: profileError } = await supabase
      .from('users')
      .insert([profileData])
      .select(); // Return the inserted data

    if (profileError) {
      console.error('Profile creation error details:', {
        error: profileError,
        code: profileError.code,
        message: profileError.message,
        details: profileError.details,
        hint: profileError.hint,
        profileData
      });

      // Try to provide more specific error information
      if (profileError.code === '42501') {
        console.error('Permission denied - likely RLS policy issue');
      } else if (profileError.code === '23505') {
        console.error('Duplicate key violation - user already exists');
      } else if (profileError.code === '42P01') {
        console.error('Table does not exist');
      }

      // Don't throw here as the auth user was created successfully
      // But log that we'll proceed without profile data
      console.warn('Proceeding without profile data due to insert failure');

      // Set fallback user details immediately to avoid race condition
      const fallbackUserDetails = {
        id: userId,
        name: name.trim(),
        email: email.trim().toLowerCase(),
        role: 'user'
      };
      setUserDetails(fallbackUserDetails);
      console.log('Set fallback user details:', fallbackUserDetails);
      return;
    } else {
      console.log('Profile created successfully:', insertData);

      // If profile was created successfully, use the returned data directly
      if (insertData && insertData.length > 0) {
        const createdProfile = insertData[0];
        console.log('Setting user details from created profile:', createdProfile);
        setUserDetails(createdProfile);
        return;
      }
    }

    // Only fetch user details if profile creation failed or didn't return data
    console.log('Calling fetchUserDetails for userId:', userId);
    const userDetails = await fetchUserDetails(userId);
    console.log('fetchUserDetails result:', userDetails);

    if (userDetails) {
      setUserDetails(userDetails);
    }
  };

  return {
    user,
    signIn,
    signOut,
    signUp,
    isAuthenticated,
    userDetails,
    fetchUserDetails,
    isLoading // Export the loading state
  };
};
