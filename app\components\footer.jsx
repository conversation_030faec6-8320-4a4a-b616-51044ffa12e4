"use client"
import { useEffect, useState } from 'react';
import { useSupabaseContext } from '../context/supabaseContext';
import { Phone, MapPin, Mail, Clock, Instagram, Facebook, MessageCircle } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { getCategoryPath } from '../lib/utils';

export default function Footer() {
  const { fetchCategories } = useSupabaseContext();
  const [categories, setCategories] = useState([]);

  useEffect(() => {
    fetchCategories().then(setCategories);
  }, [fetchCategories]);

  return (
    <footer className="bg-white border-t border-gray-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-8 sm:py-12">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">

            {/* Brand Section */}
            <div className="sm:col-span-2 lg:col-span-1">
              <div className="flex items-center gap-3 mb-4 sm:mb-6">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-primarycolor rounded-xl flex items-center justify-center">
                  <span className="text-lg sm:text-xl font-bold text-white">PM</span>
                </div>
                <div>
                  <h3 className="text-lg sm:text-xl font-bold text-gray-900">Pinchez</h3>
                  <p className="text-xs sm:text-sm text-gray-600">Merchants</p>
                </div>
              </div>
              <p className="text-sm sm:text-base text-gray-600 mb-4 sm:mb-6 leading-relaxed">
                Your trusted partner for quality products, fast delivery, and exceptional service across Kenya.
              </p>

              {/* Contact Info */}
              <div className="space-y-2 sm:space-y-3">
                <div className="flex items-center gap-3 text-gray-600">
                  <Phone className="w-4 h-4 sm:w-5 sm:h-5 text-primarycolor flex-shrink-0" />
                  <span className="text-sm sm:text-base">+254 740 209 121</span>
                </div>
                <div className="flex items-center gap-3 text-gray-600">
                  <Mail className="w-4 h-4 sm:w-5 sm:h-5 text-primarycolor flex-shrink-0" />
                  <span className="text-sm sm:text-base break-all"><EMAIL></span>
                </div>
                <div className="flex items-center gap-3 text-gray-600">
                  <MapPin className="w-4 h-4 sm:w-5 sm:h-5 text-primarycolor flex-shrink-0" />
                  <span className="text-sm sm:text-base">Nairobi, Kenya</span>
                </div>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="text-base sm:text-lg font-semibold text-gray-900 mb-4 sm:mb-6">Quick Links</h4>
              <ul className="space-y-2 sm:space-y-3">
                <li><Link href="/about" className="text-sm sm:text-base text-gray-600 hover:text-primarycolor transition-colors">About Us</Link></li>
                <li><Link href="/contact" className="text-sm sm:text-base text-gray-600 hover:text-primarycolor transition-colors">Contact</Link></li>
                <li><Link href="/shipping" className="text-sm sm:text-base text-gray-600 hover:text-primarycolor transition-colors">Shipping Info</Link></li>
                <li><Link href="/returns" className="text-sm sm:text-base text-gray-600 hover:text-primarycolor transition-colors">Returns</Link></li>
                <li><Link href="/privacy" className="text-sm sm:text-base text-gray-600 hover:text-primarycolor transition-colors">Privacy Policy</Link></li>
                <li><Link href="/terms" className="text-sm sm:text-base text-gray-600 hover:text-primarycolor transition-colors">Terms of Service</Link></li>
              </ul>
            </div>

            {/* Categories */}
            <div>
              <h4 className="text-base sm:text-lg font-semibold text-gray-900 mb-4 sm:mb-6">Categories</h4>
              <ul className="space-y-2 sm:space-y-3">
                {categories.slice(0, 6).map((category) => (
                  <li key={category.id}>
                    <Link
                      href={getCategoryPath(category)}
                      className="text-sm sm:text-base text-gray-600 hover:text-primarycolor transition-colors"
                    >
                      {category.name}
                    </Link>
                  </li>
                ))}
                {categories.length > 6 && (
                  <li>
                    <Link href="/categories" className="text-sm sm:text-base text-primarycolor font-medium hover:text-primarycolor-700">
                      View All Categories →
                    </Link>
                  </li>
                )}
              </ul>
            </div>

            {/* Customer Service */}
            <div className="sm:col-span-2 lg:col-span-1">
              <h4 className="text-base sm:text-lg font-semibold text-gray-900 mb-4 sm:mb-6">Customer Service</h4>
              <div className="space-y-3 sm:space-y-4">
                <div className="flex items-center gap-3 text-gray-600">
                  <Clock className="w-4 h-4 sm:w-5 sm:h-5 text-primarycolor flex-shrink-0" />
                  <div>
                    <p className="text-sm sm:text-base font-medium">Business Hours</p>
                    <p className="text-xs sm:text-sm">Mon - Sat: 8AM - 8PM</p>
                  </div>
                </div>

                {/* Social Links */}
                <div>
                  <p className="text-sm sm:text-base font-medium text-gray-900 mb-2 sm:mb-3">Follow Us</p>
                  <div className="flex gap-2 sm:gap-3">
                    <Link
                      href="https://www.instagram.com/sherryshouseholds/profilecard/?igsh=MXR5dndoeWtxYmV0ZA=="
                      className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-100 rounded-lg flex items-center justify-center hover:bg-primarycolor hover:text-white transition-all"
                    >
                      <Instagram className="w-4 h-4 sm:w-5 sm:h-5" />
                    </Link>
                    <Link
                      href="https://vm.tiktok.com/ZMh5fLEPU/"
                      className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-100 rounded-lg flex items-center justify-center hover:bg-primarycolor hover:text-white transition-all"
                    >
                      <svg className="w-4 h-4 sm:w-5 sm:h-5" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z" />
                      </svg>
                    </Link>
                    <Link
                      href="https://wa.me/+254740209121"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-100 rounded-lg flex items-center justify-center hover:bg-primarycolor hover:text-white transition-all"
                    >
                      <MessageCircle className="w-4 h-4 sm:w-5 sm:h-5" />
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-200 py-4 sm:py-6">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-3 sm:gap-4">
            <p className="text-gray-600 text-xs sm:text-sm text-center sm:text-left">
              © 2024 Pinchez Merchants. All rights reserved.
            </p>

            {/* Payment Methods */}
            <div className="flex flex-col sm:flex-row items-center gap-2 sm:gap-4">
              <span className="text-xs sm:text-sm text-gray-600">We accept:</span>
              <div className="flex items-center gap-2">
                <div className="w-6 h-5 sm:w-8 sm:h-6 bg-gray-100 rounded flex items-center justify-center">
                  <span className="text-xs font-bold text-gray-700">M</span>
                </div>
                <div className="w-6 h-5 sm:w-8 sm:h-6 bg-gray-100 rounded flex items-center justify-center">
                  <span className="text-xs font-bold text-gray-700">$</span>
                </div>
                <span className="text-xs sm:text-sm text-gray-600">M-Pesa & Cash</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}