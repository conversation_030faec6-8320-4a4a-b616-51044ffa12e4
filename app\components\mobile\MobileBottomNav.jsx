"use client";

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { Home, Search, ShoppingCart, Heart, User } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { useCart } from '../../context/cartContext';
import { WishlistContext } from '../../context/wishlistContext';
import { cn } from '../../lib/utils';
import { useContext } from 'react';

export default function MobileBottomNav() {
  const pathname = usePathname();
  const { isAuthenticated } = useAuth();
  const { cartItems } = useCart();
  const { wishlistItems } = useContext(WishlistContext);

  const cartItemsCount = cartItems?.length || 0;
  const wishlistItemsCount = wishlistItems?.length || 0;

  const navItems = [
    {
      icon: Home,
      label: 'Home',
      href: '/',
      active: pathname === '/'
    },
    {
      icon: Search,
      label: 'Search',
      href: '/search',
      active: pathname === '/search'
    },
    {
      icon: ShoppingCart,
      label: 'Cart',
      href: '/cart',
      active: pathname === '/cart',
      badge: cartItemsCount > 0 ? cartItemsCount : null
    },
    {
      icon: Heart,
      label: 'Wishlist',
      href: isAuthenticated ? '/profile/wishlist' : '/auth/login',
      active: pathname === '/profile/wishlist',
      badge: wishlistItemsCount > 0 ? wishlistItemsCount : null
    },
    {
      icon: User,
      label: 'Profile',
      href: isAuthenticated ? '/profile' : '/auth/login',
      active: pathname.startsWith('/profile') || pathname.startsWith('/auth')
    }
  ];

  // Hide on admin pages
  if (pathname.startsWith('/admin')) {
    return null;
  }

  return (
    <nav className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 md:hidden">
      <div className="flex items-center justify-around py-2">
        {navItems.map((item) => {
          const Icon = item.icon;
          
          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "flex flex-col items-center justify-center py-2 px-3 min-w-0 flex-1 relative transition-colors",
                item.active
                  ? "text-primarycolor"
                  : "text-gray-600 hover:text-primarycolor"
              )}
            >
              <div className="relative">
                <Icon className="w-6 h-6" />
                
                {/* Badge */}
                {item.badge && (
                  <span className="absolute -top-2 -right-2 w-5 h-5 bg-error text-white text-xs rounded-full flex items-center justify-center font-medium">
                    {item.badge > 99 ? '99+' : item.badge}
                  </span>
                )}
              </div>
              
              <span className={cn(
                "text-xs mt-1 font-medium truncate",
                item.active ? "text-primarycolor" : "text-gray-600"
              )}>
                {item.label}
              </span>
              
              {/* Active indicator */}
              {item.active && (
                <div className="absolute top-0 left-1/2 -translate-x-1/2 w-1 h-1 bg-primarycolor rounded-full" />
              )}
            </Link>
          );
        })}
      </div>
    </nav>
  );
}

// Mobile Search Bar Component
export function MobileSearchBar({ onSearch, placeholder = "Search products..." }) {
  const [query, setQuery] = React.useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    if (query.trim()) {
      onSearch(query.trim());
    }
  };

  return (
    <div className="md:hidden p-4 bg-white border-b border-gray-200">
      <form onSubmit={handleSubmit} className="relative">
        <input
          type="text"
          placeholder={placeholder}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className="w-full pl-4 pr-12 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primarycolor focus:border-transparent"
        />
        <button
          type="submit"
          className="absolute right-2 top-1/2 -translate-y-1/2 p-2 text-gray-400 hover:text-primarycolor transition-colors"
        >
          <Search className="w-5 h-5" />
        </button>
      </form>
    </div>
  );
}

// Mobile Category Tabs
export function MobileCategoryTabs({ categories = [], activeCategory, onCategoryChange }) {
  if (!categories.length) return null;

  return (
    <div className="md:hidden bg-white border-b border-gray-200">
      <div className="flex overflow-x-auto scrollbar-hide px-4 py-3 gap-2">
        <button
          onClick={() => onCategoryChange(null)}
          className={cn(
            "flex-shrink-0 px-4 py-2 rounded-full text-sm font-medium transition-colors",
            !activeCategory
              ? "bg-primarycolor text-white"
              : "bg-gray-100 text-gray-700 hover:bg-gray-200"
          )}
        >
          All
        </button>
        
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => onCategoryChange(category.id)}
            className={cn(
              "flex-shrink-0 px-4 py-2 rounded-full text-sm font-medium transition-colors whitespace-nowrap",
              activeCategory === category.id
                ? "bg-primarycolor text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            )}
          >
            {category.name}
          </button>
        ))}
      </div>
    </div>
  );
}

// Mobile Product Grid
export function MobileProductGrid({ products = [], loading = false }) {
  if (loading) {
    return (
      <div className="grid grid-cols-2 gap-3 p-4">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="bg-gray-200 aspect-square rounded-lg animate-pulse" />
        ))}
      </div>
    );
  }

  if (!products.length) {
    return (
      <div className="flex flex-col items-center justify-center py-12 px-4">
        <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mb-4">
          <Search className="w-8 h-8 text-gray-400" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">No products found</h3>
        <p className="text-gray-600 text-center">Try adjusting your search or browse different categories</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 gap-3 p-4 pb-20">
      {products.map((product) => (
        <Link
          key={product.id}
          href={`/product/${product.id}`}
          className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow"
        >
          <div className="aspect-square bg-gray-100 relative">
            <Image
              src={Array.isArray(product.image_url) ? product.image_url[0] : product.image_url}
              alt={product.name}
              width={200}
              height={200}
              className="w-full h-full object-cover"
            />
            
            {/* Discount Badge */}
            {product.discount > 0 && (
              <div className="absolute top-2 left-2 bg-error text-white px-2 py-1 rounded text-xs font-semibold">
                -{product.discount}%
              </div>
            )}
            
            {/* Wishlist Button */}
            <button className="absolute top-2 right-2 p-1.5 bg-white rounded-full shadow-md">
              <Heart className="w-4 h-4 text-gray-600" />
            </button>
          </div>
          
          <div className="p-3">
            <h3 className="font-medium text-gray-900 text-sm line-clamp-2 mb-1">
              {product.name}
            </h3>
            
            <div className="flex items-center justify-between">
              <div>
                {product.discount > 0 ? (
                  <div className="flex flex-col">
                    <span className="text-xs text-gray-500 line-through">
                      Ksh. {Math.floor(product.price)}
                    </span>
                    <span className="text-sm font-bold text-primarycolor">
                      Ksh. {Math.floor(product.price * (1 - product.discount / 100))}
                    </span>
                  </div>
                ) : (
                  <span className="text-sm font-bold text-primarycolor">
                    Ksh. {Math.floor(product.price)}
                  </span>
                )}
              </div>
              
              <button className="p-1.5 bg-primarycolor-50 rounded-full">
                <ShoppingCart className="w-4 h-4 text-primarycolor" />
              </button>
            </div>
          </div>
        </Link>
      ))}
    </div>
  );
}
