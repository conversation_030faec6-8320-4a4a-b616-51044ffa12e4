@tailwind base;
@tailwind components;
@tailwind utilities;

/* SF Pro Display Font Face Declarations */
@font-face {
  font-family: 'SF Pro Display';
  src: url('/fonts/SF-Pro-Display-Font-Family/SF-Pro-Display-Light.woff') format('woff');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('/fonts/SF-Pro-Display-Font-Family/SF-Pro-Display-Regular.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('/fonts/SF-Pro-Display-Font-Family/SF-Pro-Display-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('/fonts/SF-Pro-Display-Font-Family/SF-Pro-Display-Semibold.woff') format('woff');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('/fonts/SF-Pro-Display-Font-Family/SF-Pro-Display-Bold.woff') format('woff');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

:root {
  /* Base Colors */
  --background: #ffffff;
  --foreground: #000000;

  /* Brand Colors - New Fresh Palette */
  --primarycolor: #c3e703;
  --primarycolor-50: #f7fde4;
  --primarycolor-100: #ecfbc5;
  --primarycolor-200: #d9f792;
  --primarycolor-300: #c3e703;
  --primarycolor-400: #a8d102;
  --primarycolor-500: #8bb801;
  --primarycolor-600: #6d9200;
  --primarycolor-700: #527000;
  --primarycolor-800: #425a02;
  --primarycolor-900: #384d06;
  --primarycolor-950: #1a2b00;

  --secondarycolor: #96d1c7;
  --secondarycolor-50: #f0fdf9;
  --secondarycolor-100: #ccfbef;
  --secondarycolor-200: #99f6e0;
  --secondarycolor-300: #5eead4;
  --secondarycolor-400: #96d1c7;
  --secondarycolor-500: #14b8a6;
  --secondarycolor-600: #0d9488;
  --secondarycolor-700: #0f766e;
  --secondarycolor-800: #115e59;
  --secondarycolor-900: #134e4a;

  /* Semantic Colors */
  --success: #10b981;
  --success-50: #ecfdf5;
  --success-100: #d1fae5;
  --success-500: #10b981;
  --success-600: #059669;
  --success-700: #047857;

  --warning: #f59e0b;
  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --warning-700: #b45309;

  --error: #ef4444;
  --error-50: #fef2f2;
  --error-100: #fee2e2;
  --error-500: #ef4444;
  --error-600: #dc2626;
  --error-700: #b91c1c;

  --info: #3b82f6;
  --info-50: #eff6ff;
  --info-100: #dbeafe;
  --info-500: #3b82f6;
  --info-600: #2563eb;
  --info-700: #1d4ed8;

  /* Neutral Colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Legacy Support */
  --warningcolor: var(--error-500);
  --primarycolorvariant: var(--primarycolor-400);
  --secondaryvariant: var(--secondarycolor-100);

  /* Spacing Scale */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;
  --primaryradius: var(--radius-lg);

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;

  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Toast Configuration */
  --toast-background: var(--primarycolor);
  --toast-border: 2px solid var(--secondarycolor);
  --toast-color: white;
  --toast-border-radius: var(--radius-2xl);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: white;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: var(--font-montserrat), sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@keyframes shrink {
  from { width: 100%; }
  to { width: 0%; }
}

.animate-shrink {
  animation: shrink 5s linear forwards;
}

.custom-toast {
  transform: translateX(100%);
  animation: slideIn 0.5s forwards;
}

@keyframes slideIn {
  to { transform: translateX(0); }
}

/* Font Family Classes */
.font-sf-pro-display {
  font-family: 'SF Pro Display', system-ui, sans-serif;
}

/* Apply SF Pro Display as default font */
body {
  font-family: 'SF Pro Display', system-ui, sans-serif;
}

/* Hide scrollbars but keep functionality */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}
