"use client";
import { useBanners } from '../hooks/useQueries';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination, Autoplay } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/pagination';
import Image from 'next/image';
import Link from 'next/link';

export default function BannerCarousel() {
  const { data: banners = [] } = useBanners();

  if (!banners.length) {
    return (
      <div className="w-full h-[200px] sm:h-[250px] md:h-[300px] lg:h-[400px] bg-gradient-to-r from-primarycolor-50 to-secondarycolor-50 rounded-xl sm:rounded-2xl flex items-center justify-center">
        <div className="text-center px-4">
          <div className="w-12 h-12 sm:w-16 sm:h-16 bg-primarycolor rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
            <span className="text-lg sm:text-2xl font-bold text-white">PM</span>
          </div>
          <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-2">Welcome to Pinchez Merchants</h3>
          <p className="text-sm sm:text-base text-gray-600">Quality products, fast delivery</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative rounded-2xl overflow-hidden shadow-lg">
      <Swiper
        modules={[Pagination, Autoplay]}
        pagination={{
          clickable: true,
          el: '.modern-banner-pagination',
          bulletClass: 'modern-banner-bullet',
          bulletActiveClass: 'modern-banner-bullet-active'
        }}
        autoplay={{
          delay: 4000,
          disableOnInteraction: false,
          pauseOnMouseEnter: true
        }}
        initialSlide={0}
        loop={banners.length > 1}
        speed={600}
        className="w-full h-[200px] sm:h-[250px] md:h-[300px] lg:h-[400px]"
      >
        {banners.map((banner) => (
          <SwiperSlide key={banner.id}>
            <div className="relative w-full h-full group">
              <Image
                src={banner.image_url}
                alt={banner.title || 'Banner'}
                fill
                className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                priority
              />
              {/* Overlay for better text readability */}
              <div className="absolute inset-0 bg-black/20"></div>

              {/* Banner Content */}
              {banner.title && (
                <div className="absolute bottom-4 sm:bottom-6 lg:bottom-8 left-4 sm:left-6 lg:left-8 text-white z-10">
                  <h3 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold mb-1 sm:mb-2">{banner.title}</h3>
                  {banner.description && (
                    <p className="text-sm sm:text-base lg:text-lg opacity-90 line-clamp-2">{banner.description}</p>
                  )}
                </div>
              )}

              {banner.link && (
                <Link
                  href={banner.link}
                  className="absolute inset-0 z-20"
                  target="_blank"
                  rel="noopener noreferrer"
                />
              )}
            </div>
          </SwiperSlide>
        ))}

        {/* Modern Pagination */}
        <div className="modern-banner-pagination absolute bottom-6 left-1/2 transform -translate-x-1/2 flex gap-2 z-30"></div>
      </Swiper>

      <style jsx global>{`
        .modern-banner-bullet {
          width: 12px;
          height: 12px;
          background: rgba(255, 255, 255, 0.5);
          border-radius: 50%;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .modern-banner-bullet-active {
          background: #c3e703;
          transform: scale(1.2);
        }

        .modern-banner-bullet:hover {
          background: rgba(255, 255, 255, 0.8);
        }
      `}</style>
    </div>
  );
}
