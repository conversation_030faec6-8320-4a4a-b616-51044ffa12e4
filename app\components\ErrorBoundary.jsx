"use client";

import React from 'react';
import { Alert<PERSON>riangle, RefreshCw, Home } from 'lucide-react';
import { useRouter } from 'next/navigation';

/**
 * Global Error Boundary Component
 * Catches JavaScript errors anywhere in the child component tree and displays a fallback UI
 */
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { 
      hasError: true,
      errorId: Date.now().toString(36) + Math.random().toString(36).substr(2)
    };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details
    console.error('Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Report error to monitoring service (e.g., Sentry, LogRocket)
    this.reportError(error, errorInfo);
  }

  reportError = (error, errorInfo) => {
    // In production, you would send this to your error reporting service
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      errorId: this.state.errorId
    };

    // Example: Send to your error reporting service
    // errorReportingService.report(errorReport);
    
    // For development, log to console
    if (process.env.NODE_ENV === 'development') {
      console.group('🚨 Error Report');
      console.error('Error ID:', errorReport.errorId);
      console.error('Message:', errorReport.message);
      console.error('Stack:', errorReport.stack);
      console.error('Component Stack:', errorReport.componentStack);
      console.groupEnd();
    }
  };

  handleRetry = () => {
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: null
    });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI based on error type
      const { fallback: CustomFallback, level = 'page' } = this.props;
      
      if (CustomFallback) {
        return (
          <CustomFallback 
            error={this.state.error}
            errorInfo={this.state.errorInfo}
            onRetry={this.handleRetry}
            errorId={this.state.errorId}
          />
        );
      }

      // Default fallback UI
      return (
        <ErrorFallback
          error={this.state.error}
          errorInfo={this.state.errorInfo}
          onRetry={this.handleRetry}
          errorId={this.state.errorId}
          level={level}
        />
      );
    }

    return this.props.children;
  }
}

/**
 * Default Error Fallback Component
 */
function ErrorFallback({ error, errorInfo, onRetry, errorId, level }) {
  const router = useRouter();

  const handleGoHome = () => {
    router.push('/');
  };

  const handleReload = () => {
    window.location.reload();
  };

  // Different UI based on error level
  const isPageLevel = level === 'page';
  const containerClass = isPageLevel 
    ? 'min-h-screen flex items-center justify-center bg-gray-50 px-4'
    : 'flex items-center justify-center bg-red-50 border border-red-200 rounded-lg p-6 m-4';

  return (
    <div className={containerClass}>
      <div className="max-w-md w-full text-center">
        <div className="mb-6">
          <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            {isPageLevel ? 'Something went wrong' : 'Component Error'}
          </h1>
          <p className="text-gray-600 mb-4">
            {isPageLevel 
              ? 'We encountered an unexpected error. Please try again.'
              : 'This component failed to load properly.'
            }
          </p>
          
          {process.env.NODE_ENV === 'development' && (
            <div className="bg-red-100 border border-red-300 rounded-lg p-4 mb-4 text-left">
              <h3 className="font-semibold text-red-800 mb-2">Error Details:</h3>
              <p className="text-sm text-red-700 font-mono break-all">
                {error?.message}
              </p>
              {errorId && (
                <p className="text-xs text-red-600 mt-2">
                  Error ID: {errorId}
                </p>
              )}
            </div>
          )}
        </div>

        <div className="space-y-3">
          <button
            onClick={onRetry}
            className="w-full flex items-center justify-center gap-2 bg-primarycolor text-white px-6 py-3 rounded-lg hover:bg-primarycolor/90 transition-colors"
          >
            <RefreshCw className="w-4 h-4" />
            Try Again
          </button>
          
          {isPageLevel && (
            <>
              <button
                onClick={handleGoHome}
                className="w-full flex items-center justify-center gap-2 bg-gray-200 text-gray-800 px-6 py-3 rounded-lg hover:bg-gray-300 transition-colors"
              >
                <Home className="w-4 h-4" />
                Go Home
              </button>
              
              <button
                onClick={handleReload}
                className="w-full text-gray-600 hover:text-gray-800 transition-colors"
              >
                Reload Page
              </button>
            </>
          )}
        </div>

        {process.env.NODE_ENV === 'production' && errorId && (
          <p className="text-xs text-gray-500 mt-4">
            If this problem persists, please contact support with error ID: {errorId}
          </p>
        )}
      </div>
    </div>
  );
}

/**
 * Higher-order component to wrap components with error boundary
 */
export function withErrorBoundary(Component, errorBoundaryProps = {}) {
  const WrappedComponent = (props) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );
  
  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

/**
 * Hook to manually trigger error boundary
 */
export function useErrorHandler() {
  return (error, errorInfo) => {
    // This will trigger the nearest error boundary
    throw error;
  };
}

export default ErrorBoundary;
