"use client";
import SalesGraph from '../components/salesgraph';
import { useSupabase } from '../hooks/useSupabase';

export default function SalesReports() {
  const { useDashboardData } = useSupabase();
  const { data: dashboardData, isLoading } = useDashboardData();

  if (isLoading) return <div className='text-center text-primarycolorvariant'>Loading sales data...</div>;

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4 text-primarycolor">Sales Reports</h1>
      <SalesGraph salesData={dashboardData?.salesData || []} />
    </div>
  );
}
