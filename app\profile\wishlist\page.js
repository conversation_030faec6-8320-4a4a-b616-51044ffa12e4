import { Suspense } from "react";
import ProfileLayout from "../../components/profile/ProfileLayout";
import WishlistPage from "../../components/profile/WishlistPage";

export default function WishlistPageRoute() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ProfileLayout 
        title="Wishlist" 
        subtitle="Your saved items and favorites"
      >
        <WishlistPage />
      </ProfileLayout>
    </Suspense>
  );
}
