# Environment Variables Sync Script

This script helps you sync environment variables from your Vercel project to your local development environment.

## Quick Start

### 🚀 Most Common Use Cases

**Fetch production environment variables to local:**
```bash
npm run sync-env:prod
```

**Fetch development environment variables:**
```bash
npm run sync-env:dev
```

**Fetch preview environment variables:**
```bash
npm run sync-env:preview
```

## Prerequisites

1. **Vercel CLI Authentication**
   ```bash
   # If you have Vercel CLI globally installed
   vercel login
   
   # If using npx (recommended)
   npx vercel login
   ```

2. **Project Setup**
   - Your project should be linked to Vercel
   - Run `npx vercel link` if not already linked

## Available Commands

| Command | Description | Output File |
|---------|-------------|-------------|
| `npm run sync-env` | Sync development environment (default) | `.env.local` |
| `npm run sync-env:dev` | Sync development environment | `.env.local` |
| `npm run sync-env:prod` | Sync production environment | `.env.production` |
| `npm run sync-env:preview` | Sync preview environment | `.env.preview` |

## How It Works

1. **Automatic CLI Detection**: The script automatically detects whether you have Vercel CLI installed globally or uses `npx vercel`
2. **Backup Creation**: Before overwriting, it creates backups in `scripts/env-backups/`
3. **Environment Grouping**: Variables are organized by type (database, auth, API, etc.)
4. **File Generation**: Creates properly formatted `.env` files with comments

## File Structure

```
your-project/
├── .env.local          # Development environment variables
├── .env.production     # Production environment variables  
├── .env.preview        # Preview environment variables
└── scripts/
    ├── sync-env.js     # The sync script
    └── env-backups/    # Automatic backups
        ├── .env.local.2024-01-15T10-30-00-000Z.backup
        └── ...
```

## Troubleshooting

### Authentication Issues
```bash
# Re-authenticate with Vercel
npx vercel login
```

### Project Not Linked
```bash
# Link your project to Vercel
npx vercel link
```

### Permission Issues
```bash
# Make sure you have access to the Vercel project
npx vercel whoami
```

## Security Notes

- ⚠️ **Never commit `.env.*` files to version control**
- ✅ Add `.env.*` to your `.gitignore`
- 🔒 Environment variables may contain sensitive data
- 💾 Backups are created automatically before each sync

## Advanced Usage

### Direct Script Usage
```bash
# Using npx
npx node scripts/sync-env.js production

# Direct node execution
node scripts/sync-env.js development

# With custom project ID
node scripts/sync-env.js production your-project-id
```

### Environment Variable Grouping

The script automatically groups variables by type:
- **Database**: Variables containing 'database', 'db', 'supabase'
- **Auth**: Variables containing 'auth', 'jwt', 'secret'
- **API**: Variables containing 'api', 'key'
- **Next.js**: Variables starting with 'NEXT_'
- **Vercel**: Variables starting with 'VERCEL_'
- **Other**: All other variables
