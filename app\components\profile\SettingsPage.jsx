"use client";

import React, { useState } from 'react';
import { 
  <PERSON>tings, 
  <PERSON>lette, 
  Globe, 
  DollarSign, 
  Eye, 
  EyeOff, 
  Monitor, 
  Sun, 
  Moon,
  CheckCircle,
  AlertTriangle,
  Trash2,
  Download,
  Upload
} from 'lucide-react';
import { 
  useUserSettings,
  useUpdateUserSettings
} from '../../hooks/useProfile';
import { useAuth } from '../../hooks/useAuth';
import { userSettingsSchema } from '../../lib/validations';
import Card from '../ui/Card';
import Button from '../ui/Button';
import Input, { FormGroup } from '../ui/Input';
import { cn } from '../../lib/utils';

const THEME_OPTIONS = [
  { value: 'light', label: 'Light', icon: Sun, description: 'Light theme' },
  { value: 'dark', label: 'Dark', icon: Moon, description: 'Dark theme (coming soon)' },
  { value: 'system', label: 'System', icon: Monitor, description: 'Follow system preference' }
];

const LANGUAGE_OPTIONS = [
  { value: 'en', label: 'English', flag: '🇺🇸' },
  { value: 'sw', label: 'Kiswahili', flag: '🇰🇪' }
];

const CURRENCY_OPTIONS = [
  { value: 'KES', label: 'Kenyan Shilling (KES)', symbol: 'KSh' },
  { value: 'USD', label: 'US Dollar (USD)', symbol: '$' },
  { value: 'EUR', label: 'Euro (EUR)', symbol: '€' }
];

export default function SettingsPage() {
  const { user, signOut } = useAuth();
  const { data: settings = {}, isLoading } = useUserSettings();
  const updateSettings = useUpdateUserSettings();
  
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [success, setSuccess] = useState('');
  const [error, setError] = useState('');

  // Get current settings with defaults
  const displayPreferences = settings.display_preferences || {
    theme: 'system',
    language: 'en',
    currency: 'KES'
  };

  const privacySettings = settings.privacy_settings || {
    profile_visibility: 'private',
    show_online_status: false
  };

  const handleDisplaySettingChange = async (key, value) => {
    try {
      const newPreferences = {
        ...displayPreferences,
        [key]: value
      };
      
      await updateSettings.mutateAsync({
        key: 'display_preferences',
        value: newPreferences
      });
      
      setSuccess('Display preferences updated successfully!');
      setTimeout(() => setSuccess(''), 3000);
    } catch (error) {
      setError('Failed to update display preferences');
      setTimeout(() => setError(''), 3000);
      console.error('Failed to update display preferences:', error);
    }
  };

  const handlePrivacySettingChange = async (key, value) => {
    try {
      const newSettings = {
        ...privacySettings,
        [key]: value
      };
      
      await updateSettings.mutateAsync({
        key: 'privacy_settings',
        value: newSettings
      });
      
      setSuccess('Privacy settings updated successfully!');
      setTimeout(() => setSuccess(''), 3000);
    } catch (error) {
      setError('Failed to update privacy settings');
      setTimeout(() => setError(''), 3000);
      console.error('Failed to update privacy settings:', error);
    }
  };

  const handleExportData = () => {
    // This would typically trigger a data export process
    alert('Data export feature will be available soon. You will receive an email when your data is ready for download.');
  };

  const handleDeleteAccount = async () => {
    if (window.confirm('Are you absolutely sure? This action cannot be undone and will permanently delete your account and all associated data.')) {
      try {
        // This would typically call an API to delete the account
        alert('Account deletion feature will be available soon. Please contact support for account deletion requests.');
        setShowDeleteConfirm(false);
      } catch (error) {
        setError('Failed to delete account');
        console.error('Account deletion failed:', error);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        {[1, 2, 3].map(i => (
          <div key={i} className="animate-pulse">
            <div className="h-32 bg-gray-200 rounded-lg"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Success/Error Messages */}
      {success && (
        <Card className="p-4 bg-green-50 border-green-200">
          <div className="flex items-center gap-3">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <span className="text-green-800">{success}</span>
          </div>
        </Card>
      )}

      {error && (
        <Card className="p-4 bg-red-50 border-red-200">
          <div className="flex items-center gap-3">
            <AlertTriangle className="w-5 h-5 text-red-600" />
            <span className="text-red-800">{error}</span>
          </div>
        </Card>
      )}

      {/* Display Preferences */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-purple-50 rounded-lg">
            <Palette className="w-5 h-5 text-purple-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">Display Preferences</h3>
            <p className="text-sm text-gray-600">Customize how the app looks and feels</p>
          </div>
        </div>

        <div className="space-y-6">
          {/* Theme Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">Theme</label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              {THEME_OPTIONS.map((theme) => {
                const Icon = theme.icon;
                const isSelected = displayPreferences.theme === theme.value;
                const isDisabled = theme.value === 'dark'; // Dark theme not implemented yet
                
                return (
                  <button
                    key={theme.value}
                    onClick={() => !isDisabled && handleDisplaySettingChange('theme', theme.value)}
                    disabled={isDisabled || updateSettings.isPending}
                    className={cn(
                      'p-4 border rounded-lg text-left transition-colors',
                      isSelected
                        ? 'border-primarycolor bg-primarycolor-50'
                        : 'border-gray-300 hover:border-gray-400',
                      isDisabled && 'opacity-50 cursor-not-allowed'
                    )}
                  >
                    <div className="flex items-center gap-3">
                      <Icon className="w-5 h-5 text-gray-600" />
                      <div>
                        <div className="font-medium text-gray-900">{theme.label}</div>
                        <div className="text-sm text-gray-600">{theme.description}</div>
                      </div>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Language Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">Language</label>
            <select
              value={displayPreferences.language}
              onChange={(e) => handleDisplaySettingChange('language', e.target.value)}
              disabled={updateSettings.isPending}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primarycolor focus:border-transparent"
            >
              {LANGUAGE_OPTIONS.map((lang) => (
                <option key={lang.value} value={lang.value}>
                  {lang.flag} {lang.label}
                </option>
              ))}
            </select>
            <p className="text-xs text-gray-500 mt-1">
              Note: Kiswahili translation is coming soon
            </p>
          </div>

          {/* Currency Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">Currency</label>
            <select
              value={displayPreferences.currency}
              onChange={(e) => handleDisplaySettingChange('currency', e.target.value)}
              disabled={updateSettings.isPending}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primarycolor focus:border-transparent"
            >
              {CURRENCY_OPTIONS.map((currency) => (
                <option key={currency.value} value={currency.value}>
                  {currency.symbol} {currency.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </Card>

      {/* Privacy Settings */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-blue-50 rounded-lg">
            <Eye className="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">Privacy Settings</h3>
            <p className="text-sm text-gray-600">Control your privacy and visibility</p>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 className="font-medium text-gray-900">Profile Visibility</h4>
              <p className="text-sm text-gray-600">Control who can see your profile information</p>
            </div>
            <select
              value={privacySettings.profile_visibility}
              onChange={(e) => handlePrivacySettingChange('profile_visibility', e.target.value)}
              disabled={updateSettings.isPending}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primarycolor focus:border-transparent"
            >
              <option value="private">Private</option>
              <option value="public">Public</option>
            </select>
          </div>

          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 className="font-medium text-gray-900">Show Online Status</h4>
              <p className="text-sm text-gray-600">Let others see when you&apos;re online</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={privacySettings.show_online_status}
                onChange={(e) => handlePrivacySettingChange('show_online_status', e.target.checked)}
                disabled={updateSettings.isPending}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primarycolor-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primarycolor"></div>
            </label>
          </div>
        </div>
      </Card>

      {/* Data Management */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-green-50 rounded-lg">
            <Download className="w-5 h-5 text-green-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">Data Management</h3>
            <p className="text-sm text-gray-600">Export or manage your account data</p>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 className="font-medium text-gray-900">Export Account Data</h4>
              <p className="text-sm text-gray-600">Download a copy of your account data</p>
            </div>
            <Button variant="outline" onClick={handleExportData}>
              <Download className="w-4 h-4 mr-2" />
              Export Data
            </Button>
          </div>
        </div>
      </Card>

      {/* Account Actions */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-red-50 rounded-lg">
            <Settings className="w-5 h-5 text-red-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">Account Actions</h3>
            <p className="text-sm text-gray-600">Manage your account</p>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 className="font-medium text-gray-900">Sign Out</h4>
              <p className="text-sm text-gray-600">Sign out of your account on this device</p>
            </div>
            <Button variant="outline" onClick={signOut}>
              Sign Out
            </Button>
          </div>

          <div className="flex items-center justify-between p-4 bg-red-50 rounded-lg border border-red-200">
            <div>
              <h4 className="font-medium text-red-900">Delete Account</h4>
              <p className="text-sm text-red-700">Permanently delete your account and all data</p>
            </div>
            <Button 
              variant="outline" 
              onClick={() => setShowDeleteConfirm(true)}
              className="text-red-600 border-red-300 hover:bg-red-50"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Delete Account
            </Button>
          </div>
        </div>
      </Card>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-red-50 rounded-lg">
                  <AlertTriangle className="w-5 h-5 text-red-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900">Delete Account</h3>
              </div>
              
              <p className="text-gray-600 mb-6">
                Are you sure you want to delete your account? This action cannot be undone and will permanently remove:
              </p>
              
              <ul className="text-sm text-gray-600 mb-6 space-y-1">
                <li>• Your profile and personal information</li>
                <li>• Order history and tracking</li>
                <li>• Saved addresses and payment methods</li>
                <li>• Wishlist and preferences</li>
                <li>• All account data</li>
              </ul>
              
              <div className="flex gap-3">
                <Button
                  onClick={handleDeleteAccount}
                  className="flex-1 bg-red-600 hover:bg-red-700"
                >
                  Yes, Delete Account
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setShowDeleteConfirm(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
}
