import { supabase } from '../lib/supabase';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

/**
 * Products-related hooks
 * Separated from the main useSupabase hook for better organization
 */

export function useProducts() {
  return useQuery({
    queryKey: ['products'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          categories!products_category_name_fkey (*)
        `);
      if (error) throw error;
      
      // Ensure image_url is always an array
      return data.map(product => ({
        ...product,
        image_url: Array.isArray(product.image_url) ? product.image_url : [product.image_url]
      }));
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useProduct(productId) {
  return useQuery({
    queryKey: ['product', productId],
    queryFn: async () => {
      if (!productId) throw new Error('Product ID is required');
      
      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          categories!products_category_name_fkey (*)
        `)
        .eq('id', productId)
        .single();
      
      if (error) throw error;
      
      return {
        ...data,
        image_url: Array.isArray(data.image_url) ? data.image_url : [data.image_url]
      };
    },
    enabled: !!productId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useProductsByCategory(categoryName) {
  return useQuery({
    queryKey: ['products', 'category', categoryName],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('category_name', categoryName);
      if (error) throw error;
    
      return data.map(product => ({
        ...product,
        image_url: Array.isArray(product.image_url) ? product.image_url : [product.image_url]
      }));
    },
    enabled: !!categoryName,
    staleTime: 5 * 60 * 1000,
  });
}

export function usePromotedProducts() {
  return useQuery({
    queryKey: ['products', 'promoted'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          categories!products_category_name_fkey (*)
        `)
        .eq('is_promoted', true)
        .gte('promotion_end_date', new Date().toISOString())
        .order('created_at', { ascending: false });
      if (error) throw error;
      return data;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes for promoted products
  });
}

export function useRelatedPromotedProducts(categoryName, excludeId) {
  return useQuery({
    queryKey: ['products', 'related-promoted', categoryName, excludeId],
    queryFn: async () => {
      let query = supabase
        .from('products')
        .select(`
          *,
          categories!products_category_name_fkey (*)
        `)
        .eq('is_promoted', true)
        .gte('promotion_end_date', new Date().toISOString())
        .limit(4);

      if (categoryName) {
        query = query.eq('category_name', categoryName);
      }
      
      if (excludeId) {
        query = query.neq('id', excludeId);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data;
    },
    enabled: !!categoryName,
    staleTime: 5 * 60 * 1000,
  });
}

// Product mutations for admin
export function useCreateProduct() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (productData) => {
      const { data, error } = await supabase
        .from('products')
        .insert([{
          name: productData.name,
          price: productData.price,
          description: productData.description,
          discount: productData.discount || 0,
          quantity: productData.quantity,
          category_id: productData.category_id,
          category_name: productData.category_name,
          image_url: productData.image_url,
          is_promoted: productData.is_promoted || false,
          promotion_start_date: productData.promotion_start_date || null,
          promotion_end_date: productData.promotion_end_date || null,
          promotion_type: productData.promotion_type || null
        }])
        .select()
        .single();
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
}

export function useUpdateProduct() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, ...updateData }) => {
      const { data, error } = await supabase
        .from('products')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();
      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
      queryClient.invalidateQueries({ queryKey: ['product', data.id] });
    },
  });
}

export function useDeleteProduct() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id) => {
      const { error } = await supabase
        .from('products')
        .delete()
        .eq('id', id);
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
}

// Search products
export function useSearchProducts(searchTerm, filters = {}) {
  return useQuery({
    queryKey: ['products', 'search', searchTerm, filters],
    queryFn: async () => {
      let query = supabase
        .from('products')
        .select(`
          *,
          categories!products_category_name_fkey (*)
        `);

      // Apply search term
      if (searchTerm) {
        query = query.or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
      }

      // Apply filters
      if (filters.category) {
        query = query.eq('category_name', filters.category);
      }
      
      if (filters.minPrice) {
        query = query.gte('price', filters.minPrice);
      }
      
      if (filters.maxPrice) {
        query = query.lte('price', filters.maxPrice);
      }

      // Apply sorting
      const sortBy = filters.sortBy || 'created_at';
      const sortOrder = filters.sortOrder || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      const { data, error } = await query;
      if (error) throw error;
      
      return data.map(product => ({
        ...product,
        image_url: Array.isArray(product.image_url) ? product.image_url : [product.image_url]
      }));
    },
    enabled: !!searchTerm || Object.keys(filters).length > 0,
    staleTime: 2 * 60 * 1000,
  });
}

// Product analytics
export function useProductAnalytics(productId) {
  return useQuery({
    queryKey: ['product-analytics', productId],
    queryFn: async () => {
      // This would typically fetch analytics data
      // For now, return mock data
      return {
        views: 0,
        purchases: 0,
        wishlistAdds: 0,
        conversionRate: 0
      };
    },
    enabled: !!productId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}
