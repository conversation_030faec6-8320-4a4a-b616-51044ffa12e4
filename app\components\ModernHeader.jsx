"use client";

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter, usePathname } from 'next/navigation';
import { 
  Search, 
  ShoppingCart, 
  User, 
  Heart, 
  Menu, 
  X, 
  ChevronDown,
  Bell,
  MapPin,
  Phone
} from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useCart } from '../context/cartContext';
import { WishlistContext } from '../context/wishlistContext';
import { useProducts, useCategories } from '../hooks/useQueries';
import Button, { IconButton } from './ui/Button';
import { SearchInput } from './ui/Input';
import Card from './ui/Card';
import { cn, getInitials, getCategoryPath, toTitleCase } from '../lib/utils';
import { useContext } from 'react';

export default function ModernHeader() {
  const { user, isAuthenticated, signOut } = useAuth();
  const { cartItems } = useCart();
  const { wishlistItems } = useContext(WishlistContext);
  const { data: categories = [] } = useCategories();
  const { data: products = [] } = useProducts();
  const router = useRouter();
  const pathname = usePathname();

  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [mobileSearchOpen, setMobileSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const [categoriesMenuOpen, setCategoriesMenuOpen] = useState(false);

  const searchRef = useRef(null);
  const userMenuRef = useRef(null);
  const categoriesMenuRef = useRef(null);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setShowSearchResults(false);
      }
      if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {
        setUserMenuOpen(false);
      }
      if (categoriesMenuRef.current && !categoriesMenuRef.current.contains(event.target)) {
        setCategoriesMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Search functionality
  useEffect(() => {
    if (searchQuery.length > 2) {
      const filtered = products
        .filter(product => 
          product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          product.description?.toLowerCase().includes(searchQuery.toLowerCase())
        )
        .slice(0, 5);
      setSearchResults(filtered);
      setShowSearchResults(true);
    } else {
      setSearchResults([]);
      setShowSearchResults(false);
    }
  }, [searchQuery, products]);

  const handleSearch = (query) => {
    if (query.trim()) {
      router.push(`/search?q=${encodeURIComponent(query)}`);
      setShowSearchResults(false);
      setSearchQuery('');
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      setUserMenuOpen(false);
      router.push('/');
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const cartItemsCount = cartItems?.length || 0;
  const wishlistItemsCount = wishlistItems?.length || 0;

  return (
    <header className="bg-white shadow-sm border-b sticky top-0 z-50">
      {/* Top Bar */}
      <div className="bg-primarycolor text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-10 text-sm">
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-2">
                <Phone className="w-4 h-4" />
                <span>+254 700 000 000</span>
              </div>
              <div className="hidden md:flex items-center gap-2">
                <MapPin className="w-4 h-4" />
                <span>Free delivery in Nairobi</span>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <Link href="/help" className="hover:text-primarycolor-200 transition-colors">
                Help
              </Link>
              <Link href="/track-order" className="hover:text-primarycolor-200 transition-colors">
                Track Order
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center gap-4">
            <Link href="/" className="flex items-center gap-3">
              <div className="w-10 h-10 bg-primarycolor rounded-xl flex items-center justify-center">
                <span className="text-xl font-bold text-white">P</span>
              </div>
              <div className="hidden sm:block">
                <span className="text-xl font-bold text-gray-900">Pinchez</span>
                <div className="text-xs text-gray-500">Merchants</div>
              </div>
            </Link>

            {/* Categories Dropdown - Desktop */}
            <div className="hidden lg:block relative" ref={categoriesMenuRef}>
              <button
                onClick={() => setCategoriesMenuOpen(!categoriesMenuOpen)}
                className="flex items-center gap-2 px-4 py-2 text-gray-700 hover:text-primarycolor transition-colors"
              >
                <Menu className="w-5 h-5" />
                <span>Categories</span>
                <ChevronDown className="w-4 h-4" />
              </button>

              {categoriesMenuOpen && (
                <Card className="absolute top-full left-0 mt-2 w-64 p-4 shadow-lg z-50">
                  <div className="space-y-2">
                    {categories.slice(0, 8).map((category) => (
                      <Link
                        key={category.id}
                        href={getCategoryPath(category)}
                        className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 transition-colors"
                        onClick={() => setCategoriesMenuOpen(false)}
                      >
                        {category.image_url ? (
                          <Image
                            src={category.image_url}
                            alt={category.name}
                            width={24}
                            height={24}
                            className="w-6 h-6 object-cover rounded"
                          />
                        ) : (
                          <div className="w-6 h-6 bg-primarycolor-100 rounded flex items-center justify-center">
                            <span className="text-xs text-primarycolor">
                              {category.name.charAt(0)}
                            </span>
                          </div>
                        )}
                        <span className="text-sm font-medium text-gray-900">
                          {category.name}
                        </span>
                      </Link>
                    ))}
                    <Link
                      href="/categories"
                      className="block text-center py-2 text-sm text-primarycolor hover:text-primarycolor-700 font-medium border-t border-gray-200 mt-2 pt-2"
                      onClick={() => setCategoriesMenuOpen(false)}
                    >
                      View All Categories
                    </Link>
                  </div>
                </Card>
              )}
            </div>
          </div>

          {/* Modern Search Bar - Hidden on Mobile */}
          <div className="hidden md:flex flex-1 max-w-2xl mx-8 relative" ref={searchRef}>
            <div className="relative w-full">
              <div className="relative flex items-center border-2 border-gray-200 rounded-xl focus-within:border-primarycolor transition-all duration-200 bg-white shadow-sm">
                <Search className="h-5 w-5 text-gray-400 ml-4" />
                <input
                  type="text"
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch(searchQuery)}
                  className="w-full p-3 pl-3 pr-4 rounded-xl outline-none text-gray-900 placeholder-gray-500 bg-transparent"
                />
                {searchQuery && (
                  <button
                    onClick={() => handleSearch(searchQuery)}
                    className="absolute right-2 top-1/2 -translate-y-1/2 p-2 bg-primarycolor text-white rounded-lg hover:bg-primarycolor-700 transition-colors"
                  >
                    <Search className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>

            {/* Modern Search Results Dropdown */}
            {showSearchResults && searchResults.length > 0 && (
              <Card className="absolute top-full left-0 right-0 mt-2 shadow-xl z-50 max-h-96 overflow-y-auto border-0 rounded-xl">
                <div className="p-2">
                  {searchResults.map((product) => (
                    <Link
                      key={product.id}
                      href={`/product/${product.id}`}
                      className="flex items-center gap-4 p-3 rounded-xl hover:bg-gray-50 transition-all duration-200 group"
                      onClick={() => {
                        setShowSearchResults(false);
                        setSearchQuery('');
                      }}
                    >
                      <div className="w-14 h-14 bg-gray-100 rounded-xl overflow-hidden flex-shrink-0">
                        <Image
                          src={Array.isArray(product.image_url) ? product.image_url[0] : product.image_url}
                          alt={product.name}
                          width={56}
                          height={56}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-gray-900 truncate group-hover:text-primarycolor transition-colors">
                          {toTitleCase(product.name)}
                        </p>
                        <p className="text-primarycolor font-semibold">
                          Ksh. {Math.floor(product.price).toLocaleString()}
                        </p>
                      </div>
                    </Link>
                  ))}
                  <button
                    onClick={() => handleSearch(searchQuery)}
                    className="w-full text-center py-2 text-sm text-primarycolor hover:text-primarycolor-700 font-medium border-t border-gray-200 mt-2 pt-2"
                  >
                    View all results for &quot;{searchQuery}&quot;
                  </button>
                </div>
              </Card>
            )}
          </div>

          {/* Right Side Actions */}
          <div className="flex items-center gap-1 sm:gap-2">
            {/* Mobile Search Button */}
            <button
              onClick={() => {
                setMobileSearchOpen(!mobileSearchOpen);
                if (!mobileSearchOpen) {
                  setMobileMenuOpen(false); // Close hamburger menu when opening search
                }
              }}
              className="md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
              aria-label="Search"
            >
              <Search className="w-5 h-5 text-gray-600" />
            </button>

            {/* Wishlist - Hidden on small mobile */}
            <Link href="/profile/wishlist" className="relative hidden xs:block">
              <IconButton
                icon={<Heart className="w-5 h-5" />}
                variant="ghost"
                className="relative p-2"
                aria-label="Wishlist"
              />
              {wishlistItemsCount > 0 && (
                <span className="absolute -top-1 -right-1 w-4 h-4 sm:w-5 sm:h-5 bg-error text-white text-xs rounded-full flex items-center justify-center">
                  {wishlistItemsCount > 9 ? '9+' : wishlistItemsCount}
                </span>
              )}
            </Link>

            {/* Cart */}
            <Link href="/cart" className="relative">
              <IconButton
                icon={<ShoppingCart className="w-5 h-5" />}
                variant="ghost"
                className="relative p-2"
                aria-label="Shopping cart"
              />
              {cartItemsCount > 0 && (
                <span className="absolute -top-1 -right-1 w-4 h-4 sm:w-5 sm:h-5 bg-primarycolor text-white text-xs rounded-full flex items-center justify-center">
                  {cartItemsCount > 9 ? '9+' : cartItemsCount}
                </span>
              )}
            </Link>

            {/* User Menu */}
            {isAuthenticated ? (
              <div className="relative" ref={userMenuRef}>
                <button
                  onClick={() => setUserMenuOpen(!userMenuOpen)}
                  className="flex items-center gap-2 p-2 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="w-8 h-8 bg-primarycolor rounded-full flex items-center justify-center text-white text-sm font-medium">
                    {userDetails?.avatar_url ? (
                      <Image
                        src={userDetails.avatar_url}
                        alt="Profile"
                        width={32}
                        height={32}
                        className="w-full h-full rounded-full object-cover"
                      />
                    ) : (
                      getInitials(userDetails?.name || 'User')
                    )}
                  </div>
                  <ChevronDown className="w-4 h-4 text-gray-600" />
                </button>

                {userMenuOpen && (
                  <Card className="absolute top-full right-0 mt-2 w-64 p-4 shadow-lg z-50">
                    <div className="border-b border-gray-200 pb-3 mb-3">
                      <p className="font-medium text-gray-900">
                        {userDetails?.name || 'User'}
                      </p>
                      <p className="text-sm text-gray-600">
                        {userDetails?.email}
                      </p>
                    </div>
                    
                    <div className="space-y-1">
                      <Link
                        href="/profile"
                        className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 transition-colors"
                        onClick={() => setUserMenuOpen(false)}
                      >
                        <User className="w-4 h-4" />
                        <span>Profile</span>
                      </Link>
                      <Link
                        href="/profile/orders"
                        className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 transition-colors"
                        onClick={() => setUserMenuOpen(false)}
                      >
                        <ShoppingCart className="w-4 h-4" />
                        <span>Orders</span>
                      </Link>
                      <Link
                        href="/profile/wishlist"
                        className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 transition-colors"
                        onClick={() => setUserMenuOpen(false)}
                      >
                        <Heart className="w-4 h-4" />
                        <span>Wishlist</span>
                      </Link>
                      <button
                        onClick={handleSignOut}
                        className="w-full flex items-center gap-3 p-2 rounded-lg hover:bg-red-50 text-red-600 transition-colors"
                      >
                        <User className="w-4 h-4" />
                        <span>Sign Out</span>
                      </button>
                    </div>
                  </Card>
                )}
              </div>
            ) : (
              <div className="flex items-center gap-1 sm:gap-2">
                <Button variant="ghost" size="sm" className="hidden sm:flex">
                  <Link href="/auth/login">Sign In</Link>
                </Button>
                <Button size="sm" className="text-xs sm:text-sm px-2 sm:px-4">
                  <Link href="/auth/signup" className="sm:hidden">Join</Link>
                  <Link href="/auth/signup" className="hidden sm:block">Sign Up</Link>
                </Button>
              </div>
            )}

            {/* Mobile Menu Button */}
            <button
              onClick={() => {
                setMobileMenuOpen(!mobileMenuOpen);
                if (!mobileMenuOpen) {
                  setMobileSearchOpen(false); // Close animated search when opening menu
                }
              }}
              className="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
              aria-label="Toggle mobile menu"
            >
              {mobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Animated Mobile Search Bar */}
      <div className={cn(
        "md:hidden overflow-hidden transition-all duration-300 ease-in-out bg-white border-b border-gray-200",
        mobileSearchOpen ? "max-h-20 opacity-100" : "max-h-0 opacity-0"
      )}>
        <div className="p-4">
          <div className="relative flex items-center border-2 border-gray-200 rounded-xl focus-within:border-primarycolor transition-all duration-200 bg-white">
            <div className={cn(
              "transition-all duration-300 ease-in-out",
              mobileSearchOpen ? "ml-4" : "ml-0"
            )}>
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search products..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch(searchQuery)}
              className="w-full p-3 pl-3 pr-4 rounded-xl outline-none text-gray-900 placeholder-gray-500 bg-transparent"
            />
            {searchQuery && (
              <button
                onClick={() => handleSearch(searchQuery)}
                className="absolute right-2 top-1/2 -translate-y-1/2 p-2 bg-primarycolor text-white rounded-lg hover:bg-primarycolor-700 transition-colors"
              >
                <Search className="w-4 h-4" />
              </button>
            )}
          </div>

          {/* Mobile Search Results */}
          {showSearchResults && searchResults.length > 0 && (
            <div className="absolute left-4 right-4 top-full mt-2 bg-white border border-gray-200 rounded-xl shadow-lg z-50 max-h-80 overflow-y-auto">
              <div className="p-2">
                {searchResults.map((product) => (
                  <Link
                    key={product.id}
                    href={`/product/${product.id}`}
                    className="flex items-center gap-3 p-3 rounded-xl hover:bg-gray-50 transition-all duration-200 group"
                    onClick={() => {
                      setShowSearchResults(false);
                      setSearchQuery('');
                      setMobileSearchOpen(false);
                    }}
                  >
                    <div className="w-12 h-12 bg-gray-100 rounded-xl overflow-hidden flex-shrink-0">
                      <Image
                        src={Array.isArray(product.image_url) ? product.image_url[0] : product.image_url}
                        alt={product.name}
                        width={48}
                        height={48}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-gray-900 truncate group-hover:text-primarycolor transition-colors text-sm">
                        {toTitleCase(product.name)}
                      </p>
                      <p className="text-primarycolor font-semibold text-sm">
                        Ksh. {Math.floor(product.price).toLocaleString()}
                      </p>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Mobile Menu */}
      {mobileMenuOpen && (
        <div className="lg:hidden border-t border-gray-200 bg-white shadow-lg">
          <div className="px-4 py-4 space-y-6">
            {/* Mobile Search */}
            <div className="relative">
              <div className="relative flex items-center border-2 border-gray-200 rounded-xl focus-within:border-primarycolor transition-all duration-200 bg-white">
                <Search className="h-5 w-5 text-gray-400 ml-4" />
                <input
                  type="text"
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch(searchQuery)}
                  className="w-full p-3 pl-3 pr-4 rounded-xl outline-none text-gray-900 placeholder-gray-500 bg-transparent"
                />
                {searchQuery && (
                  <button
                    onClick={() => handleSearch(searchQuery)}
                    className="absolute right-2 top-1/2 -translate-y-1/2 p-2 bg-primarycolor text-white rounded-lg hover:bg-primarycolor-700 transition-colors"
                  >
                    <Search className="w-4 h-4" />
                  </button>
                )}
              </div>

              {/* Mobile Menu Search Results */}
              {showSearchResults && searchResults.length > 0 && (
                <div className="mt-2 bg-gray-50 rounded-xl p-2 max-h-60 overflow-y-auto">
                  {searchResults.map((product) => (
                    <Link
                      key={product.id}
                      href={`/product/${product.id}`}
                      className="flex items-center gap-3 p-2 rounded-lg hover:bg-white transition-all duration-200 group"
                      onClick={() => {
                        setShowSearchResults(false);
                        setSearchQuery('');
                        setMobileMenuOpen(false);
                      }}
                    >
                      <div className="w-10 h-10 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                        <Image
                          src={Array.isArray(product.image_url) ? product.image_url[0] : product.image_url}
                          alt={product.name}
                          width={40}
                          height={40}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-gray-900 truncate text-sm">
                          {toTitleCase(product.name)}
                        </p>
                        <p className="text-primarycolor font-semibold text-sm">
                          Ksh. {Math.floor(product.price).toLocaleString()}
                        </p>
                      </div>
                    </Link>
                  ))}
                </div>
              )}
            </div>

            {/* Mobile Categories */}
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Categories</h3>
              <div className="grid grid-cols-2 gap-2">
                {categories.slice(0, 6).map((category) => (
                  <Link
                    key={category.id}
                    href={getCategoryPath(category)}
                    className="flex items-center gap-2 p-2 rounded-lg hover:bg-gray-50 transition-colors"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    {category.image_url ? (
                      <Image
                        src={category.image_url}
                        alt={category.name}
                        width={20}
                        height={20}
                        className="w-5 h-5 object-cover rounded"
                      />
                    ) : (
                      <div className="w-5 h-5 bg-primarycolor-100 rounded flex items-center justify-center">
                        <span className="text-xs text-primarycolor">
                          {category.name.charAt(0)}
                        </span>
                      </div>
                    )}
                    <span className="text-sm font-medium text-gray-900">
                      {category.name}
                    </span>
                  </Link>
                ))}
              </div>
            </div>

            {/* Mobile User Actions */}
            {!isAuthenticated && (
              <div className="flex gap-3 pt-4 border-t border-gray-200">
                <Button variant="outline" fullWidth>
                  <Link href="/auth/login">Sign In</Link>
                </Button>
                <Button fullWidth>
                  <Link href="/auth/signup">Sign Up</Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      )}
    </header>
  );
}

// Breadcrumb Component
export function Breadcrumb({ items = [], className }) {
  if (!items.length) return null;

  return (
    <nav className={cn("flex items-center space-x-2 text-sm text-gray-600", className)}>
      <Link href="/" className="hover:text-primarycolor transition-colors">
        Home
      </Link>
      {items.map((item, index) => (
        <React.Fragment key={index}>
          <span className="text-gray-400">/</span>
          {item.href ? (
            <Link
              href={item.href}
              className="hover:text-primarycolor transition-colors"
            >
              {item.label}
            </Link>
          ) : (
            <span className="text-gray-900 font-medium">{item.label}</span>
          )}
        </React.Fragment>
      ))}
    </nav>
  );
}
