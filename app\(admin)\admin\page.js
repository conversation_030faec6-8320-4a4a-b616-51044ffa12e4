"use client";
import SalesGraph from './components/salesgraph';
import { useSupabase } from './hooks/useSupabase';
import { withAdminAuth } from './components/withAdminAuth';
import EnhancedDashboard from '../../components/admin/EnhancedDashboard';

function AdminDashboard() {
  const { useDashboardData } = useSupabase();
  const { data: dashboardData, isLoading } = useDashboardData();

  if (isLoading) return <div className='text-center text-primarycolor'>Loading dashboard data...</div>;

  return <EnhancedDashboard dashboardData={dashboardData} isLoading={isLoading} />;
}

export default withAdminAuth(AdminDashboard);