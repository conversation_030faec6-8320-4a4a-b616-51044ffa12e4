import { useQuery } from '@tanstack/react-query';
import { useSupabase } from './useSupabase';

export function useProducts() {
  const { fetchProducts } = useSupabase();
  return useQuery({
    queryKey: ['products'],
    queryFn: fetchProducts,
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    cacheTime: 30 * 60 * 1000, // Keep unused data in cache for 30 minutes
  });
}

export function useCategories() {
  const { fetchCategories } = useSupabase();
  return useQuery({
    queryKey: ['categories'],
    queryFn: fetchCategories,
    staleTime: 10 * 60 * 1000, // Categories change less frequently
    cacheTime: 60 * 60 * 1000,
  });
}

export function useBanners() {
  const { fetchMarketingBanners } = useSupabase();
  return useQuery({
    queryKey: ['banners'],
    queryFn: fetchMarketingBanners,
    staleTime: 15 * 60 * 1000, // Consider data fresh for 15 minutes
    cacheTime: 60 * 60 * 1000, // Keep unused data in cache for 1 hour
  });
}

// Add to existing useQueries.js
export function useDashboardData() {
  const { fetchDashboardData } = useSupabase();
  return useQuery({
    queryKey: ['dashboard'],
    queryFn: fetchDashboardData,
    staleTime: 5 * 60 * 1000,
    cacheTime: 30 * 60 * 1000,
  });
}

export function useUserData(userId) {
  return useQuery({
    queryKey: ['user', userId],
    queryFn: () => fetchUserDetails(userId),
    enabled: !!userId,
    staleTime: 30000,
    cacheTime: 3600000
  });
}

export function useCartItems(userId) {
  const { fetchCartItems } = useSupabase();
  return useQuery({
    queryKey: ['cart', userId],
    queryFn: () => fetchCartItems(userId),
    enabled: !!userId,
    staleTime: 2 * 60 * 1000,
  });
}


export function useProductsByCategory(categorySlug) {
  const { fetchProductsByCategory } = useSupabase();
  return useQuery({
    queryKey: ['products', 'category', categorySlug],
    queryFn: () => fetchProductsByCategory(categorySlug),
    staleTime: 5 * 60 * 1000, // Data stays fresh for 5 minutes
    cacheTime: 30 * 60 * 1000, // Cached for 30 minutes
  });
}

export function useProduct(productId) {
  const { fetchProductById } = useSupabase();
  return useQuery({
    queryKey: ['product', productId],
    queryFn: () => fetchProductById(productId),
    enabled: !!productId,
    staleTime: 5 * 60 * 1000,
    cacheTime: 30 * 60 * 1000,
  });
}
// Add these new query hooks
export function usePromotedProducts() {
  const { fetchPromotedProducts } = useSupabase();
  return useQuery({
    queryKey: ['promoted-products'],
    queryFn: fetchPromotedProducts,
    staleTime: 5 * 60 * 1000,
    cacheTime: 30 * 60 * 1000,
  });
}

export function useRelatedPromotedProducts(categoryName, productId) {
  const { fetchRelatedPromotedProducts } = useSupabase();
  return useQuery({
    queryKey: ['related-promoted-products', categoryName, productId],
    queryFn: () => fetchRelatedPromotedProducts(categoryName, productId),
    enabled: !!categoryName && !!productId,
    staleTime: 5 * 60 * 1000,
    cacheTime: 30 * 60 * 1000,
  });
}
