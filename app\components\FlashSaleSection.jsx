"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { ArrowRight, Clock, Heart } from 'lucide-react';
import EnhancedProductCard from './EnhancedProductCard';
import { cn } from '../lib/utils';

export default function FlashSaleSection({ products = [], endTime }) {
  const [timeLeft, setTimeLeft] = useState({
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  useEffect(() => {
    if (!endTime) return;

    const timer = setInterval(() => {
      const now = new Date().getTime();
      const end = new Date(endTime).getTime();
      const difference = end - now;

      if (difference > 0) {
        setTimeLeft({
          hours: Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((difference % (1000 * 60)) / 1000)
        });
      } else {
        setTimeLeft({ hours: 0, minutes: 0, seconds: 0 });
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [endTime]);

  if (!products.length) return null;

  // Default end time if not provided (24 hours from now)
  const defaultEndTime = endTime || new Date(Date.now() + 24 * 60 * 60 * 1000);

  return (
    <section className="py-6 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <h2 className="text-xl font-semibold text-gray-900">Flash Sale</h2>
            <div className="bg-primarycolor text-white px-3 py-1 rounded-lg text-sm font-medium">
              {String(timeLeft.hours).padStart(2, '0')}:
              {String(timeLeft.minutes).padStart(2, '0')}:
              {String(timeLeft.seconds).padStart(2, '0')}
            </div>
          </div>
          <Link 
            href="/flash-sale" 
            className="text-sm text-gray-600 hover:text-primarycolor transition-colors flex items-center gap-1"
          >
            See all
            <ArrowRight className="w-4 h-4" />
          </Link>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
          {products.slice(0, 6).map((product) => (
            <FlashSaleProductCard key={product.id} product={product} />
          ))}
        </div>
      </div>
    </section>
  );
}

// Flash Sale Product Card Component
function FlashSaleProductCard({ product }) {
  const [isWishlisted, setIsWishlisted] = useState(false);
  
  const discountPercentage = product.discount || 0;
  const originalPrice = product.price;
  const salePrice = originalPrice * (1 - discountPercentage / 100);

  const handleWishlistToggle = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsWishlisted(!isWishlisted);
  };

  return (
    <Link href={`/product/${product.id}`} className="group block">
      <div className="bg-white rounded-2xl overflow-hidden border border-gray-100 hover:shadow-md transition-all duration-200">
        {/* Image Container */}
        <div className="relative aspect-square bg-gray-50 overflow-hidden">
          <img
            src={Array.isArray(product.image_url) ? product.image_url[0] : product.image_url}
            alt={product.name}
            className="w-full h-full object-contain group-hover:scale-105 transition-transform duration-300"
          />
          
          {/* Wishlist Button */}
          <button
            onClick={handleWishlistToggle}
            className="absolute top-3 right-3 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-sm hover:shadow-md transition-all"
          >
            <Heart 
              className={cn(
                "w-4 h-4 transition-colors",
                isWishlisted ? "text-red-500 fill-current" : "text-gray-400"
              )} 
            />
          </button>

          {/* Discount Badge */}
          {discountPercentage > 0 && (
            <div className="absolute top-3 left-3 bg-red-500 text-white px-2 py-1 rounded-lg text-xs font-medium">
              -{discountPercentage}%
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className="p-3">
          <h3 className="text-sm font-medium text-gray-900 line-clamp-2 mb-2 group-hover:text-primarycolor transition-colors">
            {product.name}
          </h3>
          
          <div className="flex items-center gap-2">
            <span className="text-lg font-bold text-gray-900">
              Ksh. {Math.floor(salePrice).toLocaleString()}
            </span>
            {discountPercentage > 0 && (
              <span className="text-sm text-gray-500 line-through">
                Ksh. {Math.floor(originalPrice).toLocaleString()}
              </span>
            )}
          </div>
        </div>
      </div>
    </Link>
  );
}
