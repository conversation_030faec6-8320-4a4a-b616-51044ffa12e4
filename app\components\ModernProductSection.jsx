"use client";

import React from 'react';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';
import EnhancedProductCard from './EnhancedProductCard';
import { cn } from '../lib/utils';

export default function ModernProductSection({ 
  title, 
  products = [], 
  viewAllLink,
  className,
  showViewAll = true,
  compact = false 
}) {
  if (!products.length) return null;

  return (
    <section className={cn("py-6 bg-white", className)}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
          {showViewAll && viewAllLink && (
            <Link 
              href={viewAllLink} 
              className="text-sm text-gray-600 hover:text-primarycolor transition-colors flex items-center gap-1"
            >
              See all
              <ArrowRight className="w-4 h-4" />
            </Link>
          )}
        </div>

        {/* Products Grid */}
        <div className={cn(
          "grid gap-4",
          compact 
            ? "grid-cols-2 md:grid-cols-4 lg:grid-cols-6" 
            : "grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5"
        )}>
          {products.map((product) => (
            <EnhancedProductCard
              key={product.id}
              product={product}
              compact={compact}
            />
          ))}
        </div>
      </div>
    </section>
  );
}

// Horizontal Scrolling Product Section
export function HorizontalProductSection({ 
  title, 
  products = [], 
  viewAllLink,
  className,
  showViewAll = true 
}) {
  if (!products.length) return null;

  return (
    <section className={cn("py-6 bg-white", className)}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
          {showViewAll && viewAllLink && (
            <Link 
              href={viewAllLink} 
              className="text-sm text-gray-600 hover:text-primarycolor transition-colors flex items-center gap-1"
            >
              See all
              <ArrowRight className="w-4 h-4" />
            </Link>
          )}
        </div>

        {/* Horizontal Scroll Container */}
        <div className="overflow-x-auto scrollbar-hide">
          <div className="flex gap-4 pb-4" style={{ width: 'max-content' }}>
            {products.map((product) => (
              <div key={product.id} className="flex-shrink-0 w-48">
                <EnhancedProductCard
                  product={product}
                  compact
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
