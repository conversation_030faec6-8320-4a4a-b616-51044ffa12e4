// contexts/CartContext.js
"use client";
import { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import { supabase } from '../lib/supabase';

const CartContext = createContext();

export function CartProvider({ children }) {
  const [cartItems, setCartItems] = useState([]);
  const [cartCount, setCartCount] = useState(0);
  const { user } = useAuth();

  useEffect(() => {
    setCartCount(cartItems.reduce((acc, item) => acc + item.quantity, 0));
  }, [cartItems]);

  useEffect(() => {
    const loadCartItems = async () => {
      if (user) {
        // Load from database first
        const { data } = await supabase
          .from('cart')
          .select(`
            *, 
            products(
              id,
              name,
              price,
              image_url,
              is_promoted,
              promotion_end_date
            )
          `)
          .eq('user_id', user.id);

        if (data) {
          const formattedCart = data.map(item => ({
            productId: item.product_id,
            quantity: item.quantity,
            product: {
              ...item.products,
              is_promoted: item.products.is_promoted || false
            }
          }));
          setCartItems(formattedCart);
          localStorage.setItem('cart', JSON.stringify(formattedCart));
        }
      } else {
        // Load from localStorage for guest users
        const savedCart = JSON.parse(localStorage.getItem('cart') || '[]');
        setCartItems(savedCart);
      }
    };

    loadCartItems();
  }, [user]);

  const addToCart = async (product, quantity = 1) => {
    const newItem = {
      productId: product.id,
      quantity,
      product: {
        ...product,
        is_promoted: product.is_promoted || false
      }
    };

    const existingItemIndex = cartItems.findIndex(item => item.productId === product.id);
    let updatedCart;

    if (existingItemIndex >= 0) {
      updatedCart = cartItems.map((item, index) => 
        index === existingItemIndex 
          ? { ...item, quantity: item.quantity + quantity }
          : item
      );
    } else {
      updatedCart = [...cartItems, newItem];
    }

    setCartItems(updatedCart);
    localStorage.setItem('cart', JSON.stringify(updatedCart));

    if (user) {
      await syncCartWithDb(updatedCart);
    }
  };

  const updateCart = async (newCart) => {
    setCartItems(newCart);
    localStorage.setItem('cart', JSON.stringify(newCart));

    if (user) {
      await syncCartWithDb(newCart);
    }
  };

  const syncCartWithDb = async (cart) => {
    if (!user?.id) return;

    try {
      if (cart.length === 0) {
        // If cart is empty, clear all items for this user
        const { error } = await supabase
          .from('cart')
          .delete()
          .eq('user_id', user.id);

        if (error) throw error;
        return;
      }

      // Prepare cart data for upsert
      const cartData = cart.map(item => ({
        user_id: user.id,
        product_id: item.productId,
        quantity: item.quantity,
        updated_at: new Date().toISOString()
      }));

      // Use upsert to handle both inserts and updates efficiently
      const { error } = await supabase
        .from('cart')
        .upsert(cartData, {
          onConflict: 'user_id,product_id',
          ignoreDuplicates: false
        });

      if (error) throw error;

      // Remove items that are no longer in the cart
      const currentProductIds = cart.map(item => item.productId);
      if (currentProductIds.length > 0) {
        const { error: deleteError } = await supabase
          .from('cart')
          .delete()
          .eq('user_id', user.id)
          .not('product_id', 'in', `(${currentProductIds.join(',')})`);

        if (deleteError) throw deleteError;
      }
    } catch (error) {
      console.error('Error syncing cart with database:', error);
      // Don't throw the error to prevent UI disruption
      // Consider showing a toast notification instead
    }
  };

  return (
    <CartContext.Provider value={{
      cartItems,
      cartCount,
      addToCart,
      updateCart,
    }}>
      {children}
    </CartContext.Provider>
  );
}

export const useCart = () => useContext(CartContext);
