"use client";

import React, { useContext, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Heart, ShoppingCart, Trash2, Share2, Filter, Grid, List } from 'lucide-react';
import { WishlistContext } from '../../context/wishlistContext';
import { useAuth } from '../../hooks/useAuth';
import EnhancedProductCard from '../EnhancedProductCard';
import Button, { IconButton } from '../ui/Button';
import Card, { CardContent } from '../ui/Card';
import { SearchInput, Select } from '../ui/Input';
import { cn, formatCurrency } from '../../lib/utils';
import { toast } from 'sonner';

export default function WishlistPage() {
  const { wishlistItems, toggleWishlistItem, clearWishlist } = useContext(WishlistContext);
  const { isAuthenticated } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('added_date');
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [selectedItems, setSelectedItems] = useState(new Set());

  // Filter and sort wishlist items
  const filteredItems = wishlistItems
    .filter(item => 
      item.name.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'price_low':
          return a.price - b.price;
        case 'price_high':
          return b.price - a.price;
        case 'added_date':
        default:
          return new Date(b.added_at || 0) - new Date(a.added_at || 0);
      }
    });

  const handleSelectItem = (itemId) => {
    const newSelected = new Set(selectedItems);
    if (newSelected.has(itemId)) {
      newSelected.delete(itemId);
    } else {
      newSelected.add(itemId);
    }
    setSelectedItems(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedItems.size === filteredItems.length) {
      setSelectedItems(new Set());
    } else {
      setSelectedItems(new Set(filteredItems.map(item => item.id)));
    }
  };

  const handleRemoveSelected = () => {
    selectedItems.forEach(itemId => {
      const item = wishlistItems.find(i => i.id === itemId);
      if (item) {
        toggleWishlistItem(item);
      }
    });
    setSelectedItems(new Set());
    toast.success(`Removed ${selectedItems.size} items from wishlist`);
  };

  const handleAddAllToCart = () => {
    // TODO: Implement add all to cart functionality
    toast.success('Added all items to cart');
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: 'My Wishlist',
        text: 'Check out my wishlist on Pinchez Merchants',
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      toast.success('Wishlist link copied to clipboard');
    }
  };

  if (!isAuthenticated) {
    return (
      <Card className="p-8 text-center">
        <Heart className="w-16 h-16 text-gray-300 mx-auto mb-4" />
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">
          Sign in to view your wishlist
        </h2>
        <p className="text-gray-600 mb-6">
          Save your favorite items and access them from any device
        </p>
        <Button>
          <Link href="/auth/login">Sign In</Link>
        </Button>
      </Card>
    );
  }

  if (wishlistItems.length === 0) {
    return (
      <Card className="p-8 text-center">
        <Heart className="w-16 h-16 text-gray-300 mx-auto mb-4" />
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">
          Your wishlist is empty
        </h2>
        <p className="text-gray-600 mb-6">
          Start adding items to your wishlist by clicking the heart icon on products you love
        </p>
        <Button>
          <Link href="/">Start Shopping</Link>
        </Button>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <Card className="p-4">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">
              My Wishlist ({wishlistItems.length} items)
            </h2>
            <p className="text-sm text-gray-600">
              Total value: {formatCurrency(wishlistItems.reduce((sum, item) => sum + item.price, 0))}
            </p>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              leftIcon={<Share2 className="w-4 h-4" />}
              onClick={handleShare}
            >
              Share
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              leftIcon={<ShoppingCart className="w-4 h-4" />}
              onClick={handleAddAllToCart}
            >
              Add All to Cart
            </Button>

            {selectedItems.size > 0 && (
              <Button
                variant="error"
                size="sm"
                leftIcon={<Trash2 className="w-4 h-4" />}
                onClick={handleRemoveSelected}
              >
                Remove Selected ({selectedItems.size})
              </Button>
            )}
          </div>
        </div>
      </Card>

      {/* Filters and Search */}
      <Card className="p-4">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex-1 max-w-md">
            <SearchInput
              placeholder="Search wishlist..."
              onSearch={setSearchTerm}
              className="w-full"
            />
          </div>

          <div className="flex items-center gap-3">
            {/* Sort */}
            <Select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="w-40"
            >
              <option value="added_date">Recently Added</option>
              <option value="name">Name A-Z</option>
              <option value="price_low">Price: Low to High</option>
              <option value="price_high">Price: High to Low</option>
            </Select>

            {/* View Mode */}
            <div className="flex border border-gray-300 rounded-lg overflow-hidden">
              <button
                className={cn(
                  "p-2 transition-colors",
                  viewMode === 'grid' 
                    ? "bg-primarycolor text-white" 
                    : "bg-white text-gray-600 hover:bg-gray-50"
                )}
                onClick={() => setViewMode('grid')}
              >
                <Grid className="w-4 h-4" />
              </button>
              <button
                className={cn(
                  "p-2 transition-colors",
                  viewMode === 'list' 
                    ? "bg-primarycolor text-white" 
                    : "bg-white text-gray-600 hover:bg-gray-50"
                )}
                onClick={() => setViewMode('list')}
              >
                <List className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Bulk Actions */}
        {filteredItems.length > 0 && (
          <div className="flex items-center gap-4 mt-4 pt-4 border-t border-gray-200">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={selectedItems.size === filteredItems.length && filteredItems.length > 0}
                onChange={handleSelectAll}
                className="w-4 h-4 text-primarycolor border-gray-300 rounded focus:ring-primarycolor"
              />
              <span className="text-sm text-gray-600">
                Select all ({filteredItems.length})
              </span>
            </label>

            {selectedItems.size > 0 && (
              <span className="text-sm text-gray-600">
                {selectedItems.size} selected
              </span>
            )}
          </div>
        )}
      </Card>

      {/* Wishlist Items */}
      {filteredItems.length === 0 ? (
        <Card className="p-8 text-center">
          <p className="text-gray-600">No items match your search criteria</p>
        </Card>
      ) : (
        <div className={cn(
          viewMode === 'grid' 
            ? "grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
            : "space-y-4"
        )}>
          {filteredItems.map((item) => (
            <div key={item.id} className="relative">
              {viewMode === 'grid' ? (
                <>
                  {/* Selection checkbox for grid view */}
                  <div className="absolute top-2 left-2 z-10">
                    <input
                      type="checkbox"
                      checked={selectedItems.has(item.id)}
                      onChange={() => handleSelectItem(item.id)}
                      className="w-4 h-4 text-primarycolor border-gray-300 rounded focus:ring-primarycolor bg-white"
                    />
                  </div>
                  
                  <EnhancedProductCard
                    product={item}
                    showQuickActions={false}
                  />
                </>
              ) : (
                /* List view */
                <Card className="p-4">
                  <div className="flex items-center gap-4">
                    <input
                      type="checkbox"
                      checked={selectedItems.has(item.id)}
                      onChange={() => handleSelectItem(item.id)}
                      className="w-4 h-4 text-primarycolor border-gray-300 rounded focus:ring-primarycolor"
                    />
                    
                    <div className="w-20 h-20 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                      <Image
                        src={Array.isArray(item.image_url) ? item.image_url[0] : item.image_url}
                        alt={item.name}
                        width={80}
                        height={80}
                        className="w-full h-full object-cover"
                      />
                    </div>

                    <div className="flex-1 min-w-0">
                      <Link
                        href={`/product/${item.id}`}
                        className="font-medium text-gray-900 hover:text-primarycolor line-clamp-2"
                      >
                        {item.name}
                      </Link>
                      <p className="text-sm text-gray-500 mt-1">
                        Added {new Date(item.added_at).toLocaleDateString()}
                      </p>
                      <p className="text-lg font-semibold text-primarycolor mt-2">
                        {formatCurrency(item.price)}
                      </p>
                    </div>

                    <div className="flex items-center gap-2">
                      <IconButton
                        icon={<ShoppingCart className="w-4 h-4" />}
                        variant="primary"
                        size="sm"
                        aria-label="Add to cart"
                      />
                      
                      <IconButton
                        icon={<Heart className="w-4 h-4 fill-current text-error" />}
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleWishlistItem(item)}
                        aria-label="Remove from wishlist"
                      />
                    </div>
                  </div>
                </Card>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
