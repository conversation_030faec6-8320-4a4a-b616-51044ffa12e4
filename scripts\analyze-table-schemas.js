#!/usr/bin/env node

/**
 * Analyze actual table schemas by querying sample data
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
);

async function analyzeTableSchema(tableName) {
  console.log(`\n📊 Analyzing ${tableName} table...`);
  
  try {
    // Get a sample row to understand the schema
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .limit(1);
    
    if (error) {
      console.log(`❌ Error accessing ${tableName}:`, error.message);
      return null;
    }
    
    if (!data || data.length === 0) {
      console.log(`⚠️  ${tableName} table is empty`);
      return { columns: [], sample: null };
    }
    
    const sample = data[0];
    const columns = Object.keys(sample).map(key => ({
      name: key,
      type: typeof sample[key],
      sample_value: sample[key]
    }));
    
    console.log(`✅ ${tableName} has ${columns.length} columns:`);
    columns.forEach(col => {
      console.log(`   • ${col.name} (${col.type}): ${JSON.stringify(col.sample_value)}`);
    });
    
    return { columns, sample };
    
  } catch (err) {
    console.log(`❌ Error analyzing ${tableName}:`, err.message);
    return null;
  }
}

async function main() {
  console.log('🔍 Analyzing table schemas...');
  
  const tables = [
    'users', 'products', 'categories', 'orders', 
    'cart', 'wishlists', 'marketing_banners', 'order_items'
  ];
  
  const schemas = {};
  
  for (const table of tables) {
    schemas[table] = await analyzeTableSchema(table);
  }
  
  // Save results
  const fs = require('fs');
  const path = require('path');
  
  const outputDir = path.join(__dirname, '..', 'database-structure');
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  const schemaFile = path.join(outputDir, 'table-schemas.json');
  fs.writeFileSync(schemaFile, JSON.stringify(schemas, null, 2));
  
  console.log(`\n✅ Table schemas saved to: ${schemaFile}`);
  
  // Print summary
  console.log('\n📋 SCHEMA SUMMARY');
  console.log('=================');
  
  Object.entries(schemas).forEach(([table, schema]) => {
    if (schema && schema.columns) {
      console.log(`\n${table.toUpperCase()}:`);
      schema.columns.forEach(col => {
        console.log(`  ${col.name}: ${col.type}`);
      });
    }
  });
}

main().catch(console.error);
