"use client";
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useState } from 'react';
import Sidebar from './components/sidebar';
import Navbar from './components/navbar';
import { Toaster } from 'sonner';
import { withAdminAuth } from './components/withAdminAuth';

const queryClient = new QueryClient();

function AdminLayout({ children }) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  return (
    <QueryClientProvider client={queryClient}>
      <div className="flex min-h-screen bg-gray-50">
        <Sidebar isOpen={isSidebarOpen} setIsOpen={setIsSidebarOpen} />
        <div className="flex-1">
          <Navbar toggleSidebar={toggleSidebar} />
          <main className="p-4">{children}</main>
          <Toaster position="top-right" />
        </div>
      </div>
    </QueryClientProvider>
  );
}

export default withAdminAuth(AdminLayout);
