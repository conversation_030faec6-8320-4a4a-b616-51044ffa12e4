import { NextResponse } from 'next/server';

/**
 * Rate Limiting Implementation
 * Simple in-memory rate limiter for API routes
 */
class RateLimiter {
  constructor() {
    this.requests = new Map();
    this.cleanup();
  }

  // Clean up old entries every 5 minutes
  cleanup() {
    setInterval(() => {
      const now = Date.now();
      for (const [key, data] of this.requests.entries()) {
        if (now - data.resetTime > 0) {
          this.requests.delete(key);
        }
      }
    }, 5 * 60 * 1000);
  }

  check(identifier, limit = 100, windowMs = 15 * 60 * 1000) {
    const now = Date.now();
    const key = identifier;
    
    if (!this.requests.has(key)) {
      this.requests.set(key, {
        count: 1,
        resetTime: now + windowMs
      });
      return { allowed: true, remaining: limit - 1, resetTime: now + windowMs };
    }

    const data = this.requests.get(key);
    
    if (now > data.resetTime) {
      // Reset the window
      this.requests.set(key, {
        count: 1,
        resetTime: now + windowMs
      });
      return { allowed: true, remaining: limit - 1, resetTime: now + windowMs };
    }

    if (data.count >= limit) {
      return { 
        allowed: false, 
        remaining: 0, 
        resetTime: data.resetTime,
        retryAfter: Math.ceil((data.resetTime - now) / 1000)
      };
    }

    data.count++;
    return { 
      allowed: true, 
      remaining: limit - data.count, 
      resetTime: data.resetTime 
    };
  }
}

const rateLimiter = new RateLimiter();

/**
 * Rate limiting middleware for API routes
 */
export function withRateLimit(handler, options = {}) {
  const {
    limit = 100,
    windowMs = 15 * 60 * 1000, // 15 minutes
    keyGenerator = (req) => getClientIP(req),
    onLimitReached = null
  } = options;

  return async (req, ...args) => {
    const identifier = keyGenerator(req);
    const result = rateLimiter.check(identifier, limit, windowMs);

    // Add rate limit headers
    const headers = {
      'X-RateLimit-Limit': limit.toString(),
      'X-RateLimit-Remaining': result.remaining.toString(),
      'X-RateLimit-Reset': new Date(result.resetTime).toISOString()
    };

    if (!result.allowed) {
      if (onLimitReached) {
        await onLimitReached(req, result);
      }

      return NextResponse.json(
        { 
          error: 'Too many requests', 
          message: `Rate limit exceeded. Try again in ${result.retryAfter} seconds.`,
          retryAfter: result.retryAfter
        },
        { 
          status: 429,
          headers: {
            ...headers,
            'Retry-After': result.retryAfter.toString()
          }
        }
      );
    }

    // Continue with the original handler
    const response = await handler(req, ...args);
    
    // Add rate limit headers to successful responses
    if (response instanceof NextResponse) {
      Object.entries(headers).forEach(([key, value]) => {
        response.headers.set(key, value);
      });
    }

    return response;
  };
}

/**
 * Get client IP address from request
 */
export function getClientIP(req) {
  const forwarded = req.headers.get('x-forwarded-for');
  const realIP = req.headers.get('x-real-ip');
  const cfConnectingIP = req.headers.get('cf-connecting-ip');
  
  if (cfConnectingIP) return cfConnectingIP;
  if (realIP) return realIP;
  if (forwarded) return forwarded.split(',')[0].trim();
  
  return 'unknown';
}

/**
 * CSRF Protection
 */
export class CSRFProtection {
  constructor() {
    this.tokens = new Map();
    this.cleanup();
  }

  cleanup() {
    setInterval(() => {
      const now = Date.now();
      for (const [token, expiry] of this.tokens.entries()) {
        if (now > expiry) {
          this.tokens.delete(token);
        }
      }
    }, 10 * 60 * 1000); // Clean up every 10 minutes
  }

  generateToken(sessionId) {
    const token = this.randomString(32);
    const expiry = Date.now() + (60 * 60 * 1000); // 1 hour
    this.tokens.set(token, { sessionId, expiry });
    return token;
  }

  validateToken(token, sessionId) {
    const data = this.tokens.get(token);
    if (!data) return false;
    if (Date.now() > data.expiry) {
      this.tokens.delete(token);
      return false;
    }
    if (data.sessionId !== sessionId) return false;
    return true;
  }

  randomString(length) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
}

const csrfProtection = new CSRFProtection();

/**
 * CSRF middleware for API routes
 */
export function withCSRFProtection(handler) {
  return async (req, ...args) => {
    // Skip CSRF for GET requests
    if (req.method === 'GET') {
      return handler(req, ...args);
    }

    const token = req.headers.get('x-csrf-token');
    const sessionId = req.headers.get('x-session-id') || getClientIP(req);

    if (!token || !csrfProtection.validateToken(token, sessionId)) {
      return NextResponse.json(
        { error: 'Invalid CSRF token' },
        { status: 403 }
      );
    }

    return handler(req, ...args);
  };
}

/**
 * Generate CSRF token for client
 */
export function generateCSRFToken(sessionId) {
  return csrfProtection.generateToken(sessionId);
}

/**
 * Input Sanitization
 */
export function sanitizeInput(input) {
  if (typeof input !== 'string') return input;
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .substring(0, 1000); // Limit length
}

/**
 * SQL Injection Prevention (for raw queries)
 */
export function escapeSQLString(str) {
  if (typeof str !== 'string') return str;
  return str.replace(/'/g, "''").replace(/;/g, '');
}

/**
 * Request Validation Middleware
 */
export function withRequestValidation(handler, schema) {
  return async (req, ...args) => {
    try {
      let body = {};
      
      if (req.method !== 'GET' && req.headers.get('content-type')?.includes('application/json')) {
        body = await req.json();
      }

      // Validate request body against schema
      if (schema) {
        const result = schema.safeParse(body);
        if (!result.success) {
          return NextResponse.json(
            { 
              error: 'Validation failed',
              details: result.error.errors
            },
            { status: 400 }
          );
        }
        // Replace body with validated data
        req.validatedBody = result.data;
      }

      return handler(req, ...args);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid request format' },
        { status: 400 }
      );
    }
  };
}

/**
 * Security Headers Middleware
 */
export function withSecurityHeaders(handler) {
  return async (req, ...args) => {
    const response = await handler(req, ...args);
    
    if (response instanceof NextResponse) {
      // Add security headers
      response.headers.set('X-Content-Type-Options', 'nosniff');
      response.headers.set('X-Frame-Options', 'DENY');
      response.headers.set('X-XSS-Protection', '1; mode=block');
      response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
      response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
      
      // Content Security Policy
      const csp = [
        "default-src 'self'",
        "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://vercel.live",
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
        "font-src 'self' https://fonts.gstatic.com",
        "img-src 'self' data: https: blob:",
        "connect-src 'self' https://api.supabase.co wss://realtime.supabase.co",
        "frame-src 'none'"
      ].join('; ');
      
      response.headers.set('Content-Security-Policy', csp);
    }
    
    return response;
  };
}

/**
 * Combine multiple middleware functions
 */
export function combineMiddleware(...middlewares) {
  return (handler) => {
    return middlewares.reduceRight((acc, middleware) => {
      return middleware(acc);
    }, handler);
  };
}

/**
 * Client-side security utilities
 */
export const clientSecurity = {
  // Sanitize user input on the client side
  sanitizeInput: (input) => {
    if (typeof input !== 'string') return input;
    return input.trim().replace(/[<>]/g, '').substring(0, 1000);
  },

  // Validate email format
  isValidEmail: (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // Validate phone number (Kenyan format)
  isValidPhone: (phone) => {
    const phoneRegex = /^(\+254|0)[17]\d{8}$/;
    return phoneRegex.test(phone);
  },

  // Generate a simple client-side token for form submissions
  generateFormToken: () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  },

  // Debounce function to prevent rapid API calls
  debounce: (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
};
