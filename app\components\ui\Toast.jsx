"use client";

import React, { createContext, useContext, useState, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react';
import { cn } from '../../lib/utils';

// Toast Context
const ToastContext = createContext();

export function useToast() {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
}

// Toast Provider
export function ToastProvider({ children }) {
  const [toasts, setToasts] = useState([]);

  const removeToast = useCallback((id) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const addToast = useCallback((toast) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newToast = {
      id,
      type: 'info',
      duration: 5000,
      ...toast,
    };

    setToasts(prev => [...prev, newToast]);

    // Auto remove toast after duration
    if (newToast.duration > 0) {
      setTimeout(() => {
        removeToast(id);
      }, newToast.duration);
    }

    return id;
  }, [removeToast]);

  const removeAllToasts = useCallback(() => {
    setToasts([]);
  }, []);

  // Convenience methods
  const toast = useCallback((message, options = {}) => {
    return addToast({ message, ...options });
  }, [addToast]);

  const success = useCallback((message, options = {}) => {
    return addToast({ message, type: 'success', ...options });
  }, [addToast]);

  const error = useCallback((message, options = {}) => {
    return addToast({ message, type: 'error', duration: 7000, ...options });
  }, [addToast]);

  const warning = useCallback((message, options = {}) => {
    return addToast({ message, type: 'warning', ...options });
  }, [addToast]);

  const info = useCallback((message, options = {}) => {
    return addToast({ message, type: 'info', ...options });
  }, [addToast]);

  const value = {
    toasts,
    addToast,
    removeToast,
    removeAllToasts,
    toast,
    success,
    error,
    warning,
    info,
  };

  return (
    <ToastContext.Provider value={value}>
      {children}
      <ToastContainer />
    </ToastContext.Provider>
  );
}

// Toast Container
function ToastContainer() {
  const { toasts } = useToast();

  if (typeof window === 'undefined') return null;

  return createPortal(
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full">
      {toasts.map((toast) => (
        <Toast key={toast.id} {...toast} />
      ))}
    </div>,
    document.body
  );
}

// Individual Toast Component
function Toast({ 
  id, 
  type = 'info', 
  title, 
  message, 
  action,
  dismissible = true,
  className 
}) {
  const { removeToast } = useToast();

  const icons = {
    success: CheckCircle,
    error: AlertCircle,
    warning: AlertTriangle,
    info: Info,
  };

  const styles = {
    success: 'bg-success-50 border-success-200 text-success-800',
    error: 'bg-error-50 border-error-200 text-error-800',
    warning: 'bg-warning-50 border-warning-200 text-warning-800',
    info: 'bg-info-50 border-info-200 text-info-800',
  };

  const iconStyles = {
    success: 'text-success-500',
    error: 'text-error-500',
    warning: 'text-warning-500',
    info: 'text-info-500',
  };

  const Icon = icons[type];

  return (
    <div
      className={cn(
        'relative flex items-start gap-3 p-4 border rounded-lg shadow-lg backdrop-blur-sm',
        'animate-slide-down transition-all duration-300',
        styles[type],
        className
      )}
    >
      {/* Icon */}
      <Icon className={cn('w-5 h-5 flex-shrink-0 mt-0.5', iconStyles[type])} />

      {/* Content */}
      <div className="flex-1 min-w-0">
        {title && (
          <p className="font-semibold text-sm mb-1">
            {title}
          </p>
        )}
        <p className="text-sm">
          {message}
        </p>
        {action && (
          <div className="mt-2">
            {action}
          </div>
        )}
      </div>

      {/* Dismiss Button */}
      {dismissible && (
        <button
          onClick={() => removeToast(id)}
          className={cn(
            'flex-shrink-0 p-1 rounded-md transition-colors',
            'hover:bg-black/10 focus:outline-none focus:ring-2 focus:ring-black/20'
          )}
        >
          <X className="w-4 h-4" />
        </button>
      )}
    </div>
  );
}

// Preset Toast Components
export function SuccessToast({ message, ...props }) {
  return <Toast type="success" message={message} {...props} />;
}

export function ErrorToast({ message, ...props }) {
  return <Toast type="error" message={message} {...props} />;
}

export function WarningToast({ message, ...props }) {
  return <Toast type="warning" message={message} {...props} />;
}

export function InfoToast({ message, ...props }) {
  return <Toast type="info" message={message} {...props} />;
}

// Loading Toast
export function LoadingToast({ message = "Loading...", ...props }) {
  return (
    <div
      className={cn(
        'relative flex items-center gap-3 p-4 border rounded-lg shadow-lg backdrop-blur-sm',
        'bg-white border-gray-200 text-gray-800',
        props.className
      )}
    >
      {/* Loading Spinner */}
      <div className="w-5 h-5 flex-shrink-0">
        <svg className="animate-spin w-full h-full" fill="none" viewBox="0 0 24 24">
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium">
          {message}
        </p>
      </div>
    </div>
  );
}

// Promise Toast - shows loading, then success/error
export function usePromiseToast() {
  const { addToast, removeToast } = useToast();

  return useCallback(async (promise, messages = {}) => {
    const {
      loading = "Loading...",
      success = "Success!",
      error = "Something went wrong"
    } = messages;

    // Show loading toast
    const loadingId = addToast({
      message: loading,
      type: 'info',
      duration: 0, // Don't auto-dismiss
      dismissible: false,
    });

    try {
      const result = await promise;
      
      // Remove loading toast
      removeToast(loadingId);
      
      // Show success toast
      addToast({
        message: typeof success === 'function' ? success(result) : success,
        type: 'success',
      });

      return result;
    } catch (err) {
      // Remove loading toast
      removeToast(loadingId);
      
      // Show error toast
      addToast({
        message: typeof error === 'function' ? error(err) : error,
        type: 'error',
      });

      throw err;
    }
  }, [addToast, removeToast]);
}

// Hook for form submission with toast feedback
export function useFormToast() {
  const { success, error } = useToast();

  const handleSubmit = useCallback(async (submitFn, options = {}) => {
    const {
      successMessage = "Form submitted successfully!",
      errorMessage = "Failed to submit form. Please try again.",
      onSuccess,
      onError
    } = options;

    try {
      const result = await submitFn();
      success(successMessage);
      onSuccess?.(result);
      return result;
    } catch (err) {
      const message = err.message || errorMessage;
      error(message);
      onError?.(err);
      throw err;
    }
  }, [success, error]);

  return { handleSubmit };
}
