"use client";

import { Heart, ShoppingCart, Star, Eye, Badge } from "lucide-react";
import Link from "next/link";
import { useState, useEffect, useContext, useCallback, useMemo } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "../hooks/useAuth";
import { WishlistContext } from "../context/wishlistContext";
import { ProductImage } from "./OptimizedImage";
import { ProductCard } from "./ui/Card";
import { IconButton } from "./ui/Button";
import { cn, formatCurrency, toTitleCase } from "../lib/utils";

export default function EnhancedProductCard({ 
  product, 
  showQuickActions = true, 
  compact = false,
  showAddToCart = true,
  className 
}) {
  const { user, isAuthenticated } = useAuth();
  const { toggleWishlistItem, isInWishlist } = useContext(WishlistContext);
  const router = useRouter();

  const productInWishlist = useMemo(() => {
    return isInWishlist(product.id);
  }, [isInWishlist, product.id]);

  const handleWishlistClick = useCallback(
    (e) => {
      e.preventDefault();
      e.stopPropagation();

      if (!isAuthenticated) {
        router.push("/auth/login");
        return;
      }

      toggleWishlistItem(product);
    },
    [isAuthenticated, router, toggleWishlistItem, product]
  );

  const handleAddToCart = useCallback(
    (e) => {
      e.preventDefault();
      e.stopPropagation();
      
      // TODO: Implement add to cart functionality
      console.log('Add to cart:', product.id);
    },
    [product.id]
  );

  const handleQuickView = useCallback(
    (e) => {
      e.preventDefault();
      e.stopPropagation();
      
      // TODO: Implement quick view modal
      console.log('Quick view:', product.id);
    },
    [product.id]
  );

  const discountPercentage = product.discount || 0;
  const originalPrice = Number(product.price);
  const discountedPrice = discountPercentage > 0 
    ? originalPrice * (1 - discountPercentage / 100)
    : originalPrice;

  const isOutOfStock = product.quantity === 0;
  const isLowStock = product.quantity > 0 && product.quantity < 10;

  return (
    <div className={cn("group h-full bg-white rounded-xl sm:rounded-2xl overflow-hidden border border-gray-100 hover:shadow-md transition-all duration-200", compact && "w-full max-w-[240px] sm:max-w-[250px]", className)}>
      <Link href={`/product/${product.id}`} className="block h-full">
        {/* Image container */}
        <div className="relative w-full aspect-square overflow-hidden bg-gray-50">
          <ProductImage
            product={product}
            fill
            priority={false}
            className={cn(
              "object-contain transition-transform duration-300",
              !isOutOfStock && "group-hover:scale-105"
            )}
            containerClassName="w-full h-full"
          />

          {/* Wishlist Button - Always visible like in reference */}
          <button
            onClick={handleWishlistClick}
            className="absolute top-2 right-2 sm:top-3 sm:right-3 w-7 h-7 sm:w-8 sm:h-8 bg-white rounded-full flex items-center justify-center shadow-sm hover:shadow-md transition-all"
          >
            <Heart
              className={cn(
                "w-4 h-4 transition-colors",
                productInWishlist ? "text-red-500 fill-current" : "text-gray-400"
              )}
            />
          </button>

          {/* Discount Badge */}
          {product.discount > 0 && (
            <div className="absolute top-2 left-2 sm:top-3 sm:left-3 bg-red-500 text-white px-1.5 py-0.5 sm:px-2 sm:py-1 rounded-md sm:rounded-lg text-xs font-medium">
              -{product.discount}%
            </div>
          )}
          {/* Stock Status */}
          {isOutOfStock && (
            <div className="absolute inset-0 bg-white/80 flex items-center justify-center">
              <span className="text-gray-500 font-medium">Out of Stock</span>
            </div>
          )}
        </div>

        {/* Product info section */}
        <div className={cn("p-2 sm:p-3 flex flex-col gap-1 sm:gap-2", compact && "p-2 gap-1")}>
          <h3 className={cn(
            "font-medium text-gray-900 group-hover:text-primarycolor transition-colors",
            compact ? "text-xs sm:text-sm line-clamp-1" : "text-sm sm:text-base line-clamp-2"
          )} title={product.name}>
            {toTitleCase(product.name)}
          </h3>

          {/* Price */}
          <div className="flex items-center gap-2">
            <span className={cn(
              "font-bold text-gray-900",
              compact ? "text-base" : "text-lg"
            )}>
              Ksh. {Math.floor(discountedPrice).toLocaleString()}
            </span>
            {product.discount > 0 && (
              <span className="text-sm text-gray-500 line-through">
                Ksh. {Math.floor(originalPrice).toLocaleString()}
              </span>
            )}
          </div>
        </div>
      </Link>
    </div>
  );
}
