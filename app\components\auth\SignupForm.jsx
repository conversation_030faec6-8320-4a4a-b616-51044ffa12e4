"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { User, Mail, Lock, AlertCircle, CheckCircle } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { useFormSubmission } from '../../hooks/useValidation';
import { signupSchema } from '../../lib/validations';
import Button from '../ui/Button';
import Input, { FormGroup } from '../ui/Input';
import AuthLayout, { SocialLoginButton, AuthDivider, PasswordStrengthIndicator } from './AuthLayout';

export default function SignupForm() {
  const { signUp, userDetails, isLoading } = useAuth();
  const router = useRouter();
  const [acceptTerms, setAcceptTerms] = useState(false);

  // Form handling with validation
  const {
    data,
    errors,
    isValid,
    updateField,
    handleSubmit,
    isSubmitting,
    submitError,
    clearSubmitError
  } = useFormSubmission(signupSchema, async (formData) => {
    if (!acceptTerms) {
      throw new Error('Please accept the terms and conditions to continue.');
    }

    try {
      await signUp(formData.email, formData.password, formData.name);
    } catch (error) {
      throw new Error(error.message || 'Registration failed. Please try again.');
    }
  });

  // Redirect after successful signup
  useEffect(() => {
    if (userDetails) {
      router.replace('/profile?welcome=true');
    }
  }, [userDetails, router]);

  const handleSocialSignup = (provider) => {
    // TODO: Implement social signup
    console.log(`Sign up with ${provider}`);
  };

  const passwordRequirements = [
    { test: (pwd) => pwd.length >= 8, text: 'At least 8 characters' },
    { test: (pwd) => /[a-z]/.test(pwd), text: 'One lowercase letter' },
    { test: (pwd) => /[A-Z]/.test(pwd), text: 'One uppercase letter' },
    { test: (pwd) => /[0-9]/.test(pwd), text: 'One number' },
  ];

  return (
    <AuthLayout
      title="Create Account"
      subtitle="Join thousands of happy customers and start shopping today"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Name Field */}
        <FormGroup
          label="Full Name"
          error={errors?.name}
          required
        >
          <Input
            type="text"
            placeholder="Enter your full name"
            value={data.name || ''}
            onChange={(e) => updateField('name', e.target.value)}
            leftIcon={<User className="w-5 h-5" />}
            error={!!errors?.name}
            disabled={isSubmitting}
          />
        </FormGroup>

        {/* Email Field */}
        <FormGroup
          label="Email Address"
          error={errors?.email}
          required
        >
          <Input
            type="email"
            placeholder="Enter your email"
            value={data.email || ''}
            onChange={(e) => updateField('email', e.target.value)}
            leftIcon={<Mail className="w-5 h-5" />}
            error={!!errors?.email}
            disabled={isSubmitting}
          />
        </FormGroup>

        {/* Password Field */}
        <FormGroup
          label="Password"
          error={errors?.password}
          required
        >
          <Input
            type="password"
            placeholder="Create a strong password"
            value={data.password || ''}
            onChange={(e) => updateField('password', e.target.value)}
            leftIcon={<Lock className="w-5 h-5" />}
            error={!!errors?.password}
            disabled={isSubmitting}
          />
          <PasswordStrengthIndicator password={data.password || ''} />
        </FormGroup>

        {/* Confirm Password Field */}
        <FormGroup
          label="Confirm Password"
          error={errors?.confirmPassword}
          required
        >
          <Input
            type="password"
            placeholder="Confirm your password"
            value={data.confirmPassword || ''}
            onChange={(e) => updateField('confirmPassword', e.target.value)}
            leftIcon={<Lock className="w-5 h-5" />}
            error={!!errors?.confirmPassword}
            success={data.password && data.confirmPassword && data.password === data.confirmPassword}
            disabled={isSubmitting}
          />
        </FormGroup>

        {/* Password Requirements */}
        {data.password && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Password Requirements:</h4>
            <div className="space-y-1">
              {passwordRequirements.map((req, index) => {
                const isValid = req.test(data.password);
                return (
                  <div key={index} className="flex items-center gap-2 text-sm">
                    {isValid ? (
                      <CheckCircle className="w-4 h-4 text-success" />
                    ) : (
                      <div className="w-4 h-4 border border-gray-300 rounded-full" />
                    )}
                    <span className={isValid ? 'text-success' : 'text-gray-600'}>
                      {req.text}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Terms and Conditions */}
        <div className="flex items-start gap-3">
          <input
            type="checkbox"
            id="acceptTerms"
            checked={acceptTerms}
            onChange={(e) => setAcceptTerms(e.target.checked)}
            className="w-4 h-4 text-primarycolor border-gray-300 rounded focus:ring-primarycolor focus:ring-2 mt-0.5"
            disabled={isSubmitting}
          />
          <label htmlFor="acceptTerms" className="text-sm text-gray-600 leading-relaxed">
            I agree to the{' '}
            <Link href="/terms" className="text-primarycolor hover:underline font-medium">
              Terms of Service
            </Link>{' '}
            and{' '}
            <Link href="/privacy" className="text-primarycolor hover:underline font-medium">
              Privacy Policy
            </Link>
          </label>
        </div>

        {/* Submit Error */}
        {submitError && (
          <div className="flex items-center gap-2 p-3 bg-error-50 border border-error-200 rounded-lg text-error-700">
            <AlertCircle className="w-5 h-5 flex-shrink-0" />
            <span className="text-sm">{submitError}</span>
            <button
              type="button"
              onClick={clearSubmitError}
              className="ml-auto text-error-500 hover:text-error-700"
            >
              ×
            </button>
          </div>
        )}

        {/* Submit Button */}
        <Button
          type="submit"
          fullWidth
          size="lg"
          loading={isSubmitting || isLoading}
          disabled={!isValid || !acceptTerms}
        >
          Create Account
        </Button>

        {/* Social Signup */}
        <AuthDivider />
        
        <div className="space-y-3">
          <SocialLoginButton
            provider="Google"
            icon={
              <svg className="w-5 h-5" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
            }
            onClick={() => handleSocialSignup('Google')}
            disabled={isSubmitting}
          />
          
          <SocialLoginButton
            provider="Facebook"
            icon={
              <svg className="w-5 h-5" fill="#1877F2" viewBox="0 0 24 24">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
            }
            onClick={() => handleSocialSignup('Facebook')}
            disabled={isSubmitting}
          />
        </div>

        {/* Login Link */}
        <div className="text-center pt-4 border-t border-gray-200">
          <p className="text-sm text-gray-600">
            Already have an account?{' '}
            <Link
              href="/auth/login"
              className="font-medium text-primarycolor hover:text-primarycolor-700 transition-colors"
            >
              Sign in
            </Link>
          </p>
        </div>
      </form>
    </AuthLayout>
  );
}
