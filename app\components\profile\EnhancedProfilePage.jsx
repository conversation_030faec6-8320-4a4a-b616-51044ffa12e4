"use client";

import React from 'react';
import Image from 'next/image';
import { useAuth } from '../../hooks/useAuth';
import { useQuery } from '@tanstack/react-query';
import ProfileLayout, { ProfileSection, ProfileStats } from './ProfileLayout';
import ProfileForm from './ProfileForm';
import Card from '../ui/Card';
import { Package, Heart, ShoppingCart, Star, TrendingUp, Clock, CheckCircle } from 'lucide-react';
import { formatCurrency } from '../../lib/utils';
import Link from 'next/link';
import Button from '../ui/Button';

export default function EnhancedProfilePage() {
  const { userDetails, isLoading } = useAuth();

  // Fetch user statistics
  const { data: userStats, isLoading: statsLoading } = useQuery({
    queryKey: ['userStats', userDetails?.id],
    queryFn: async () => {
      // TODO: Implement API call to fetch user statistics
      // This is mock data for now
      return {
        totalOrders: 12,
        totalSpent: 45600,
        wishlistItems: 8,
        reviewsGiven: 5,
        recentOrders: [
          {
            id: '1',
            status: 'delivered',
            total: 2500,
            date: '2024-01-15',
            items: 3
          },
          {
            id: '2',
            status: 'processing',
            total: 1800,
            date: '2024-01-20',
            items: 2
          }
        ]
      };
    },
    enabled: !!userDetails,
    staleTime: 5 * 60 * 1000 // Cache for 5 minutes
  });

  if (isLoading) {
    return (
      <ProfileLayout title="Profile" subtitle="Manage your account information">
        <div className="animate-pulse space-y-6">
          <div className="h-32 bg-gray-200 rounded-lg"></div>
          <div className="h-64 bg-gray-200 rounded-lg"></div>
        </div>
      </ProfileLayout>
    );
  }

  const stats = [
    {
      label: 'Total Orders',
      value: userStats?.totalOrders || 0,
      icon: Package,
      color: 'text-blue-600'
    },
    {
      label: 'Total Spent',
      value: formatCurrency(userStats?.totalSpent || 0),
      icon: TrendingUp,
      color: 'text-green-600'
    },
    {
      label: 'Wishlist Items',
      value: userStats?.wishlistItems || 0,
      icon: Heart,
      color: 'text-red-600'
    },
    {
      label: 'Reviews Given',
      value: userStats?.reviewsGiven || 0,
      icon: Star,
      color: 'text-yellow-600'
    }
  ];

  return (
    <ProfileLayout 
      title="Profile" 
      subtitle="Manage your account information and preferences"
    >
      {/* Welcome Message */}
      <Card className="p-6 bg-gradient-to-r from-primarycolor-50 to-secondarycolor-50">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Welcome back, {userDetails?.name?.split(' ')[0] || 'User'}! 👋
            </h2>
            <p className="text-gray-600">
              Here&apos;s what&apos;s happening with your account today.
            </p>
          </div>
          <div className="hidden md:block">
            <div className="w-20 h-20 bg-primarycolor rounded-full flex items-center justify-center text-white text-2xl font-bold">
              {userDetails?.avatar_url ? (
                <Image
                  src={userDetails.avatar_url}
                  alt="Profile"
                  width={80}
                  height={80}
                  className="w-full h-full rounded-full object-cover"
                />
              ) : (
                userDetails?.name?.charAt(0)?.toUpperCase() || 'U'
              )}
            </div>
          </div>
        </div>
      </Card>

      {/* Statistics */}
      <ProfileSection title="Account Overview">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <Card key={index} className="p-4 text-center hover:shadow-md transition-shadow">
                <div className="flex items-center justify-center mb-2">
                  <Icon className={`w-6 h-6 ${stat.color}`} />
                </div>
                <div className="text-2xl font-bold text-gray-900 mb-1">
                  {stat.value}
                </div>
                <div className="text-sm text-gray-600">
                  {stat.label}
                </div>
              </Card>
            );
          })}
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="p-4 hover:shadow-md transition-shadow">
            <div className="flex items-center gap-3 mb-3">
              <Package className="w-5 h-5 text-blue-600" />
              <h3 className="font-semibold text-gray-900">Recent Orders</h3>
            </div>
            <p className="text-sm text-gray-600 mb-4">
              Track your recent purchases and order history
            </p>
            <Button variant="outline" size="sm" fullWidth>
              <Link href="/profile/orders">View Orders</Link>
            </Button>
          </Card>

          <Card className="p-4 hover:shadow-md transition-shadow">
            <div className="flex items-center gap-3 mb-3">
              <Heart className="w-5 h-5 text-red-600" />
              <h3 className="font-semibold text-gray-900">Wishlist</h3>
            </div>
            <p className="text-sm text-gray-600 mb-4">
              Manage your saved items and favorites
            </p>
            <Button variant="outline" size="sm" fullWidth>
              <Link href="/profile/wishlist">View Wishlist</Link>
            </Button>
          </Card>

          <Card className="p-4 hover:shadow-md transition-shadow">
            <div className="flex items-center gap-3 mb-3">
              <ShoppingCart className="w-5 h-5 text-green-600" />
              <h3 className="font-semibold text-gray-900">Continue Shopping</h3>
            </div>
            <p className="text-sm text-gray-600 mb-4">
              Discover new products and great deals
            </p>
            <Button variant="outline" size="sm" fullWidth>
              <Link href="/">Browse Products</Link>
            </Button>
          </Card>
        </div>
      </ProfileSection>

      {/* Recent Activity */}
      {userStats?.recentOrders && userStats.recentOrders.length > 0 && (
        <ProfileSection title="Recent Activity">
          <div className="space-y-3">
            {userStats.recentOrders.map((order) => (
              <Card key={order.id} className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                      order.status === 'delivered' 
                        ? 'bg-green-100 text-green-600' 
                        : 'bg-blue-100 text-blue-600'
                    }`}>
                      {order.status === 'delivered' ? (
                        <CheckCircle className="w-5 h-5" />
                      ) : (
                        <Clock className="w-5 h-5" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">
                        Order #{order.id}
                      </p>
                      <p className="text-sm text-gray-600">
                        {order.items} items • {new Date(order.date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900">
                      {formatCurrency(order.total)}
                    </p>
                    <p className={`text-sm capitalize ${
                      order.status === 'delivered' 
                        ? 'text-green-600' 
                        : 'text-blue-600'
                    }`}>
                      {order.status}
                    </p>
                  </div>
                </div>
              </Card>
            ))}
          </div>
          
          <div className="mt-4 text-center">
            <Button variant="outline">
              <Link href="/profile/orders">View All Orders</Link>
            </Button>
          </div>
        </ProfileSection>
      )}

      {/* Profile Form */}
      <ProfileForm />
    </ProfileLayout>
  );
}
