// app/layout.js (Root Layout - Server Component)
import './globals.css';
import { SupabaseProvider } from './context/supabaseContext';
import { CartProvider } from './context/cartContext'
import { WishlistProvider } from './context/wishlistContext'
import ClientLayoutWrapper from './clientlayoutwrapper';
import QueryProvider from './providers/queryProvider';
import FacebookPixel from './components/FacebookPixel';
import ErrorBoundary from './components/ErrorBoundary';
import AsyncErrorBoundary from './components/AsyncErrorBoundary';

export const metadata = {
  title: "SHERRY'S MERCHANTS",
  description: "Ecommerce of Sherry's Merchants",
};

// Add route segment config
export const dynamic = 'force-static'
export const revalidate = 3600 // Revalidate every hour

// Add generateStaticParams for ads routes
export async function generateStaticParams() {
  return [{
    ads: ['08b56d0d-0e57-499c-b3fc-8b95c41048a5']
  }]
}

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body className="font-sf-pro-display">
        <FacebookPixel />
        <ErrorBoundary level="app">
          <AsyncErrorBoundary>
            <QueryProvider>
              <SupabaseProvider>
                <WishlistProvider>
                  <CartProvider>
                    <ClientLayoutWrapper>
                      {children}
                    </ClientLayoutWrapper>
                  </CartProvider>
                </WishlistProvider>
              </SupabaseProvider>
            </QueryProvider>
          </AsyncErrorBoundary>
        </ErrorBoundary>
      </body>
    </html>
  );
}