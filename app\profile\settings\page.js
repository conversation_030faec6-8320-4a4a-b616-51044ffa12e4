import { Suspense } from "react";
import ProfileLayout from "../../components/profile/ProfileLayout";
import SettingsPage from "../../components/profile/SettingsPage";

export default function SettingsPageRoute() {
  return (
    <Suspense fallback={<div>Loading settings...</div>}>
      <ProfileLayout 
        title="Settings" 
        subtitle="Manage your account preferences and settings"
      >
        <SettingsPage />
      </ProfileLayout>
    </Suspense>
  );
}
