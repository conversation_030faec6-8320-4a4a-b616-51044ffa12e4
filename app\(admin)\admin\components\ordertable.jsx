"use client";
import { useSupabase } from '../hooks/useSupabase';

export default function OrderTable() {
  const { useOrders, useUpdateOrderStatus } = useSupabase();
  const { data: orders, isLoading } = useOrders();
  const updateStatus = useUpdateOrderStatus();

  const handleApprove = async (orderId) => {
    await updateStatus.mutateAsync({ orderId, status: 'CONFIRMED' });
    
  };

  if (isLoading) return <div>Loading orders...</div>;

  return (
    <table className="w-full">
      <thead>
        <tr>
          <th>Order ID</th>
          <th>Customer</th>
          <th>Status</th>
          <th>Mpesa Code</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        {orders?.map((order) => (
          <tr key={order.id}>
            <td>{order.id}</td>
            <td>{order.customer_name}</td>
            <td>{order.status}</td>
            <td>{order.mpesa_code}</td>
            <td>
              {order.status === 'pending' && (
                <button
                  onClick={() => handleApprove(order.id)}
                  disabled={updateStatus.isLoading}
                  className="bg-green-500 text-white px-4 py-2 rounded disabled:opacity-50"
                >
                  {updateStatus.isLoading ? 'Processing...' : 'Approve'}
                </button>
              )}
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}
