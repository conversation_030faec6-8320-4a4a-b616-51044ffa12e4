"use client";
import { useState, useEffect, useCallback } from 'react';
import { useSupabase } from '../hooks/useSupabase';
import Image from 'next/image';
import { toast } from 'sonner';

export default function MarketingManagement() {
  const { fetchMarketingBanners, createMarketingBanner, updateMarketingBanner, deleteMarketingBanner, uploadProductImage } = useSupabase();
  const [banners, setBanners] = useState([]);
  const [selectedBanner, setSelectedBanner] = useState(null);
  const [imageFile, setImageFile] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    image_url: '',
    link: '',
    display_order: 0,
    is_active: true
  });

  const loadBanners = useCallback(async () => {
    const data = await fetchMarketingBanners();
    setBanners(data);
  }, [fetchMarketingBanners]);

  useEffect(() => {
    loadBanners();
  }, [loadBanners]);

  useEffect(() => {
    if (selectedBanner) {
      setFormData({
        title: selectedBanner.title,
        image_url: selectedBanner.image_url,
        link: selectedBanner.link || '',
        display_order: selectedBanner.display_order,
        is_active: selectedBanner.is_active
      });
    }
  }, [selectedBanner]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    let imageUrl = formData.image_url;

    if (imageFile) {
      try {
        imageUrl = await uploadProductImage(imageFile);
      } catch (error) {
        console.error('Image upload error:', error);
        toast.error(`Image upload error: ${error.message}`);
        return;
      }
    }

    try {
      const bannerData = {
        ...formData,
        image_url: imageUrl
      };

      if (selectedBanner) {
        await updateMarketingBanner(selectedBanner.id, bannerData);
        toast.success('Banner updated successfully');
      } else {
        await createMarketingBanner(bannerData);
        toast.success('Banner created successfully');
      }
      
      loadBanners();
      resetForm();
    } catch (error) {
      toast.error(`Failed to save banner: ${error.message}`);
    }
  };

  const handleDelete = async (id) => {
    if (confirm('Are you sure you want to delete this banner?')) {
      try {
        await deleteMarketingBanner(id);
        toast.success('Banner deleted successfully');
        loadBanners();
      } catch (error) {
        toast.error(`Failed to delete banner: ${error.message}`);
      }
    }
  };

  const resetForm = () => {
    setSelectedBanner(null);
    setImageFile(null);
    setFormData({
      title: '',
      image_url: '',
      link: '',
      display_order: 0,
      is_active: true
    });
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Marketing Banner Management</h1>

      <form onSubmit={handleSubmit} className="mb-8 bg-white p-6 rounded-lg shadow">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <input
            type="text"
            placeholder="Banner Title"
            value={formData.title}
            onChange={(e) => setFormData({...formData, title: e.target.value})}
            className="w-full p-2 border rounded"
            required
          />
          
          <div className="space-y-2">
            <label className="block text-sm font-medium text-primarycolor">Banner Image</label>
            <input
              type="file"
              accept="image/*"
              onChange={(e) => setImageFile(e.target.files[0])}
              className="w-full p-2 border rounded"
            />
            {formData.image_url && (
              <Image
                src={formData.image_url}
                alt="Preview"
                width={200}
                height={100}
                className="object-contain"
              />
            )}
          </div>

          <input
            type="url"
            placeholder="Link URL (optional)"
            value={formData.link}
            onChange={(e) => setFormData({...formData, link: e.target.value})}
            className="w-full p-2 border rounded"
          />
          
          <input
            type="number"
            placeholder="Display Order"
            value={formData.display_order}
            onChange={(e) => setFormData({...formData, display_order: parseInt(e.target.value)})}
            className="w-full p-2 border rounded"
            required
          />
          
          <div className="flex items-center">
            <input
              type="checkbox"
              checked={formData.is_active}
              onChange={(e) => setFormData({...formData, is_active: e.target.checked})}
              className="mr-2"
            />
            <label>Active</label>
          </div>
        </div>

        <div className="mt-4">
          <button
            type="submit"
            className="w-full bg-primarycolor text-white p-3 rounded"
          >
            {selectedBanner ? 'Update Banner' : 'Add Banner'}
          </button>
          {selectedBanner && (
            <button
              type="button"
              onClick={resetForm}
              className="w-full mt-2 bg-gray-500 text-white p-3 rounded"
            >
              Cancel
            </button>
          )}
        </div>
      </form>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {banners.map((banner) => (
          <div key={banner.id} className="bg-white p-4 rounded-lg shadow">
            <Image
              src={banner.image_url}
              alt={banner.title}
              width={400}
              height={200}
              className="w-full h-48 object-cover rounded mb-4"
            />
            <h3 className="font-bold">{banner.title}</h3>
            <p className="text-sm text-gray-500 mb-2">Order: {banner.display_order}</p>
            <p className="text-sm text-gray-500 mb-4">
              Status: {banner.is_active ? 'Active' : 'Inactive'}
            </p>
            <div className="flex justify-end gap-2">
              <button
                onClick={() => setSelectedBanner(banner)}
                className="bg-primarycolor text-white px-3 py-1 rounded"
              >
                Edit
              </button>
              <button
                onClick={() => handleDelete(banner.id)}
                className="bg-warningcolor text-white px-3 py-1 rounded"
              >
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
