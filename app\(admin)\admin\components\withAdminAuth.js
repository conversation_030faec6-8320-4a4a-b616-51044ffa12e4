"use client";
import { useAuth } from '../../../hooks/useAuth';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export function withAdminAuth(Component) {
  return function ProtectedRoute(props) {
    const { userDetails, isAuthenticated } = useAuth();
    const router = useRouter();
    const [isLoaded, setIsLoaded] = useState(false);

    useEffect(() => {
      // Wait for both auth states to be determined
      if (typeof isAuthenticated === 'boolean' && userDetails !== null) {
        console.log('Full auth state loaded:', {
          isAuthenticated,
          role: userDetails?.role
        });

        if (userDetails.role === 'admin') {
          setIsLoaded(true);
        } else {
          router.replace('/');
        }
      }
    }, [isAuthenticated, userDetails, router]);

    if (!isLoaded) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-white">
          <div className="text-primarycolor text-xl">Loading...</div>
        </div>
      );
    }

    return <Component {...props} />;
  };
}
