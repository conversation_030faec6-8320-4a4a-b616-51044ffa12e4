"use client";

import React, { useState } from 'react';
import { 
  MapPin, 
  Plus, 
  Edit, 
  Trash2, 
  Star, 
  Home, 
  Building, 
  User,
  Phone,
  Mail
} from 'lucide-react';
import { 
  useUserAddresses, 
  useCreateAddress, 
  useUpdateAddress, 
  useDeleteAddress 
} from '../../hooks/useProfile';
import { userAddressSchema } from '../../lib/validations';
import Card from '../ui/Card';
import Button from '../ui/Button';
import Input, { FormGroup } from '../ui/Input';
import { cn } from '../../lib/utils';

const KENYAN_COUNTIES = [
  'Nairobi', 'Mombasa', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Eldoret', '<PERSON>hi<PERSON>', 'Malindi',
  '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>kamega', '<PERSON>hakos', 'Meru', '<PERSON>yeri', '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Homa Bay', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>lifi', '<PERSON>u', 'Taveta', 'Loitokitok', 'Kajiado',
  'Namanga', 'Magadi', 'Athi River', 'Kiambu', 'Limuru', 'Kikuyu', 'Ruiru',
  'Githunguri', 'Gatundu', 'Murang\'a', 'Kenol', 'Sagana'
];

export default function AddressesPage() {
  const [showForm, setShowForm] = useState(false);
  const [editingAddress, setEditingAddress] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    full_name: '',
    phone: '',
    street_address: '',
    city: '',
    county: '',
    postal_code: '',
    is_default: false
  });
  const [errors, setErrors] = useState({});

  const { data: addresses = [], isLoading } = useUserAddresses();
  const createAddress = useCreateAddress();
  const updateAddress = useUpdateAddress();
  const deleteAddress = useDeleteAddress();

  const resetForm = () => {
    setFormData({
      title: '',
      full_name: '',
      phone: '',
      street_address: '',
      city: '',
      county: '',
      postal_code: '',
      is_default: false
    });
    setErrors({});
    setEditingAddress(null);
    setShowForm(false);
  };

  const handleEdit = (address) => {
    setFormData({
      title: address.title,
      full_name: address.full_name,
      phone: address.phone,
      street_address: address.street_address,
      city: address.city,
      county: address.county,
      postal_code: address.postal_code || '',
      is_default: address.is_default
    });
    setEditingAddress(address);
    setShowForm(true);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setErrors({});

    try {
      // Validate form data
      const validatedData = userAddressSchema.parse(formData);
      
      if (editingAddress) {
        await updateAddress.mutateAsync({ id: editingAddress.id, ...validatedData });
      } else {
        await createAddress.mutateAsync(validatedData);
      }
      
      resetForm();
    } catch (error) {
      if (error.errors) {
        const newErrors = {};
        error.errors.forEach(err => {
          newErrors[err.path[0]] = err.message;
        });
        setErrors(newErrors);
      } else {
        console.error('Address operation failed:', error);
      }
    }
  };

  const handleDelete = async (addressId) => {
    if (window.confirm('Are you sure you want to delete this address?')) {
      try {
        await deleteAddress.mutateAsync(addressId);
      } catch (error) {
        console.error('Delete failed:', error);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map(i => (
          <div key={i} className="animate-pulse">
            <div className="h-32 bg-gray-200 rounded-lg"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-bold text-gray-900">Saved Addresses</h2>
          <p className="text-gray-600">Manage your shipping and billing addresses</p>
        </div>
        <Button onClick={() => setShowForm(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Add Address
        </Button>
      </div>

      {/* Addresses List */}
      {addresses.length === 0 ? (
        <Card className="p-8 text-center">
          <MapPin className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Addresses Saved</h3>
          <p className="text-gray-600 mb-4">
            Add your first address to make checkout faster and easier.
          </p>
          <Button onClick={() => setShowForm(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Add Your First Address
          </Button>
        </Card>
      ) : (
        <div className="grid gap-4">
          {addresses.map((address) => (
            <Card key={address.id} className="p-6">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="font-semibold text-gray-900">{address.title}</h3>
                    {address.is_default && (
                      <span className="inline-flex items-center gap-1 px-2 py-1 bg-primarycolor text-white text-xs rounded-full">
                        <Star className="w-3 h-3" />
                        Default
                      </span>
                    )}
                  </div>
                  
                  <div className="space-y-1 text-sm text-gray-600">
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4" />
                      <span className="font-medium">{address.full_name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="w-4 h-4" />
                      {address.phone}
                    </div>
                    <div className="flex items-start gap-2">
                      <MapPin className="w-4 h-4 mt-0.5" />
                      <div>
                        <div>{address.street_address}</div>
                        <div>{address.city}, {address.county}</div>
                        {address.postal_code && <div>{address.postal_code}</div>}
                        <div>{address.country}</div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEdit(address)}
                  >
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(address.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Add/Edit Address Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-900">
                  {editingAddress ? 'Edit Address' : 'Add New Address'}
                </h2>
                <Button variant="ghost" size="sm" onClick={resetForm}>
                  ✕
                </Button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <FormGroup label="Address Title" error={errors.title}>
                  <Input
                    type="text"
                    placeholder="e.g., Home, Work, Office"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    error={!!errors.title}
                  />
                </FormGroup>

                <FormGroup label="Full Name" error={errors.full_name}>
                  <Input
                    type="text"
                    placeholder="Enter full name"
                    value={formData.full_name}
                    onChange={(e) => setFormData(prev => ({ ...prev, full_name: e.target.value }))}
                    error={!!errors.full_name}
                  />
                </FormGroup>

                <FormGroup label="Phone Number" error={errors.phone}>
                  <Input
                    type="tel"
                    placeholder="e.g., +254712345678 or 0712345678"
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    error={!!errors.phone}
                  />
                </FormGroup>

                <FormGroup label="Street Address" error={errors.street_address}>
                  <Input
                    type="text"
                    placeholder="Enter street address"
                    value={formData.street_address}
                    onChange={(e) => setFormData(prev => ({ ...prev, street_address: e.target.value }))}
                    error={!!errors.street_address}
                  />
                </FormGroup>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormGroup label="City" error={errors.city}>
                    <Input
                      type="text"
                      placeholder="Enter city"
                      value={formData.city}
                      onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                      error={!!errors.city}
                    />
                  </FormGroup>

                  <FormGroup label="County" error={errors.county}>
                    <select
                      value={formData.county}
                      onChange={(e) => setFormData(prev => ({ ...prev, county: e.target.value }))}
                      className={cn(
                        "w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primarycolor focus:border-transparent",
                        errors.county ? "border-red-300" : "border-gray-300"
                      )}
                    >
                      <option value="">Select County</option>
                      {KENYAN_COUNTIES.map(county => (
                        <option key={county} value={county}>{county}</option>
                      ))}
                    </select>
                  </FormGroup>
                </div>

                <FormGroup label="Postal Code (Optional)" error={errors.postal_code}>
                  <Input
                    type="text"
                    placeholder="Enter postal code"
                    value={formData.postal_code}
                    onChange={(e) => setFormData(prev => ({ ...prev, postal_code: e.target.value }))}
                    error={!!errors.postal_code}
                  />
                </FormGroup>

                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="is_default"
                    checked={formData.is_default}
                    onChange={(e) => setFormData(prev => ({ ...prev, is_default: e.target.checked }))}
                    className="w-4 h-4 text-primarycolor border-gray-300 rounded focus:ring-primarycolor"
                  />
                  <label htmlFor="is_default" className="text-sm text-gray-700">
                    Set as default address
                  </label>
                </div>

                <div className="flex gap-3 pt-4">
                  <Button
                    type="submit"
                    disabled={createAddress.isPending || updateAddress.isPending}
                    className="flex-1"
                  >
                    {createAddress.isPending || updateAddress.isPending ? 'Saving...' : 
                     editingAddress ? 'Update Address' : 'Add Address'}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={resetForm}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
}
