"use client";
import dynamic from "next/dynamic";
import { useProducts } from "../hooks/useQueries";
import Link from "next/link";
import Image from "next/image";
import { useState, useEffect, useRef, useCallback } from "react";
import { useSupabaseContext } from "../context/supabaseContext";
import { useCart } from "../context/cartContext";
import { usePathname, useRouter } from "next/navigation";
import { useQueryClient } from "@tanstack/react-query";

// Dynamically import icons with preload
const ShoppingCart = dynamic(
  () => import("lucide-react").then((mod) => mod.ShoppingCart),
  {
    ssr: false,
    loading: () => <div className="w-6 h-6" />,
  }
);

const User = dynamic(() => import("lucide-react").then((mod) => mod.User), {
  ssr: false,
  loading: () => <div className="w-6 h-6" />,
});

const AlignLeft = dynamic(
  () => import("lucide-react").then((mod) => mod.AlignLeft),
  {
    ssr: false,
    loading: () => <div className="w-6 h-6" />,
  }
);

const X = dynamic(() => import("lucide-react").then((mod) => mod.X), {
  ssr: false,
  loading: () => <div className="w-6 h-6" />,
});
const Search = dynamic(() => import("lucide-react").then((mod) => mod.Search), {
  ssr: false,
  loading: () => <div className="w-6 h-6" />,
});

export default function Header() {
  const queryClient = useQueryClient();
  const { data: products = [] } = useProducts();
  const [menuOpen, setMenuOpen] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const { fetchCategories, user } = useSupabaseContext();
  const [categories, setCategories] = useState([]);
  const menuRef = useRef(null);
  const { cartCount } = useCart();
  const [searchTerm, setSearchTerm] = useState("");
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Prefetch common routes
  useEffect(() => {
    const routes = ["/cart", "/profile", "/auth/login"];
    routes.forEach((route) => {
      router.prefetch(route);
    });
  }, [router]);

  const toggleMenu = useCallback(() => {
    setMenuOpen((prev) => !prev);
  }, []);

  useEffect(() => {
    fetchCategories().then(setCategories).catch(console.error);
  }, [fetchCategories]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setMenuOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleInputChange = (e) => {
    const value = e.target.value;
    setSearchTerm(value);

    if (value.trim()) {
      const filteredSuggestions = products.filter((product) =>
        product.name.toLowerCase().includes(value.toLowerCase())
      );
      setSuggestions(filteredSuggestions.slice(0, 5));
      setShowSuggestions(true);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  };

  return (
    <>
      <header className="relative z-50 bg-white shadow-md">
        <div className="max-w-7xl mx-auto px-4 py-3">
          {/* Mobile Header */}
          <div className="flex md:hidden justify-between items-center">
            <button onClick={toggleMenu}>
              {menuOpen ? (
                <X className="h-6 w-6 text-warningcolor" />
              ) : (
                <AlignLeft className="h-6 w-6 text-primarycolor" />
              )}
            </button>

            <Link href="/" prefetch={true}>
              <Image
                src="/logo.svg"
                alt="Better Days Closet"
                width={60}
                height={32}
                priority
              />
            </Link>

            <div className="flex space-x-4">
              <Link href="/cart" prefetch={true} className="relative">
                <ShoppingCart
                  className={`h-6 w-6 ${
                    pathname === "/cart"
                      ? "text-secondarycolor"
                      : "text-primarycolor"
                  }`}
                />
                {cartCount > 0 && (
                  <span className="absolute -top-2 -right-2 bg-warningcolor text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {cartCount}
                  </span>
                )}
              </Link>
              <Link
                href={user ? "/profile" : "/auth/login"}
                prefetch={true}
                onMouseEnter={() => {
                  queryClient.prefetchQuery({
                    queryKey: ["userDetails", user?.id],
                    queryFn: () => fetchUserDetails(),
                  });
                }}
              >
                <User
                  className={`h-6 w-6 ${
                    pathname === "/profile"
                      ? "text-secondarycolor"
                      : "text-primarycolor"
                  }`}
                />
              </Link>
            </div>
          </div>

          {/* Desktop Header with proper alignment */}
          <div className="hidden md:flex items-center justify-between w-full gap-8">
            {/* Logo - Left */}
            <div className="flex-none">
              <Link href="/" prefetch={true}>
                <Image
                  src="/logo.svg"
                  alt="Better Days Closet"
                  width={120}
                  height={40}
                  priority
                />
              </Link>
            </div>

            {/* Search - Center */}
            <div className="flex-1 max-w-2xl mx-auto">
              <div className="relative flex items-center">
                <input
                  type="text"
                  placeholder="Search Products..."
                  value={searchTerm}
                  onChange={handleInputChange}
                  className="w-full p-3 pl-12 rounded border-[1.5px] border-primarycolor outline-none text-primarycolor"
                />
                <Search className="absolute left-4 h-5 w-5 text-primarycolor" />
              </div>
              {/* Search suggestions dropdown */}
              {showSuggestions && suggestions.length > 0 && (
                <div className="absolute w-full bg-white border border-primarycolor rounded-lg mt-1 shadow-lg z-20">
                  {suggestions.map((product) => (
                    <Link
                      href={`/product/${product.id}`}
                      key={product.id}
                      className="flex items-center justify-between p-4 hover:bg-primarycolorvariant"
                    >
                      <div>
                        <p className="font-bold text-primarycolor">
                          {product.name}
                        </p>
                        <p className="text-secondarycolor">
                          Ksh. {product.price}
                        </p>
                      </div>
                      <Image
                        src={product.image_url}
                        alt={product.name}
                        width={64}
                        height={64}
                        className="object-cover rounded"
                      />
                    </Link>
                  ))}
                </div>
              )}
            </div>

            {/* Icons - Right */}
            <div className="flex-none flex items-center gap-6">
              <Link href="/cart" prefetch={true} className="relative">
                <ShoppingCart
                  className={`h-6 w-6 ${
                    pathname === "/cart"
                      ? "text-secondarycolor"
                      : "text-primarycolor"
                  }`}
                />
                {cartCount > 0 && (
                  <span className="absolute -top-2 -right-2 bg-warningcolor text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {cartCount}
                  </span>
                )}
              </Link>
              <Link href={user ? "/profile" : "/auth/login"} prefetch={true}>
                <User
                  className={`h-6 w-6 ${
                    pathname === "/profile"
                      ? "text-secondarycolor"
                      : "text-primarycolor"
                  }`}
                />
              </Link>
            </div>
          </div>
        </div>
      </header>
      {/* Overlay */}
      <div
        className={`fixed inset-0 bg-black/30 backdrop-blur-sm transition-opacity duration-300 ease-in-out z-30
          ${menuOpen ? "opacity-100" : "opacity-0 pointer-events-none"}`}
        onClick={() => setMenuOpen(false)}
      />

      {/* Menu */}
      <nav
        ref={menuRef}
        className={`fixed z-40 left-0 text-primarycolor bg-secondarycolor w-full shadow-md
          transition-all duration-300 ease-in-out
          ${menuOpen ? "top-16" : "-top-[100%]"}
          max-h-[calc(100vh-4rem)] overflow-y-auto`}
      >
        <ul className="p-4">
          {categories.length > 0 ? (
            categories.map((category, index) => (
              <li
                key={category.id}
                className={`py-2 border-b border-white/20 ${
                  index === categories.length - 1 ? "border-b-0" : ""
                }
                  transition-colors duration-200 hover:bg-white/10`}
              >
                <Link
                  href={`/categories/${category.slug}`}
                  className="block w-full"
                  onClick={() => setMenuOpen(false)}
                >
                  {category.name}
                </Link>
              </li>
            ))
          ) : (
            <li>No categories available</li>
          )}
        </ul>
      </nav>
    </>
  );
}
