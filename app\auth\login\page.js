"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '../../hooks/useAuth';
import { setCookie, getCookie } from 'cookies-next';
import { PasswordInput } from '../../components/ui/PasswordStrength';

export default function LoginPage() {
  const { signIn, userDetails, isLoading } = useAuth();
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [error, setError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load remembered email on component mount
  useEffect(() => {
    const rememberedEmail = getCookie('rememberedEmail');
    if (rememberedEmail) {
      setEmail(rememberedEmail);
      setRememberMe(true);
    }
  }, []);

  // Redirect if already authenticated
  useEffect(() => {
    if (userDetails && !isLoading) {
      console.log('Redirecting user:', userDetails);
      if (userDetails.role === 'admin') {
        router.push('/admin');
      } else {
        router.push('/');
      }
    }
  }, [userDetails, router, isLoading]);

  // Add timeout to prevent infinite loading
  useEffect(() => {
    if (isSubmitting) {
      const timeout = setTimeout(() => {
        console.log('Login timeout - stopping loading state');
        setIsSubmitting(false);
        setError('Login is taking too long. Please try again.');
      }, 10000); // 10 second timeout

      return () => clearTimeout(timeout);
    }
  }, [isSubmitting]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setIsSubmitting(true);

    // Client-side validation
    if (!email || !password) {
      setError('Please fill in all fields');
      setIsSubmitting(false);
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email.trim())) {
      setError('Please enter a valid email address');
      setIsSubmitting(false);
      return;
    }

    try {
      console.log('Attempting login with:', { email: email.trim().toLowerCase(), hasPassword: !!password });
      const result = await signIn(email.trim().toLowerCase(), password);
      console.log('Login successful:', result);

      // Handle remember me functionality
      if (rememberMe) {
        setCookie('rememberedEmail', email, { maxAge: 30 * 24 * 60 * 60 }); // 30 days
      } else {
        setCookie('rememberedEmail', '', { maxAge: 0 }); // Clear cookie
      }

      // Remove manual redirect logic - let the auth state change handle redirection
      // The useEffect above will handle redirection when userDetails is set

    } catch (error) {
      console.error('Login error:', error);
      let errorMessage = 'Login failed. Please check your credentials and try again.';

      if (error.message.includes('Invalid login credentials')) {
        errorMessage = 'Invalid email or password. Please check your credentials.';
      } else if (error.message.includes('Email not confirmed')) {
        errorMessage = 'Please check your email and click the confirmation link.';
      } else if (error.message.includes('Too many requests')) {
        errorMessage = 'Too many login attempts. Please wait a moment and try again.';
      }

      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primarycolor-50 via-white to-secondarycolor-50 flex">
      {/* Left Side - Branding */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-primarycolor to-primarycolor-800 relative overflow-hidden">
        <div className="absolute inset-0 bg-grid-pattern opacity-10" />

        <div className="relative z-10 flex flex-col justify-center px-8 xl:px-12 text-white">
          <div className="max-w-md">
            {/* Logo */}
            <div className="mb-6 lg:mb-8">
              <Link href="/" className="flex items-center gap-3">
                <div className="w-10 h-10 lg:w-12 lg:h-12 bg-white rounded-xl flex items-center justify-center">
                  <span className="text-lg lg:text-2xl font-bold text-primarycolor">PM</span>
                </div>
                <span className="text-xl lg:text-2xl font-bold">Pinchez Merchants</span>
              </Link>
            </div>

            <h1 className="text-3xl lg:text-4xl font-bold mb-4 lg:mb-6">
              Welcome to Your Shopping Paradise
            </h1>

            <p className="text-primarycolor-100 text-base lg:text-lg mb-6 lg:mb-8 leading-relaxed">
              Join thousands of satisfied customers who trust us for quality products,
              fast delivery, and exceptional service.
            </p>

            {/* Trust Indicators */}
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primarycolor-600 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <p className="font-semibold">Secure & Safe</p>
                  <p className="text-primarycolor-200 text-sm">Your data is protected</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primarycolor-600 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                  </svg>
                </div>
                <div>
                  <p className="font-semibold">10,000+ Customers</p>
                  <p className="text-primarycolor-200 text-sm">Trusted by many</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primarycolor-600 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <p className="font-semibold">Fast Delivery</p>
                  <p className="text-primarycolor-200 text-sm">Quick & reliable</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Auth Form */}
      <div className="flex-1 flex flex-col justify-center px-4 sm:px-6 lg:px-8 xl:px-16 py-8">
        <div className="w-full max-w-md mx-auto">
          {/* Mobile Logo */}
          <div className="lg:hidden mb-6 text-center">
            <Link href="/" className="inline-flex items-center gap-3">
              <div className="w-10 h-10 bg-primarycolor rounded-xl flex items-center justify-center">
                <span className="text-lg font-bold text-white">PM</span>
              </div>
              <span className="text-xl font-bold text-gray-900">Pinchez Merchants</span>
            </Link>
          </div>

          {/* Back Button */}
          <div className="mb-6 lg:mb-8">
            <button
              onClick={() => window.history.back()}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Back
            </button>
          </div>

          {/* Form Card */}
          <div className="bg-white p-6 sm:p-8 rounded-xl lg:rounded-2xl shadow-lg">
            <div className="text-center mb-6 sm:mb-8">
              <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
                Welcome Back
              </h2>
              <p className="text-gray-600 text-sm sm:text-base">
                Sign in to your account to continue shopping
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-1">
                <label className="block text-sm font-medium text-gray-700">
                  Email Address <span className="text-red-500 ml-1">*</span>
                </label>
                <input
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isSubmitting}
                  required
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primarycolor focus:border-primarycolor disabled:opacity-50 disabled:cursor-not-allowed"
                />
              </div>

              <div className="space-y-1">
                <label className="block text-sm font-medium text-gray-700">
                  Password <span className="text-red-500 ml-1">*</span>
                </label>
                <PasswordInput
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your password"
                  disabled={isSubmitting}
                  showStrength={false}
                  required
                />
              </div>

              <div className="flex items-center justify-between">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                    className="w-4 h-4 text-primarycolor border-gray-300 rounded focus:ring-primarycolor"
                  />
                  <span className="ml-2 text-sm text-gray-600">Remember me</span>
                </label>

                <Link
                  href="/auth/forgot-password"
                  className="text-sm text-primarycolor hover:text-primarycolor-700 font-medium"
                >
                  Forgot password?
                </Link>
              </div>

              {error && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-sm text-red-600">{error}</p>
                </div>
              )}

              <button
                type="submit"
                disabled={!email || !password || isSubmitting || isLoading}
                className="w-full inline-flex items-center justify-center px-6 py-3 text-lg font-medium rounded-lg text-white bg-primarycolor hover:bg-primarycolor-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primarycolor disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                onClick={() => console.log('Button clicked:', { email, password: !!password, isSubmitting, isLoading, disabled: !email || !password || isSubmitting || isLoading })}
              >
                {(isSubmitting || isLoading) && (
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                  </svg>
                )}
                {(isSubmitting || isLoading) ? 'Signing In...' : 'Sign In'}
              </button>
            </form>

            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                Don&apos;t have an account?{' '}
                <Link
                  href="/auth/signup"
                  className="font-medium text-primarycolor hover:text-primarycolor-700"
                >
                  Sign up for free
                </Link>
              </p>
            </div>
          </div>

          {/* Footer */}
          <div className="mt-8 text-center text-sm text-gray-500">
            <p>
              By continuing, you agree to our{' '}
              <Link href="/terms" className="text-primarycolor hover:underline">
                Terms of Service
              </Link>{' '}
              and{' '}
              <Link href="/privacy" className="text-primarycolor hover:underline">
                Privacy Policy
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
