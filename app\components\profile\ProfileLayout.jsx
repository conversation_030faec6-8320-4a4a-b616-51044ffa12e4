"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter, usePathname } from 'next/navigation';
import { 
  User, 
  Heart, 
  Package, 
  Settings, 
  LogOut, 
  ChevronLeft,
  Menu,
  X,
  Bell,
  CreditCard,
  MapPin,
  Shield
} from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import Card from '../ui/Card';
import Button, { IconButton } from '../ui/Button';
import { cn, getInitials } from '../../lib/utils';

const navigationItems = [
  {
    id: 'profile',
    label: 'Profile',
    icon: User,
    href: '/profile',
    description: 'Manage your personal information'
  },
  {
    id: 'orders',
    label: 'Orders',
    icon: Package,
    href: '/profile/orders',
    description: 'View your order history'
  },
  {
    id: 'wishlist',
    label: 'Wishlist',
    icon: Heart,
    href: '/profile/wishlist',
    description: 'Your saved items'
  },
  {
    id: 'addresses',
    label: 'Addresses',
    icon: MapPin,
    href: '/profile/addresses',
    description: 'Manage shipping addresses'
  },
  {
    id: 'payment',
    label: 'Payment Methods',
    icon: CreditCard,
    href: '/profile/payment',
    description: 'Manage payment options'
  },
  {
    id: 'notifications',
    label: 'Notifications',
    icon: Bell,
    href: '/profile/notifications',
    description: 'Notification preferences'
  },
  {
    id: 'security',
    label: 'Security',
    icon: Shield,
    href: '/profile/security',
    description: 'Password and security settings'
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: Settings,
    href: '/profile/settings',
    description: 'Account preferences'
  },
];

export default function ProfileLayout({ children, title, subtitle }) {
  const { userDetails, signOut } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push('/');
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const currentItem = navigationItems.find(item => item.href === pathname) || navigationItems[0];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Back to Home */}
            <div className="flex items-center gap-4">
              <Link
                href="/"
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ChevronLeft className="w-5 h-5" />
                <span className="hidden sm:inline">Back to Store</span>
              </Link>
              
              {/* Mobile menu button */}
              <button
                className="lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                onClick={() => setSidebarOpen(!sidebarOpen)}
                aria-label="Toggle menu"
              >
                {sidebarOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
              </button>
            </div>

            {/* User Info */}
            <div className="flex items-center gap-3">
              <div className="hidden sm:block text-right">
                <p className="text-sm font-medium text-gray-900">
                  {userDetails?.name || 'User'}
                </p>
                <p className="text-xs text-gray-500">
                  {userDetails?.email}
                </p>
              </div>
              
              {/* Avatar */}
              <div className="w-10 h-10 bg-primarycolor rounded-full flex items-center justify-center text-white font-medium">
                {userDetails?.avatar_url ? (
                  <Image
                    src={userDetails.avatar_url}
                    alt="Profile"
                    width={40}
                    height={40}
                    className="w-full h-full rounded-full object-cover"
                  />
                ) : (
                  getInitials(userDetails?.name || 'User')
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar */}
          <div className={cn(
            "lg:w-80 flex-shrink-0",
            "lg:block",
            sidebarOpen ? "block" : "hidden"
          )}>
            <Card className="p-6">
              {/* Profile Summary */}
              <div className="text-center mb-8">
                <div className="w-20 h-20 bg-primarycolor rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4">
                  {userDetails?.avatar_url ? (
                    <Image
                      src={userDetails.avatar_url}
                      alt="Profile"
                      width={80}
                      height={80}
                      className="w-full h-full rounded-full object-cover"
                    />
                  ) : (
                    getInitials(userDetails?.name || 'User')
                  )}
                </div>
                <h2 className="text-xl font-semibold text-gray-900 mb-1">
                  {userDetails?.name || 'User'}
                </h2>
                <p className="text-sm text-gray-500">
                  {userDetails?.email}
                </p>
              </div>

              {/* Navigation */}
              <nav className="space-y-2">
                {navigationItems.map((item) => {
                  const Icon = item.icon;
                  const isActive = pathname === item.href;
                  
                  return (
                    <Link
                      key={item.id}
                      href={item.href}
                      className={cn(
                        "flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",
                        isActive
                          ? "bg-primarycolor text-white"
                          : "text-gray-700 hover:bg-gray-100"
                      )}
                      onClick={() => setSidebarOpen(false)}
                    >
                      <Icon className="w-5 h-5" />
                      <div className="flex-1">
                        <div>{item.label}</div>
                        {!isActive && (
                          <div className="text-xs text-gray-500 mt-0.5">
                            {item.description}
                          </div>
                        )}
                      </div>
                    </Link>
                  );
                })}
              </nav>

              {/* Sign Out */}
              <div className="mt-8 pt-6 border-t border-gray-200">
                <Button
                  variant="ghost"
                  fullWidth
                  leftIcon={<LogOut className="w-5 h-5" />}
                  onClick={handleSignOut}
                  className="justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  Sign Out
                </Button>
              </div>
            </Card>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {/* Page Header */}
            <div className="mb-8">
              <div className="flex items-center gap-3 mb-2">
                <currentItem.icon className="w-6 h-6 text-primarycolor" />
                <h1 className="text-3xl font-bold text-gray-900">
                  {title || currentItem.label}
                </h1>
              </div>
              {subtitle && (
                <p className="text-gray-600">
                  {subtitle}
                </p>
              )}
            </div>

            {/* Page Content */}
            <div className="space-y-6">
              {children}
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Sidebar Overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
}

// Profile Section Component
export function ProfileSection({ title, children, className }) {
  return (
    <Card className={cn("p-6", className)}>
      {title && (
        <h2 className="text-xl font-semibold text-gray-900 mb-6">
          {title}
        </h2>
      )}
      {children}
    </Card>
  );
}

// Profile Stats Component
export function ProfileStats({ stats }) {
  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      {stats.map((stat, index) => (
        <Card key={index} className="p-4 text-center">
          <div className="text-2xl font-bold text-primarycolor mb-1">
            {stat.value}
          </div>
          <div className="text-sm text-gray-600">
            {stat.label}
          </div>
        </Card>
      ))}
    </div>
  );
}
