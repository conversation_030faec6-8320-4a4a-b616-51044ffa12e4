"use client";
import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Autoplay } from "swiper/modules";
import { ChevronRight } from "lucide-react";
import EnhancedProductCard from "./EnhancedProductCard";
import Link from "next/link";
import { createCategorySlug } from '../lib/utils';

// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";

export default function ProductCarousel({
  title,
  products,
  category,
  isSpecialCategory,
}) {
  const limitedProducts = products.slice(0, 10);

  const getViewMoreLink = () => {
    if (!category) return "/";

    if (isSpecialCategory) {
      return `/${category.toLowerCase().replace(/\s+/g, "-")}`;
    }

    // For regular categories, create proper slug
    const slug = createCategorySlug({ name: category });
    return `/categories/${slug}`;
  };
  return (
    <div className="py-12 relative product-carousel">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg sm:text-xl font-bold text-gray-900">
            {title}
          </h2>
          <Link
            href={getViewMoreLink()}
            className="text-sm text-primarycolor hover:text-primarycolor-700 font-medium flex items-center gap-1 transition-colors"
          >
            View All
            <ChevronRight className="w-4 h-4" />
          </Link>
        </div>

        <Swiper
          modules={[Navigation, Autoplay]}
          spaceBetween={12}
          slidesPerView={1.2}
          breakpoints={{
            480: {
              slidesPerView: 1.5,
              spaceBetween: 16,
            },
            640: {
              slidesPerView: 2,
              spaceBetween: 16,
            },
            768: {
              slidesPerView: 2.5,
              spaceBetween: 20,
            },
            1024: {
              slidesPerView: 3.5,
              spaceBetween: 24,
            },
            1280: {
              slidesPerView: 4.5,
              spaceBetween: 24,
            },
            1536: {
              slidesPerView: 5,
              spaceBetween: 24,
            },
          }}
          navigation={true}
          autoplay={{
            delay: 4000,
            disableOnInteraction: false,
            pauseOnMouseEnter: true,
          }}
          loop={limitedProducts.length > 3}
          className="modern-product-swiper"
        >
          {limitedProducts.map((product) => (
            <SwiperSlide
              key={product.id}
              className="!w-auto"
            >
              <div className="w-[240px] sm:w-[260px] lg:w-[280px]">
                <EnhancedProductCard
                  product={{
                    ...product,
                    image_url: Array.isArray(product.image_url)
                      ? product.image_url[0]
                      : product.image_url,
                  }}
                  compact={true}
                />
              </div>
            </SwiperSlide>
          ))}
        </Swiper>

        <style jsx global>{`
          .modern-product-swiper .swiper-button-next,
          .modern-product-swiper .swiper-button-prev {
            background: white;
            color: #c3e703;
            width: 44px;
            height: 44px;
            border-radius: 50%;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 2px solid #f3f4f6;
          }

          .modern-product-swiper .swiper-button-next:hover,
          .modern-product-swiper .swiper-button-prev:hover {
            background: #c3e703;
            color: white;
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(195, 231, 3, 0.3);
          }

          .modern-product-swiper .swiper-button-next::after,
          .modern-product-swiper .swiper-button-prev::after {
            font-size: 18px;
            font-weight: 600;
          }

          .modern-product-swiper .swiper-button-disabled {
            opacity: 0.3;
            pointer-events: none;
          }

          .modern-product-swiper .swiper-slide {
            transition: transform 0.3s ease;
          }
        `}</style>
      </div>
    </div>
  );
}
