import { render, screen, fireEvent, waitFor } from '../utils/test-utils'
import { useQuery, useMutation } from '@tanstack/react-query'
import CartPage from '../../app/cart/page'
import { mockUser, mockProduct, mockCartItem } from '../utils/test-utils'

// Mock the cart context
const mockUpdateCart = jest.fn()
const mockCartItems = [mockCartItem]

jest.mock('../../app/context/cartContext', () => ({
  useCart: () => ({
    cartItems: mockCartItems,
    cartCount: 1,
    updateCart: mockUpdateCart,
  }),
}))

// Mock the auth hook
jest.mock('../../app/hooks/useAuth', () => ({
  useAuth: () => ({
    user: mockUser,
    isAuthenticated: true,
  }),
}))

// Mock Next.js router
const mockPush = jest.fn()
const mockBack = jest.fn()

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    back: mockBack,
  }),
}))

describe('Cart Flow Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('displays cart items correctly', async () => {
    render(<CartPage />, {
      user: mockUser,
      isAuthenticated: true,
    })

    await waitFor(() => {
      expect(screen.getByText('MY CART')).toBeInTheDocument()
      expect(screen.getByText(mockProduct.name)).toBeInTheDocument()
    })
  })

  it('handles quantity changes', async () => {
    render(<CartPage />, {
      user: mockUser,
      isAuthenticated: true,
    })

    await waitFor(() => {
      const increaseButton = screen.getByRole('button', { name: /increase/i })
      fireEvent.click(increaseButton)
    })

    expect(mockUpdateCart).toHaveBeenCalledWith(
      expect.arrayContaining([
        expect.objectContaining({
          productId: mockCartItem.product_id,
          quantity: mockCartItem.quantity + 1,
        }),
      ])
    )
  })

  it('handles item removal', async () => {
    render(<CartPage />, {
      user: mockUser,
      isAuthenticated: true,
    })

    await waitFor(() => {
      const removeButton = screen.getByRole('button', { name: /remove/i })
      fireEvent.click(removeButton)
    })

    expect(mockUpdateCart).toHaveBeenCalledWith([])
  })

  it('calculates total correctly', async () => {
    render(<CartPage />, {
      user: mockUser,
      isAuthenticated: true,
    })

    await waitFor(() => {
      const expectedTotal = mockCartItem.quantity * mockProduct.price
      expect(screen.getByText(`Ksh. ${expectedTotal.toFixed(2)}`)).toBeInTheDocument()
    })
  })

  it('navigates to checkout when proceed button is clicked', async () => {
    render(<CartPage />, {
      user: mockUser,
      isAuthenticated: true,
    })

    await waitFor(() => {
      const checkoutButton = screen.getByText('Proceed to Checkout')
      fireEvent.click(checkoutButton)
    })

    expect(mockPush).toHaveBeenCalledWith('/checkout')
  })

  it('shows empty cart message when no items', async () => {
    // Mock empty cart
    jest.doMock('../../app/context/cartContext', () => ({
      useCart: () => ({
        cartItems: [],
        cartCount: 0,
        updateCart: mockUpdateCart,
      }),
    }))

    render(<CartPage />, {
      user: mockUser,
      isAuthenticated: true,
    })

    await waitFor(() => {
      expect(screen.getByText('Your cart is empty')).toBeInTheDocument()
      expect(screen.getByText('Browse our products and discover our best deals!')).toBeInTheDocument()
    })
  })

  it('handles back navigation', async () => {
    render(<CartPage />, {
      user: mockUser,
      isAuthenticated: true,
    })

    await waitFor(() => {
      const backButton = screen.getByRole('button', { name: /back/i })
      fireEvent.click(backButton)
    })

    expect(mockBack).toHaveBeenCalled()
  })

  it('displays cart count in header', async () => {
    render(<CartPage />, {
      user: mockUser,
      isAuthenticated: true,
    })

    await waitFor(() => {
      expect(screen.getByText('1')).toBeInTheDocument() // Cart count
    })
  })

  it('handles loading state', () => {
    render(<CartPage />, {
      user: mockUser,
      isAuthenticated: true,
    })

    // Initially should show loading or the cart content
    // This test might need adjustment based on actual loading implementation
    expect(screen.getByText('MY CART')).toBeInTheDocument()
  })

  it('applies discounts correctly', async () => {
    const discountedProduct = {
      ...mockProduct,
      discount: 20,
    }

    const discountedCartItem = {
      ...mockCartItem,
      products: discountedProduct,
    }

    // Mock cart with discounted item
    jest.doMock('../../app/context/cartContext', () => ({
      useCart: () => ({
        cartItems: [discountedCartItem],
        cartCount: 1,
        updateCart: mockUpdateCart,
      }),
    }))

    render(<CartPage />, {
      user: mockUser,
      isAuthenticated: true,
    })

    await waitFor(() => {
      const originalPrice = discountedProduct.price
      const discountedPrice = originalPrice * 0.8
      const total = discountedPrice * discountedCartItem.quantity

      expect(screen.getByText(`Ksh. ${total.toFixed(2)}`)).toBeInTheDocument()
    })
  })

  it('prevents checkout with empty cart', async () => {
    // Mock empty cart
    jest.doMock('../../app/context/cartContext', () => ({
      useCart: () => ({
        cartItems: [],
        cartCount: 0,
        updateCart: mockUpdateCart,
      }),
    }))

    render(<CartPage />, {
      user: mockUser,
      isAuthenticated: true,
    })

    await waitFor(() => {
      expect(screen.queryByText('Proceed to Checkout')).not.toBeInTheDocument()
    })
  })

  it('handles quantity input validation', async () => {
    render(<CartPage />, {
      user: mockUser,
      isAuthenticated: true,
    })

    await waitFor(() => {
      const quantityInput = screen.getByDisplayValue(mockCartItem.quantity.toString())
      
      // Try to set quantity to 0
      fireEvent.change(quantityInput, { target: { value: '0' } })
      fireEvent.blur(quantityInput)
    })

    // Should not update to 0 or should remove item
    expect(mockUpdateCart).toHaveBeenCalled()
  })

  it('persists cart changes for authenticated users', async () => {
    render(<CartPage />, {
      user: mockUser,
      isAuthenticated: true,
    })

    await waitFor(() => {
      const increaseButton = screen.getByRole('button', { name: /increase/i })
      fireEvent.click(increaseButton)
    })

    // Should call updateCart which should sync with database for authenticated users
    expect(mockUpdateCart).toHaveBeenCalled()
  })
})
