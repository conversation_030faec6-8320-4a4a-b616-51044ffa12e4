"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '../../hooks/useAuth';
import { PasswordInput, calculatePasswordStrength } from '../../components/ui/PasswordStrength';
import { signupSchema } from '../../lib/validations';

export default function SignUpPage() {
  const { signUp, userDetails, isLoading } = useAuth();
  const router = useRouter();
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Redirect if already authenticated
  useEffect(() => {
    console.log('Signup page - userDetails changed:', userDetails);
    if (userDetails) {
      console.log('Redirecting to profile page...');
      router.push('/profile?welcome=true');
    }
  }, [userDetails, router]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setIsSubmitting(true);

    try {
      setError('');
      setSuccess('');

      // Validate form data using Zod schema
      const formData = {
        name: name?.trim() || '',
        email: email?.trim().toLowerCase() || '',
        password: password || '',
        confirmPassword: confirmPassword || ''
      };

      console.log('Form data:', {
        name: formData.name,
        nameLength: formData.name.length,
        nameChars: formData.name.split('').map(c => `${c}(${c.charCodeAt(0)})`),
        email: formData.email,
        passwordLength: formData.password.length,
        hasSchema: !!signupSchema
      });

      // Basic field validation
      if (!formData.name || !formData.email || !formData.password || !confirmPassword) {
        throw new Error('Please fill in all fields');
      }

      // Test name regex manually first
      const nameRegex = /^[a-zA-Z\u00C0-\u017F\s'-\.]+$/;
      console.log('Name regex test:', {
        name: formData.name,
        passes: nameRegex.test(formData.name),
        regex: nameRegex.toString()
      });

      // Test password regex manually
      const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/;
      console.log('Password regex test:', {
        passwordLength: formData.password.length,
        hasLowercase: /[a-z]/.test(formData.password),
        hasUppercase: /[A-Z]/.test(formData.password),
        hasNumber: /\d/.test(formData.password),
        passesFullRegex: passwordRegex.test(formData.password),
        regex: passwordRegex.toString()
      });

      // Validate using Zod schema (including confirmPassword)
      if (signupSchema) {
        const validationResult = signupSchema.safeParse({
          name: formData.name,
          email: formData.email,
          password: formData.password,
          confirmPassword: formData.confirmPassword
        });

        if (!validationResult.success) {
          const errors = validationResult.error?.errors || [];
          console.log('Full validation result:', validationResult);
          console.log('Validation error object:', validationResult.error);
          console.log('Validation errors array:', errors);
          console.log('Error issues:', validationResult.error?.issues);

          // Try to get errors from issues instead of errors
          const issues = validationResult.error?.issues || [];

          if (issues.length > 0) {
            // Show the first error with field context
            const firstIssue = issues[0];
            const fieldName = firstIssue.path?.join('.') || 'field';
            throw new Error(`${fieldName}: ${firstIssue.message}`);
          } else if (errors.length > 0) {
            // Fallback to errors array
            const firstError = errors[0];
            const fieldName = firstError.path?.join('.') || 'field';
            throw new Error(`${fieldName}: ${firstError.message}`);
          } else {
            console.log('No specific errors found in validation result:', validationResult.error);
            throw new Error('Validation failed. Please check your input and try again.');
          }
        }
      } else {
        // Fallback validation if schema is not available
        if (formData.name.length < 2) {
          throw new Error('Name must be at least 2 characters');
        }
        if (formData.password.length < 6) {
          throw new Error('Password must be at least 6 characters');
        }
      }

      // Password matching is now handled by Zod schema validation above

      // Password strength validation
      const passwordStrength = calculatePasswordStrength(formData.password);
      const score = passwordStrength?.score || 0;
      if (score < 3) {
        throw new Error('Please choose a stronger password. Follow the requirements below.');
      }

      if (!acceptTerms) {
        throw new Error('Please accept the Terms of Service and Privacy Policy');
      }

      // Call signup with validated data
      console.log('=== SIGNUP PROCESS START ===');
      console.log('Calling signUp with data:', {
        email: formData.email,
        name: formData.name,
        passwordLength: formData.password.length
      });

      await signUp(formData.email, formData.password, formData.name);
      console.log('=== SIGNUP PROCESS COMPLETED ===');

      setSuccess('Account created successfully! Redirecting to your profile...');

      // Add a small delay to allow auth state to update, then redirect manually if needed
      setTimeout(() => {
        console.log('=== MANUAL REDIRECT TRIGGERED ===');
        router.push('/profile?welcome=true');
      }, 2000);
    } catch (error) {
      console.error('Signup error:', error);
      setError(error.message || 'Registration failed. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primarycolor-50 via-white to-secondarycolor-50 flex">
      {/* Left Side - Branding */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-primarycolor to-primarycolor-800 relative overflow-hidden">
        <div className="absolute inset-0 bg-grid-pattern opacity-10" />

        <div className="relative z-10 flex flex-col justify-center px-12 text-white">
          <div className="max-w-md">
            {/* Logo */}
            <div className="mb-8">
              <Link href="/" className="flex items-center gap-3">
                <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center">
                  <span className="text-2xl font-bold text-primarycolor">PM</span>
                </div>
                <span className="text-2xl font-bold">Pinchez Merchants</span>
              </Link>
            </div>

            <h1 className="text-4xl font-bold mb-6">
              Join Our Community
            </h1>

            <p className="text-primarycolor-100 text-lg mb-8 leading-relaxed">
              Create your account and discover amazing products with fast delivery
              and exceptional customer service.
            </p>

            {/* Benefits */}
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primarycolor-600 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <p className="font-semibold">Free Account</p>
                  <p className="text-primarycolor-200 text-sm">No hidden fees</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primarycolor-600 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                  </svg>
                </div>
                <div>
                  <p className="font-semibold">Personal Dashboard</p>
                  <p className="text-primarycolor-200 text-sm">Track your orders</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primarycolor-600 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <p className="font-semibold">Wishlist & Favorites</p>
                  <p className="text-primarycolor-200 text-sm">Save items you love</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Auth Form */}
      <div className="flex-1 flex flex-col justify-center px-6 sm:px-12 lg:px-16">
        <div className="w-full max-w-md mx-auto">
          {/* Back Button */}
          <div className="mb-8">
            <button
              onClick={() => window.history.back()}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Back
            </button>
          </div>

          {/* Mobile Logo */}
          <div className="lg:hidden mb-8 text-center">
            <Link href="/" className="inline-flex items-center gap-3">
              <div className="w-10 h-10 bg-primarycolor rounded-xl flex items-center justify-center">
                <span className="text-xl font-bold text-white">PM</span>
              </div>
              <span className="text-xl font-bold text-gray-900">Pinchez Merchants</span>
            </Link>
          </div>

          {/* Form Card */}
          <div className="bg-white p-8 rounded-lg shadow-md">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                Create Account
              </h2>
              <p className="text-gray-600">
                Join thousands of happy customers today
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-1">
                <label className="block text-sm font-medium text-gray-700">
                  Full Name <span className="text-red-500 ml-1">*</span>
                </label>
                <input
                  type="text"
                  placeholder="Enter your full name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  disabled={isSubmitting}
                  required
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primarycolor focus:border-primarycolor disabled:opacity-50 disabled:cursor-not-allowed"
                />
              </div>

              <div className="space-y-1">
                <label className="block text-sm font-medium text-gray-700">
                  Email Address <span className="text-red-500 ml-1">*</span>
                </label>
                <input
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isSubmitting}
                  required
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primarycolor focus:border-primarycolor disabled:opacity-50 disabled:cursor-not-allowed"
                />
              </div>

              <div className="space-y-1">
                <label className="block text-sm font-medium text-gray-700">
                  Password <span className="text-red-500 ml-1">*</span>
                </label>
                <PasswordInput
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Create a strong password"
                  disabled={isSubmitting}
                  showStrength={true}
                  required
                />
              </div>

              <div className="space-y-1">
                <label className="block text-sm font-medium text-gray-700">
                  Confirm Password <span className="text-red-500 ml-1">*</span>
                </label>
                <PasswordInput
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder="Confirm your password"
                  disabled={isSubmitting}
                  showStrength={false}
                  required
                />
                {confirmPassword && password !== confirmPassword && (
                  <p className="text-xs text-red-600 mt-1">Passwords do not match</p>
                )}
                {confirmPassword && password === confirmPassword && confirmPassword.length > 0 && (
                  <p className="text-xs text-green-600 mt-1 flex items-center gap-1">
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    Passwords match
                  </p>
                )}
              </div>

              <div className="flex items-start gap-3">
                <input
                  type="checkbox"
                  id="acceptTerms"
                  checked={acceptTerms}
                  onChange={(e) => setAcceptTerms(e.target.checked)}
                  className="w-4 h-4 text-primarycolor border-gray-300 rounded focus:ring-primarycolor mt-0.5"
                  required
                />
                <label htmlFor="acceptTerms" className="text-sm text-gray-600 leading-relaxed">
                  I agree to the{' '}
                  <Link href="/terms" className="text-primarycolor hover:underline font-medium">
                    Terms of Service
                  </Link>{' '}
                  and{' '}
                  <Link href="/privacy" className="text-primarycolor hover:underline font-medium">
                    Privacy Policy
                  </Link>
                </label>
              </div>

              {error && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-sm text-red-600">{error}</p>
                </div>
              )}

              {success && (
                <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                  <p className="text-sm text-green-600 flex items-center gap-2">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    {success}
                  </p>
                </div>
              )}

              <button
                type="submit"
                disabled={!name || !email || !password || !confirmPassword || !acceptTerms || password !== confirmPassword || isSubmitting || isLoading || success}
                className="w-full inline-flex items-center justify-center px-6 py-3 text-lg font-medium rounded-lg text-white bg-primarycolor hover:bg-primarycolor-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primarycolor disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {(isSubmitting || isLoading) && (
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                  </svg>
                )}
                {isSubmitting || isLoading ? 'Creating Account...' : success ? 'Account Created!' : 'Create Account'}
              </button>
            </form>

            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                Already have an account?{' '}
                <Link
                  href="/auth/login"
                  className="font-medium text-primarycolor hover:text-primarycolor-700"
                >
                  Sign in
                </Link>
              </p>
            </div>
          </div>

          {/* Footer */}
          <div className="mt-8 text-center text-sm text-gray-500">
            <p>
              By continuing, you agree to our{' '}
              <Link href="/terms" className="text-primarycolor hover:underline">
                Terms of Service
              </Link>{' '}
              and{' '}
              <Link href="/privacy" className="text-primarycolor hover:underline">
                Privacy Policy
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}