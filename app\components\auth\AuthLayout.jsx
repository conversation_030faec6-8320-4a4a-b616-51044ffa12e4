"use client";

import React from 'react';
import Link from 'next/link';
import { ArrowLeft, Shield, Users, Star } from 'lucide-react';
import Card from '../ui/Card';
import Button from '../ui/Button';
import { cn } from '../../lib/utils';

export default function AuthLayout({ 
  children, 
  title, 
  subtitle, 
  showBackButton = true,
  className 
}) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primarycolor-50 via-white to-secondarycolor-50 flex">
      {/* Left Side - Branding */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-primarycolor to-primarycolor-800 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-grid-pattern opacity-10" />
        
        <div className="relative z-10 flex flex-col justify-center px-12 text-white">
          <div className="max-w-md">
            {/* Logo */}
            <div className="mb-8">
              <Link href="/" className="flex items-center gap-3">
                <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center">
                  <span className="text-2xl font-bold text-primarycolor">P</span>
                </div>
                <span className="text-2xl font-bold">Pinchez Merchants</span>
              </Link>
            </div>

            <h1 className="text-4xl font-bold mb-6">
              Welcome to Your Shopping Paradise
            </h1>
            
            <p className="text-primarycolor-100 text-lg mb-8 leading-relaxed">
              Join thousands of satisfied customers who trust us for quality products, 
              fast delivery, and exceptional service.
            </p>

            {/* Trust Indicators */}
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primarycolor-600 rounded-lg flex items-center justify-center">
                  <Shield className="w-5 h-5" />
                </div>
                <div>
                  <p className="font-semibold">Secure & Safe</p>
                  <p className="text-sm text-primarycolor-200">Your data is protected with enterprise-grade security</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primarycolor-600 rounded-lg flex items-center justify-center">
                  <Users className="w-5 h-5" />
                </div>
                <div>
                  <p className="font-semibold">10,000+ Happy Customers</p>
                  <p className="text-sm text-primarycolor-200">Join our growing community of satisfied shoppers</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primarycolor-600 rounded-lg flex items-center justify-center">
                  <Star className="w-5 h-5 fill-current" />
                </div>
                <div>
                  <p className="font-semibold">4.8/5 Rating</p>
                  <p className="text-sm text-primarycolor-200">Consistently rated excellent by our customers</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute top-20 right-20 w-32 h-32 bg-secondarycolor rounded-full opacity-20 animate-pulse" />
        <div className="absolute bottom-20 right-32 w-24 h-24 bg-white rounded-full opacity-10 animate-pulse" style={{ animationDelay: '1s' }} />
      </div>

      {/* Right Side - Auth Form */}
      <div className="flex-1 flex flex-col justify-center px-6 sm:px-12 lg:px-16">
        <div className="w-full max-w-md mx-auto">
          {/* Back Button */}
          {showBackButton && (
            <div className="mb-8">
              <Button
                variant="ghost"
                leftIcon={<ArrowLeft className="w-4 h-4" />}
                onClick={() => window.history.back()}
                className="text-gray-600 hover:text-gray-900"
              >
                Back
              </Button>
            </div>
          )}

          {/* Mobile Logo */}
          <div className="lg:hidden mb-8 text-center">
            <Link href="/" className="inline-flex items-center gap-3">
              <div className="w-10 h-10 bg-primarycolor rounded-xl flex items-center justify-center">
                <span className="text-xl font-bold text-white">P</span>
              </div>
              <span className="text-xl font-bold text-gray-900">Pinchez Merchants</span>
            </Link>
          </div>

          {/* Form Card */}
          <Card className={cn("p-8", className)}>
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                {title}
              </h2>
              {subtitle && (
                <p className="text-gray-600">
                  {subtitle}
                </p>
              )}
            </div>

            {children}
          </Card>

          {/* Footer */}
          <div className="mt-8 text-center text-sm text-gray-500">
            <p>
              By continuing, you agree to our{' '}
              <Link href="/terms" className="text-primarycolor hover:underline">
                Terms of Service
              </Link>{' '}
              and{' '}
              <Link href="/privacy" className="text-primarycolor hover:underline">
                Privacy Policy
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

// Social Login Button Component
export function SocialLoginButton({ 
  provider, 
  icon, 
  onClick, 
  disabled = false,
  className 
}) {
  return (
    <Button
      variant="outline"
      fullWidth
      leftIcon={icon}
      onClick={onClick}
      disabled={disabled}
      className={cn(
        "justify-center py-3 border-gray-300 hover:border-gray-400 hover:bg-gray-50",
        className
      )}
    >
      Continue with {provider}
    </Button>
  );
}

// Divider Component
export function AuthDivider({ text = "or" }) {
  return (
    <div className="relative my-6">
      <div className="absolute inset-0 flex items-center">
        <div className="w-full border-t border-gray-300" />
      </div>
      <div className="relative flex justify-center text-sm">
        <span className="px-4 bg-white text-gray-500 font-medium">
          {text}
        </span>
      </div>
    </div>
  );
}

// Password Strength Indicator
export function PasswordStrengthIndicator({ password }) {
  const getStrength = (password) => {
    if (!password) return { score: 0, label: '', color: '' };
    
    let score = 0;
    if (password.length >= 8) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;

    const levels = [
      { label: 'Very Weak', color: 'bg-red-500' },
      { label: 'Weak', color: 'bg-orange-500' },
      { label: 'Fair', color: 'bg-yellow-500' },
      { label: 'Good', color: 'bg-blue-500' },
      { label: 'Strong', color: 'bg-green-500' },
    ];

    return { score, ...levels[score] };
  };

  const strength = getStrength(password);

  if (!password) return null;

  return (
    <div className="mt-2">
      <div className="flex gap-1 mb-1">
        {[...Array(5)].map((_, i) => (
          <div
            key={i}
            className={cn(
              "h-1 flex-1 rounded-full transition-colors",
              i < strength.score ? strength.color : 'bg-gray-200'
            )}
          />
        ))}
      </div>
      <p className={cn(
        "text-xs font-medium",
        strength.score < 2 ? 'text-red-600' :
        strength.score < 4 ? 'text-yellow-600' : 'text-green-600'
      )}>
        Password strength: {strength.label}
      </p>
    </div>
  );
}
