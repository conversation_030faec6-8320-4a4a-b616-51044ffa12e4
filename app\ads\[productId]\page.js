"use client";
import React, { useEffect, useState, useCallback } from "react";
import { useParams, useRouter } from "next/navigation";
import Image from "next/image";
import {
  ChevronLeft,
  Heart,
  ShoppingCart,
  Clock,
  Shield,
  Truck,
} from "lucide-react";
import { useProduct } from "../../hooks/useQueries";
import { useAuth } from "../../hooks/useAuth";
import { useCart } from "../../context/cartContext";
import { WishlistContext } from "../../context/wishlistContext";
import { useContext } from "react";
import { useSupabase } from "../../hooks/useSupabase";
import ProductCarousel from "../../components/productcarousel";
import { toast } from "sonner";

export default function PromotedProductPage() {
  const router = useRouter();

  // Prefetch checkout page
  useEffect(() => {
    router.prefetch("/checkout");
  }, [router]);

  const { cartItems, updateCart } = useCart();
  const { user } = useAuth();
  const [quantity, setQuantity] = useState(1);
  const { isInWishlist, toggleWishlistItem } = useContext(WishlistContext);
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [relatedProducts, setRelatedProducts] = useState([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const params = useParams();
  const [timeLeft, setTimeLeft] = useState("");

  const { data: product, isLoading } = useProduct(params.productId);
  const { fetchRelatedPromotedProducts } = useSupabase();

  // Fast Buy Now handler
  const handleBuyNow = () => {
    if (!product) return;

    const discountedPrice = product.price * (1 - product.discount / 100);
    const existingItem = cartItems.find(
      (item) => item.product.id.toString() === product.id.toString()
    );

    let updatedCart;
    if (existingItem) {
      updatedCart = cartItems.map((item) =>
        item.product.id.toString() === product.id.toString()
          ? {
              ...item,
              quantity: item.quantity + quantity,
              total_amount: discountedPrice * (item.quantity + quantity),
            }
          : item
      );
    } else {
      const newItem = {
        productId: product.id,
        quantity,
        total_amount: discountedPrice * quantity,
        product: {
          id: product.id,
          name: product.name,
          price: discountedPrice,
          image_url: product.image_url,
          is_promoted: true,
          discount: product.discount,
        },
      };
      updatedCart = [...cartItems, newItem];
    }

    updateCart(updatedCart);
    router.push("/checkout");
  };

  useEffect(() => {
    if (product?.category_name && product?.id) {
      const loadRelatedProducts = async () => {
        try {
          const related = await fetchRelatedPromotedProducts(
            product.category_name,
            product.id
          );
          console.log("Related products:", related); // For debugging
          if (Array.isArray(related) && related.length > 0) {
            setRelatedProducts(related);
          }
        } catch (error) {
          console.error("Error fetching related products:", error);
        }
      };

      loadRelatedProducts();
    }
  }, [product, fetchRelatedPromotedProducts]);

  useEffect(() => {
    if (product?.promotion_end_date) {
      const timer = setInterval(() => {
        const end = new Date(product.promotion_end_date);
        const now = new Date();
        const diff = end - now;

        if (diff > 0) {
          const days = Math.floor(diff / (1000 * 60 * 60 * 24));
          const hours = Math.floor(
            (diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
          );
          const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
          setTimeLeft(`${days}d ${hours}h ${minutes}m`);
        }
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [product]);

  const handleAddToCart = async () => {
    if (isAddingToCart || !product) return;
    setIsAddingToCart(true);

    try {
      const discountedPrice = product.price * (1 - product.discount / 100);
      const existingItem = cartItems.find(
        (item) => item.product.id === product.id
      );

      let updatedCart;
      if (existingItem) {
        updatedCart = cartItems.map((item) =>
          item.product.id === product.id
            ? {
                ...item,
                quantity: item.quantity + quantity,
                total_amount: discountedPrice * (item.quantity + quantity),
              }
            : item
        );
      } else {
        const newItem = {
          productId: product.id,
          quantity,
          total_amount: discountedPrice * quantity,
          product: {
            id: product.id,
            name: product.name,
            price: discountedPrice,
            image_url: product.image_url,
            is_promoted: true,
            discount: product.discount,
          },
        };
        updatedCart = [...cartItems, newItem];
      }

      updateCart(updatedCart);
      toast.success("Product added to cart successfully!");
    } catch (error) {
      console.error("Error adding to cart:", error);
      toast.error("Failed to add product to cart");
    } finally {
      setIsAddingToCart(false);
    }
  };

  const handleWishlistClick = useCallback(() => {
    toggleWishlistItem(product);
  }, [toggleWishlistItem, product]);

  if (isLoading)
    return <p className="text-primarycolor text-center">Loading...</p>;
  if (!product)
    return <p className="text-primarycolor text-center">Product not found</p>;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-sm">
        <div className="max-w-6xl mx-auto px-4">
          <div className="flex items-center justify-between py-4">
            <button
              onClick={() => router.back()}
              className="flex items-center text-gray-600 hover:text-primarycolor"
            >
              <ChevronLeft className="w-5 h-5" />
              <span>Back</span>
            </button>
            <Heart
              className={`w-6 h-6 cursor-pointer ${
                isInWishlist(product?.id)
                  ? "fill-warningcolor text-warningcolor"
                  : "fill-none text-warningcolor"
              }`}
              onClick={handleWishlistClick}
            />
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-sm">
          <div className="grid md:grid-cols-2 gap-8 p-6">
            {/* Image Section */}
            <div className="relative">
              <div className="absolute top-2 left-2 z-10 space-y-2">
                <div className="bg-warningcolor/90 backdrop-blur-sm text-white px-3 py-2 rounded-lg text-sm font-bold shadow-lg">
                  SPECIAL OFFER
                </div>
                {timeLeft && (
                  <div className="bg-primarycolor/90 backdrop-blur-sm text-white px-3 py-2 rounded-lg flex items-center gap-2 shadow-lg">
                    <Clock className="w-4 h-4" />
                    <span className="font-mono font-bold text-sm">
                      {timeLeft} LEFT!
                    </span>
                  </div>
                )}
              </div>

              <div className="relative aspect-square bg-gray-50 rounded-lg">
                <Image
                  src={
                    Array.isArray(product.image_url)
                      ? product.image_url[currentImageIndex]
                      : product.image_url
                  }
                  alt={product.name}
                  fill
                  priority
                  loading="eager"
                  className="object-contain p-4"
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
              </div>

              {/* Thumbnail Gallery */}
              {Array.isArray(product.image_url) &&
                product.image_url.length > 1 && (
                  <div className="mt-4 flex gap-2 overflow-x-auto">
                    {product.image_url.map((url, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentImageIndex(index)}
                        className={`relative w-20 h-20 border-2 rounded-lg ${
                          currentImageIndex === index
                            ? "border-primarycolor"
                            : "border-transparent"
                        }`}
                      >
                        <Image
                          src={url}
                          alt={`${product.name} - Image ${index + 1}`}
                          fill
                          className="object-contain p-2"
                          sizes="80px"
                        />
                      </button>
                    ))}
                  </div>
                )}
            </div>

            {/* Product Info Section */}
            <div className="flex flex-col">
              {/* Add urgency indicator */}
              <div className="mb-4 text-warningcolor font-medium">
                🔥 Limited Time Offer
              </div>
              <h1 className="text-3xl md:text-4xl font-bold text-primarycolor mb-4">
                {product.name}
              </h1>
              {/* Move savings calculation up */}
              {product.discount > 0 && (
                <div className="bg-red-50 p-4 rounded-lg mb-6 inline-block">
                  <span className="text-xl font-bold text-red-600">
                    Save Ksh.{" "}
                    {Math.round(
                      Number(product.price) * (product.discount / 100)
                    )}
                    !
                  </span>
                </div>
              )}
              {/* Enhanced price display */}
              <div className="flex flex-col gap-2 mb-6">
                <div className="flex items-baseline gap-2 sm:gap-4">
                  <span className="text-2xl sm:text-4xl md:text-5xl font-bold text-warningcolor">
                    Ksh.{" "}
                    {Math.round(
                      Number(product.price) * (1 - product.discount / 100)
                    )}
                  </span>
                  {product.discount > 0 && (
                    <>
                      <span className="text-lg sm:text-2xl text-gray-400 line-through">
                        Ksh. {Math.round(Number(product.price))}
                      </span>
                      <span className="bg-warningcolor text-white px-2 sm:px-4 py-1 rounded-full text-xs sm:text-lg font-bold whitespace-nowrap">
                        {Math.round(product.discount)}% OFF
                      </span>
                    </>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-3 gap-2 mb-6 w-full sm:flex sm:flex-row sm:justify-between">
                <div className="bg-green-50 px-2 py-2 rounded-full text-xs sm:text-sm text-center">
                  <span className="text-green-600 font-medium">
                    ✓ Fast Delivery
                  </span>
                </div>
                <div className="bg-blue-50 px-2 py-2 rounded-full text-xs sm:text-sm text-center">
                  <span className="text-blue-600 font-medium">
                    ✓ 7-Day Returns
                  </span>
                </div>
                <div className="bg-purple-50 px-2 py-2 rounded-full text-xs sm:text-sm text-center">
                  <span className="text-purple-600 font-medium">
                    ✓ Genuine Product
                  </span>
                </div>
              </div>
              <div className="mb-6">
                <span className="block text-gray-600 mb-2">
                  Select Quantity
                </span>
                <div className="inline-flex border border-primarycolor rounded-lg overflow-hidden">
                  <button
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    className="w-12 h-12 flex items-center justify-center text-primarycolor hover:bg-gray-50"
                  >
                    -
                  </button>
                  <span className="w-16 h-12 flex items-center justify-center border-x border-primarycolor text-primarycolor font-medium">
                    {quantity}
                  </span>
                  <button
                    onClick={() => setQuantity(quantity + 1)}
                    className="w-12 h-12 flex items-center justify-center text-primarycolor hover:bg-gray-50"
                  >
                    +
                  </button>
                </div>
              </div>
              <div className="flex flex-col gap-3">
                <button
                  onClick={handleBuyNow}
                  className="w-full h-14 bg-warningcolor text-white text-lg font-semibold rounded-lg hover:bg-warningcolor/90 transition-colors flex items-center justify-center gap-2"
                >
                  Buy Now
                </button>

                <button
                  onClick={handleAddToCart}
                  disabled={isAddingToCart}
                  className="w-full h-14 bg-primarycolor text-white text-lg font-semibold rounded-lg hover:bg-primarycolor/90 transition-colors flex items-center justify-center gap-2"
                >
                  <ShoppingCart className="w-6 h-6" />
                  {isAddingToCart ? "Adding..." : "Add to Cart"}
                </button>
              </div>
              <div className="mt-6 bg-green-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-green-600 font-bold">⚡ Hot Deal</span>
                </div>
                <p className="text-green-700">
                  <span className="font-bold">
                    {Math.floor(Math.random() * 50) + 20} people
                  </span>{" "}
                  purchased in the last hour
                </p>
                <div className="w-full bg-green-200 rounded-full h-2 mt-2">
                  <div
                    className="bg-green-600 h-2 rounded-full"
                    style={{ width: "75%" }}
                  ></div>
                </div>
                <p className="text-sm text-green-700 mt-1">Stock: 75% sold</p>
              </div>
              {product.description && (
                <div className="mt-8 pt-6 border-t">
                  <h3 className="text-lg font-semibold text-primarycolor mb-4">
                    Product Description
                  </h3>
                  <div className="space-y-4">
                    {product.description
                      .split("\n\n")
                      .map((paragraph, index) => (
                        <p
                          key={index}
                          className="text-gray-600 leading-relaxed"
                        >
                          {paragraph}
                        </p>
                      ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
        {relatedProducts?.length > 0 && (
          <div className="mt-12">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-primarycolor mb-6">
                More Special Offers You&apos;ll Love
              </h2>
              <ProductCarousel
                products={relatedProducts.slice(0, 6)} // Limit to 6 products
                isSpecialCategory={true}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
