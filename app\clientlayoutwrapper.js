'use client';

import { usePathname } from 'next/navigation';
import ModernHeader from './components/ModernHeader';
import Footer from './components/footer';
import { Toaster } from 'sonner';

export default function ClientLayoutWrapper({ children }) {
  const pathname = usePathname();
  const isAdminRoute = pathname.startsWith('/admin');
  const isAuthRoute = pathname.startsWith('/auth');
  const isProfileRoute = pathname.startsWith('/profile');

  // Show only children for admin, auth, and profile routes
  if (isAdminRoute || isAuthRoute || isProfileRoute) {
    return (
      <>
        {children}
        <Toaster 
          theme="light"
          position="top-right"
          toastOptions={{
            style: {
              background: 'var(--primarycolor)',
              color: 'white',
              borderRadius: '24px',
            },
          }}
        />
      </>
    );
  }

  // Show full layout for all other routes
  return (
    <>
      <ModernHeader />
      <main className="min-h-screen">{children}</main>
      <Footer />
      <Toaster 
        theme="light"
        position="top-right"
        toastOptions={{
          style: {
            background: 'var(--primarycolor)',
            color: 'white',
            borderRadius: '24px',
          },
        }}
      />
    </>
  );
}