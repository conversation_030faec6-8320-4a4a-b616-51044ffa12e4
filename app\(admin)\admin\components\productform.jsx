"use client";
import { useState, useEffect, useMemo } from "react";
import { useSupabase } from "../hooks/useSupabase";
import Image from "next/image";
import { toast } from "sonner";

export default function ProductForm({ product }) {
  const initialFormState = useMemo(
    () => ({
      name: "",
      description: "",
      category_id: "",
      price: "",
      quantity: "",
      discount: "0",
      image_url: [],
      is_promoted: false,
      promotion_start_date: "",
      promotion_end_date: "",
      promotion_type: "",
    }),
    []
  );

  const [formData, setFormData] = useState(initialFormState);
  const { useAddProduct, useUpdateProduct, useCategories, uploadProductImage } =
    useSupabase();
  const addProduct = useAddProduct();
  const updateProduct = useUpdateProduct();
  const { data: categories } = useCategories();
  const [imageFiles, setImageFiles] = useState([]);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);

  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name || "",
        description: product.description || "",
        category_id: product.category_id || "",
        price: product.price || "",
        quantity: product.quantity || "",
        discount: product.discount || "0",
        image_url: product.image_url || "",
        is_promoted: product.is_promoted || false,
        promotion_start_date: product.promotion_start_date || "",
        promotion_end_date: product.promotion_end_date || "",
        promotion_type: product.promotion_type || "",
      });
    } else {
      setFormData(initialFormState);
    }
  }, [initialFormState, product]);

  const handleImageDelete = async (indexToDelete) => {
    const updatedImages = formData.image_url.filter(
      (_, index) => index !== indexToDelete
    );
    setFormData((prev) => ({
      ...prev,
      image_url: updatedImages,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    let imageUrls = formData.image_url;
  
    if (imageFiles.length > 0) {
      setIsUploading(true);
      try {
        const uploadPromises = imageFiles.map((file, index) => {
          return new Promise(async (resolve, reject) => {
            try {
              const url = await uploadProductImage(file);
              setUploadProgress((prev) => (index + 1) / imageFiles.length * 100);
              resolve(url);
            } catch (error) {
              reject(error);
            }
          });
        });
  
        const urls = await Promise.all(uploadPromises);
        imageUrls = [...(Array.isArray(imageUrls) ? imageUrls : []), ...urls];
      } catch (error) {
        console.error('Image upload error:', error);
        toast.error(`Image upload error: ${error.message}`);
        return;
      } finally {
        setIsUploading(false);
        setUploadProgress(0);
      }
    }
  
    try {
      const selectedCategory = categories?.find(cat => cat.id === formData.category_id);
      const productData = {
        ...formData,
        image_url: imageUrls,
        category_name: selectedCategory?.name
      };
  
      if (product) {
        await updateProduct.mutateAsync({ id: product.id, product: productData });
        toast.success('Product updated successfully');
      } else {
        await addProduct.mutateAsync(productData);
        toast.success('Product created successfully');
      }
  
      setFormData(initialFormState);
      setImageFiles([]);
      setSelectedProduct(null);
      const fileInput = document.querySelector('input[type="file"]');
      if (fileInput) fileInput.value = '';
  
    } catch (error) {
      toast.error(`Failed to save product: ${error.message}`);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-4 text-primarycolor">
      <input
        type="text"
        placeholder="Product Name"
        value={formData.name}
        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
        className="w-full p-2 border text-primarycolor"
        required
      />

      <textarea
        placeholder="Description"
        value={formData.description}
        onChange={(e) =>
          setFormData({ ...formData, description: e.target.value })
        }
        className="w-full p-2 border text-primarycolor"
        required
      />

      <select
        value={formData.category_id}
        onChange={(e) =>
          setFormData({ ...formData, category_id: e.target.value })
        }
        className="w-full p-2 border text-primarycolor"
        required
      >
        <option value="">Select Category</option>
        {categories?.map((cat) => (
          <option key={cat.id} value={cat.id}>
            {cat.name}
          </option>
        ))}
      </select>

      <div className="grid grid-cols-2 gap-4 text-primarycolor">
        <input
          type="number"
          placeholder="Price"
          value={formData.price}
          onChange={(e) => setFormData({ ...formData, price: e.target.value })}
          className="w-full p-2 border"
          required
        />

        <input
          type="number"
          placeholder="Stock Quantity"
          value={formData.quantity}
          onChange={(e) =>
            setFormData({ ...formData, quantity: e.target.value })
          }
          className="w-full p-2 border"
          required
        />

        <input
          type="number"
          placeholder="Discount %"
          value={formData.discount}
          onChange={(e) =>
            setFormData({ ...formData, discount: e.target.value })
          }
          className="w-full p-2 border"
          min="0"
          max="100"
          step="0.001" // This allows for 3 decimal places
        />
      </div>

      <div className="space-y-2">
        <label className="block text-sm font-medium text-primarycolor">
          Product Images
        </label>
        <input
          type="file"
          multiple
          accept="image/*"
          onChange={(e) => setImageFiles(Array.from(e.target.files))}
          className="w-full p-2 border"
          disabled={isUploading}
        />

        {/* Upload Progress */}
        {isUploading && (
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div
              className="bg-primarycolor h-2.5 rounded-full transition-all duration-300"
              style={{ width: `${uploadProgress}%` }}
            ></div>
          </div>
        )}

        {/* Image Preview Grid */}
        <div className="grid grid-cols-4 gap-2">
          {formData.image_url &&
            Array.isArray(formData.image_url) &&
            formData.image_url.map((url, index) => (
              <div key={index} className="relative group">
                <Image
                  src={url}
                  alt={`Preview ${index + 1}`}
                  width={80}
                  height={80}
                  className="object-contain rounded-lg"
                />
                <button
                  type="button"
                  onClick={() => handleImageDelete(index)}
                  className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </div>
            ))}
        </div>
      </div>
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            id="is_promoted"
            checked={formData.is_promoted}
            onChange={(e) =>
              setFormData({ ...formData, is_promoted: e.target.checked })
            }
            className="w-4 h-4"
          />
          <label htmlFor="is_promoted" className="text-primarycolor">
            Promote this product
          </label>
        </div>

        {formData.is_promoted && (
          <>
            <input
              type="datetime-local"
              placeholder="Promotion Start Date"
              value={formData.promotion_start_date}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  promotion_start_date: e.target.value,
                })
              }
              className="w-full p-2 border"
            />

            <input
              type="datetime-local"
              placeholder="Promotion End Date"
              value={formData.promotion_end_date}
              onChange={(e) =>
                setFormData({ ...formData, promotion_end_date: e.target.value })
              }
              className="w-full p-2 border"
            />

            <select
              value={formData.promotion_type}
              onChange={(e) =>
                setFormData({ ...formData, promotion_type: e.target.value })
              }
              className="w-full p-2 border"
            >
              <option value="">Select Promotion Type</option>
              <option value="flash_sale">Flash Sale</option>
              <option value="featured">Featured Product</option>
              <option value="clearance">Clearance</option>
            </select>
          </>
        )}
      </div>

      <button
        type="submit"
        disabled={addProduct.isLoading || updateProduct.isLoading}
        className="w-full bg-primarycolor text-white p-3 rounded disabled:opacity-50"
      >
        {addProduct.isLoading || updateProduct.isLoading
          ? "Saving..."
          : product
          ? "Update Product"
          : "Add Product"}
      </button>
    </form>
  );
}
