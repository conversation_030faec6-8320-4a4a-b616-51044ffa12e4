"use client";

import React from 'react';

export default function SimpleInput({ 
  type = 'text',
  placeholder,
  value,
  onChange,
  className = '',
  error,
  disabled = false,
  ...props 
}) {
  const baseClasses = 'block w-full px-3 py-2 border rounded-md placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';
  
  const stateClasses = error 
    ? 'border-red-500 text-red-900 focus:ring-red-500 focus:border-red-500'
    : 'border-gray-300 text-gray-900 focus:ring-primarycolor focus:border-primarycolor';

  const inputClasses = `${baseClasses} ${stateClasses} ${className}`;

  return (
    <input
      type={type}
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      className={inputClasses}
      disabled={disabled}
      {...props}
    />
  );
}

export function SimpleFormGroup({ 
  label, 
  error, 
  required, 
  children, 
  className = '' 
}) {
  return (
    <div className={`space-y-1 ${className}`}>
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      {children}
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  );
}
