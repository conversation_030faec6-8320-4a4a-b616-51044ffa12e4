import { Suspense } from "react";
import ProfileLayout from "../../components/profile/ProfileLayout";
import NotificationsPage from "../../components/profile/NotificationsPage";

export default function NotificationsPageRoute() {
  return (
    <Suspense fallback={<div>Loading notifications...</div>}>
      <ProfileLayout 
        title="Notifications" 
        subtitle="Manage your notification preferences and history"
      >
        <NotificationsPage />
      </ProfileLayout>
    </Suspense>
  );
}
