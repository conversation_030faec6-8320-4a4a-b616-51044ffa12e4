"use client"
import { useState } from 'react';
import { useAuth } from '../../hooks/useAuth';
import Link from 'next/link';
import { ChevronLeft } from 'lucide-react';

export default function ForgotPasswordPage() {
  const { resetPasswordForEmail } = useAuth();
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  const handleReset = async (e) => {
    e.preventDefault();
    try {
      await resetPasswordForEmail(email);
      setMessage('Reset email sent! Please check your inbox.');
      setError('');
    } catch (err) {
      setError('Failed to send reset email. Please try again.');
      setMessage('');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-purple-50 to-white flex flex-col items-center justify-center px-4">
      <div className="w-full max-w-md bg-white rounded-lg shadow-xl p-8">
        <Link href="/auth/login" className="text-primarycolor flex items-center mb-6">
          <ChevronLeft className="w-5 h-5 mr-2"/>
          Back to Login
        </Link>

        <h1 className="text-3xl font-bold text-primarycolor mb-2">Reset Password</h1>
        <p className="text-secondarycolor mb-8">Enter your email to receive a password reset link</p>

        <form onSubmit={handleReset} className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-primarycolor mb-1">Email Address</label>
            <input
              id="email"
              type="email"
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>

          <button 
            type="submit" 
            className="w-full bg-primarycolor text-white py-2 px-4 rounded-md hover:bg-primarycolor transition duration-200"
          >
            Send Reset Link
          </button>
        </form>

        {error && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 text-red-600 rounded-md">
            {error}
          </div>
        )}

        {message && (
          <div className="mt-4 p-3 bg-green-50 border border-green-200 text-green-600 rounded-md">
            {message}
          </div>
        )}

        <p className="mt-6 text-center text-gray-600">
          Remember your password?{' '}
          <Link href="/auth/login" className="text-primarycolor hover:text-purple-800 font-medium">
            Sign in
          </Link>
        </p>
      </div>
    </div>
  );
}
