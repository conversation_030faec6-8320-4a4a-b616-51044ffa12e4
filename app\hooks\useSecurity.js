import { useState, useCallback, useRef, useEffect } from 'react';
import { clientSecurity } from '../lib/security';

/**
 * Security hook for client-side security features
 */
export function useSecurity() {
  const [csrfToken, setCsrfToken] = useState(null);
  const [isRateLimited, setIsRateLimited] = useState(false);
  const requestCounts = useRef(new Map());

  // Generate CSRF token on mount
  useEffect(() => {
    const token = clientSecurity.generateFormToken();
    setCsrfToken(token);
  }, []);

  // Client-side rate limiting
  const checkRateLimit = useCallback((key, limit = 5, windowMs = 60000) => {
    const now = Date.now();
    const requests = requestCounts.current.get(key) || [];
    
    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < windowMs);
    
    if (validRequests.length >= limit) {
      setIsRateLimited(true);
      setTimeout(() => setIsRateLimited(false), windowMs);
      return false;
    }
    
    validRequests.push(now);
    requestCounts.current.set(key, validRequests);
    return true;
  }, []);

  // Secure fetch wrapper
  const secureFetch = useCallback(async (url, options = {}) => {
    const key = `${url}-${options.method || 'GET'}`;
    
    // Check client-side rate limiting
    if (!checkRateLimit(key)) {
      throw new Error('Rate limit exceeded. Please try again later.');
    }

    // Add security headers
    const secureOptions = {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        ...(csrfToken && { 'X-CSRF-Token': csrfToken }),
        ...options.headers
      }
    };

    // Sanitize request body
    if (secureOptions.body && typeof secureOptions.body === 'string') {
      try {
        const parsed = JSON.parse(secureOptions.body);
        const sanitized = sanitizeObject(parsed);
        secureOptions.body = JSON.stringify(sanitized);
      } catch (error) {
        // If not JSON, sanitize as string
        secureOptions.body = clientSecurity.sanitizeInput(secureOptions.body);
      }
    }

    const response = await fetch(url, secureOptions);
    
    // Handle rate limiting from server
    if (response.status === 429) {
      const retryAfter = response.headers.get('Retry-After');
      setIsRateLimited(true);
      setTimeout(() => setIsRateLimited(false), (retryAfter || 60) * 1000);
      throw new Error(`Rate limited. Try again in ${retryAfter || 60} seconds.`);
    }

    return response;
  }, [csrfToken, checkRateLimit, sanitizeObject]);

  // Sanitize object recursively
  const sanitizeObject = useCallback((obj) => {
    if (typeof obj !== 'object' || obj === null) {
      return clientSecurity.sanitizeInput(obj);
    }

    if (Array.isArray(obj)) {
      return obj.map(sanitizeObject);
    }

    const sanitized = {};
    for (const [key, value] of Object.entries(obj)) {
      sanitized[clientSecurity.sanitizeInput(key)] = sanitizeObject(value);
    }
    return sanitized;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    csrfToken,
    isRateLimited,
    secureFetch,
    sanitizeInput: clientSecurity.sanitizeInput,
    sanitizeObject,
    isValidEmail: clientSecurity.isValidEmail,
    isValidPhone: clientSecurity.isValidPhone,
    checkRateLimit
  };
}

/**
 * Hook for secure form submissions
 */
export function useSecureForm(onSubmit, options = {}) {
  const { secureFetch, sanitizeObject, isRateLimited } = useSecurity();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState(null);
  const [submitCount, setSubmitCount] = useState(0);
  const lastSubmitTime = useRef(0);

  const { 
    debounceMs = 1000,
    maxSubmitsPerMinute = 3,
    sanitizeData = true 
  } = options;

  const handleSubmit = useCallback(async (data, endpoint) => {
    // Prevent rapid submissions
    const now = Date.now();
    if (now - lastSubmitTime.current < debounceMs) {
      return;
    }

    // Check submission rate
    if (submitCount >= maxSubmitsPerMinute) {
      setSubmitError('Too many submissions. Please wait before trying again.');
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);
    lastSubmitTime.current = now;
    setSubmitCount(prev => prev + 1);

    try {
      // Sanitize data if enabled
      const processedData = sanitizeData ? sanitizeObject(data) : data;

      if (endpoint) {
        // Make API request
        const response = await secureFetch(endpoint, {
          method: 'POST',
          body: JSON.stringify(processedData)
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Submission failed');
        }

        const result = await response.json();
        await onSubmit(result);
      } else {
        // Direct callback
        await onSubmit(processedData);
      }
    } catch (error) {
      setSubmitError(error.message);
    } finally {
      setIsSubmitting(false);
    }
  }, [
    secureFetch, 
    sanitizeObject, 
    onSubmit, 
    debounceMs, 
    maxSubmitsPerMinute, 
    sanitizeData,
    submitCount
  ]);

  // Reset submit count every minute
  useEffect(() => {
    const interval = setInterval(() => {
      setSubmitCount(0);
    }, 60000);

    return () => clearInterval(interval);
  }, []);

  return {
    handleSubmit,
    isSubmitting: isSubmitting || isRateLimited,
    submitError,
    canSubmit: !isSubmitting && !isRateLimited && submitCount < maxSubmitsPerMinute,
    clearError: () => setSubmitError(null)
  };
}

/**
 * Hook for input validation with security
 */
export function useSecureInput(initialValue = '', options = {}) {
  const [value, setValue] = useState(initialValue);
  const [error, setError] = useState(null);
  const { sanitizeInput } = useSecurity();

  const { 
    maxLength = 1000,
    validator = null,
    sanitize = true 
  } = options;

  const updateValue = useCallback((newValue) => {
    // Sanitize input
    const processedValue = sanitize ? sanitizeInput(newValue) : newValue;
    
    // Check length
    if (processedValue.length > maxLength) {
      setError(`Input too long. Maximum ${maxLength} characters allowed.`);
      return;
    }

    // Custom validation
    if (validator) {
      const validationError = validator(processedValue);
      if (validationError) {
        setError(validationError);
        return;
      }
    }

    setError(null);
    setValue(processedValue);
  }, [sanitizeInput, maxLength, validator, sanitize]);

  return {
    value,
    setValue: updateValue,
    error,
    isValid: !error && value.length > 0,
    clearError: () => setError(null)
  };
}

/**
 * Hook for monitoring suspicious activity
 */
export function useSecurityMonitor() {
  const [suspiciousActivity, setSuspiciousActivity] = useState([]);
  const activityLog = useRef([]);

  const checkSuspiciousActivity = useCallback((activity) => {
    const recentActivities = activityLog.current.filter(
      a => Date.now() - a.timestamp < 60000 // Last minute
    );

    // Check for rapid repeated actions
    const sameTypeCount = recentActivities.filter(a => a.type === activity.type).length;
    if (sameTypeCount > 10) {
      setSuspiciousActivity(prev => [...prev, {
        type: 'rapid_actions',
        message: `Rapid ${activity.type} actions detected`,
        timestamp: Date.now()
      }]);
    }

    // Check for unusual patterns
    if (activity.type === 'failed_login' && sameTypeCount > 3) {
      setSuspiciousActivity(prev => [...prev, {
        type: 'brute_force',
        message: 'Multiple failed login attempts',
        timestamp: Date.now()
      }]);
    }
  }, []);

  const logActivity = useCallback((type, details) => {
    const activity = {
      type,
      details,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    activityLog.current.push(activity);

    // Keep only last 100 activities
    if (activityLog.current.length > 100) {
      activityLog.current = activityLog.current.slice(-100);
    }

    // Check for suspicious patterns
    checkSuspiciousActivity(activity);
  }, [checkSuspiciousActivity]);

  return {
    logActivity,
    suspiciousActivity,
    clearSuspiciousActivity: () => setSuspiciousActivity([])
  };
}
