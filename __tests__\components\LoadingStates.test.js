import { render, screen, fireEvent } from '../utils/test-utils'
import {
  Skeleton,
  ProductCardSkeleton,
  ProductGridSkeleton,
  LoadingSpinner,
  ButtonLoading,
  LazyLoadWrapper,
} from '../../app/components/LoadingStates'

describe('LoadingStates Components', () => {
  describe('Skeleton', () => {
    it('renders with default classes', () => {
      render(<Skeleton data-testid="skeleton" />)
      
      const skeleton = screen.getByTestId('skeleton')
      expect(skeleton).toHaveClass('animate-pulse', 'bg-gray-200', 'rounded', 'h-4', 'w-full')
    })

    it('applies custom className', () => {
      render(<Skeleton className="custom-class" data-testid="skeleton" />)
      
      const skeleton = screen.getByTestId('skeleton')
      expect(skeleton).toHaveClass('custom-class')
    })

    it('applies rounded class when rounded prop is true', () => {
      render(<Skeleton rounded data-testid="skeleton" />)
      
      const skeleton = screen.getByTestId('skeleton')
      expect(skeleton).toHaveClass('rounded-full')
    })

    it('applies custom width and height', () => {
      render(<Skeleton width="100px" height="50px" data-testid="skeleton" />)
      
      const skeleton = screen.getByTestId('skeleton')
      expect(skeleton).toHaveStyle({
        width: '100px',
        height: '50px',
      })
    })
  })

  describe('ProductCardSkeleton', () => {
    it('renders product card skeleton structure', () => {
      render(<ProductCardSkeleton />)
      
      // Should have container with proper classes
      const container = screen.getByRole('generic')
      expect(container).toHaveClass('bg-white', 'border', 'rounded-lg', 'p-4', 'space-y-4')
    })
  })

  describe('ProductGridSkeleton', () => {
    it('renders default number of skeleton cards', () => {
      render(<ProductGridSkeleton />)
      
      const container = screen.getByRole('generic')
      expect(container).toHaveClass('grid', 'grid-cols-2', 'md:grid-cols-3', 'lg:grid-cols-4', 'gap-4')
    })

    it('renders custom number of skeleton cards', () => {
      render(<ProductGridSkeleton count={4} />)
      
      // This is a bit tricky to test without counting actual elements
      // We can test that the component renders without errors
      const container = screen.getByRole('generic')
      expect(container).toBeInTheDocument()
    })
  })

  describe('LoadingSpinner', () => {
    it('renders with default size and color', () => {
      render(<LoadingSpinner data-testid="spinner" />)
      
      const spinner = screen.getByTestId('spinner')
      expect(spinner).toHaveClass('animate-spin', 'rounded-full', 'border-2', 'w-8', 'h-8')
    })

    it('applies different sizes', () => {
      render(<LoadingSpinner size="lg" data-testid="spinner" />)
      
      const spinner = screen.getByTestId('spinner')
      expect(spinner).toHaveClass('w-12', 'h-12')
    })

    it('applies custom color', () => {
      render(<LoadingSpinner color="red-500" data-testid="spinner" />)
      
      const spinner = screen.getByTestId('spinner')
      expect(spinner).toHaveClass('border-t-red-500')
    })
  })

  describe('ButtonLoading', () => {
    it('renders children when not loading', () => {
      render(
        <ButtonLoading isLoading={false}>
          Click me
        </ButtonLoading>
      )
      
      expect(screen.getByText('Click me')).toBeInTheDocument()
      expect(screen.queryByText('Loading...')).not.toBeInTheDocument()
    })

    it('shows loading state when isLoading is true', () => {
      render(
        <ButtonLoading isLoading={true}>
          Click me
        </ButtonLoading>
      )
      
      expect(screen.queryByText('Click me')).not.toBeInTheDocument()
      expect(screen.getByText('Loading...')).toBeInTheDocument()
    })

    it('shows custom loading text', () => {
      render(
        <ButtonLoading isLoading={true} loadingText="Processing...">
          Click me
        </ButtonLoading>
      )
      
      expect(screen.getByText('Processing...')).toBeInTheDocument()
    })

    it('disables button when loading', () => {
      render(
        <ButtonLoading isLoading={true}>
          Click me
        </ButtonLoading>
      )
      
      const button = screen.getByRole('button')
      expect(button).toBeDisabled()
    })

    it('disables button when disabled prop is true', () => {
      render(
        <ButtonLoading isLoading={false} disabled={true}>
          Click me
        </ButtonLoading>
      )
      
      const button = screen.getByRole('button')
      expect(button).toBeDisabled()
    })

    it('passes through other props', () => {
      const handleClick = jest.fn()
      
      render(
        <ButtonLoading isLoading={false} onClick={handleClick} className="custom-class">
          Click me
        </ButtonLoading>
      )
      
      const button = screen.getByRole('button')
      expect(button).toHaveClass('custom-class')
      
      fireEvent.click(button)
      expect(handleClick).toHaveBeenCalled()
    })
  })

  describe('LazyLoadWrapper', () => {
    it('renders children when not loading and no error', () => {
      render(
        <LazyLoadWrapper isLoading={false} error={null}>
          <div>Content loaded</div>
        </LazyLoadWrapper>
      )
      
      expect(screen.getByText('Content loaded')).toBeInTheDocument()
    })

    it('renders skeleton when loading', () => {
      const MockSkeleton = () => <div data-testid="mock-skeleton">Loading skeleton</div>
      
      render(
        <LazyLoadWrapper isLoading={true} skeleton={<MockSkeleton />}>
          <div>Content loaded</div>
        </LazyLoadWrapper>
      )
      
      expect(screen.getByTestId('mock-skeleton')).toBeInTheDocument()
      expect(screen.queryByText('Content loaded')).not.toBeInTheDocument()
    })

    it('renders default loading spinner when loading and no skeleton provided', () => {
      render(
        <LazyLoadWrapper isLoading={true}>
          <div>Content loaded</div>
        </LazyLoadWrapper>
      )
      
      // Should render the default LoadingSpinner
      expect(screen.queryByText('Content loaded')).not.toBeInTheDocument()
    })

    it('renders error state when error is present', () => {
      render(
        <LazyLoadWrapper isLoading={false} error={new Error('Test error')}>
          <div>Content loaded</div>
        </LazyLoadWrapper>
      )
      
      expect(screen.getByText('Error loading content')).toBeInTheDocument()
      expect(screen.getByText('Try again')).toBeInTheDocument()
      expect(screen.queryByText('Content loaded')).not.toBeInTheDocument()
    })

    it('handles try again button click', () => {
      // Mock window.location.reload
      const mockReload = jest.fn()
      Object.defineProperty(window, 'location', {
        value: { reload: mockReload },
        writable: true,
      })
      
      render(
        <LazyLoadWrapper isLoading={false} error={new Error('Test error')}>
          <div>Content loaded</div>
        </LazyLoadWrapper>
      )
      
      const tryAgainButton = screen.getByText('Try again')
      fireEvent.click(tryAgainButton)
      
      expect(mockReload).toHaveBeenCalled()
    })

    it('prioritizes error over loading state', () => {
      render(
        <LazyLoadWrapper isLoading={true} error={new Error('Test error')}>
          <div>Content loaded</div>
        </LazyLoadWrapper>
      )
      
      expect(screen.getByText('Error loading content')).toBeInTheDocument()
      expect(screen.queryByText('Content loaded')).not.toBeInTheDocument()
    })
  })
})
