"use client";

import { useState, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";
import { useCart } from "../context/cartContext";
import { useAuth } from "../hooks/useAuth";
import { useUserAddresses } from "../hooks/useProfile";
import { useProducts } from "../hooks/useQueries";
import { useDeliveryCost, useAvailableCounties } from "../hooks/useDelivery";
import { 
  ChevronLeft, 
  Package, 
  CreditCard, 
  CheckCircle, 
  Truck, 
  MapPin, 
  Phone, 
  Mail, 
  User, 
  ArrowRight, 
  Shield,
  Clock
} from "lucide-react";
import { toast } from "sonner";
import { toTitleCase } from "../lib/utils";
import AddressSelector from "../components/checkout/AddressSelector";
import EnhancedOrderSummary from "../components/checkout/EnhancedOrderSummary";

const CHECKOUT_STEPS = [
  { id: 'address', title: 'Delivery Address', icon: MapPin },
  { id: 'payment', title: 'Payment Method', icon: CreditCard },
  { id: 'review', title: 'Review Order', icon: Package },
];

export default function CheckoutPage() {
  const router = useRouter();
  const { user } = useAuth();
  const { cartItems, clearCart } = useCart();
  const { data: products = [] } = useProducts();
  const { data: userAddresses = [] } = useUserAddresses();
  const { data: availableCounties = [] } = useAvailableCounties();

  // State management
  const [currentStep, setCurrentStep] = useState('address');
  const [selectedAddress, setSelectedAddress] = useState(null);
  const [guestFormData, setGuestFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    county: '',
    city: '',
    streetAddress: '',
    postalCode: ''
  });
  const [paymentMethod, setPaymentMethod] = useState('mpesa');
  const [isProcessing, setIsProcessing] = useState(false);

  // Get cart with product details
  const cartWithDetails = useMemo(() => {
    return cartItems.map(item => ({
      ...item,
      product: products.find(p => p.id === item.productId)
    })).filter(item => item.product);
  }, [cartItems, products]);

  // Auto-select default address for logged-in users
  useEffect(() => {
    if (user && userAddresses.length > 0 && !selectedAddress) {
      const defaultAddress = userAddresses.find(addr => addr.is_default) || userAddresses[0];
      setSelectedAddress(defaultAddress);
    }
  }, [user, userAddresses, selectedAddress]);

  // Get delivery information
  const deliveryAddress = user ? selectedAddress : (guestFormData.county ? guestFormData : null);
  const { data: deliveryInfo } = useDeliveryCost(deliveryAddress?.county);

  // Calculate totals
  const subtotal = useMemo(() => {
    return cartWithDetails.reduce((sum, item) => {
      return sum + (Number(item.product?.price || 0) * Number(item.quantity || 0));
    }, 0);
  }, [cartWithDetails]);

  const deliveryCost = deliveryInfo?.cost || 0;
  const total = subtotal + deliveryCost;

  // Redirect if cart is empty
  useEffect(() => {
    if (cartItems.length === 0) {
      router.push('/cart');
    }
  }, [cartItems.length, router]);

  const handleStepChange = (stepId) => {
    // Validation logic for each step
    if (stepId === 'payment' && !deliveryAddress) {
      toast.error('Please select a delivery address first');
      return;
    }
    if (stepId === 'review' && !paymentMethod) {
      toast.error('Please select a payment method first');
      return;
    }
    setCurrentStep(stepId);
  };

  const handlePlaceOrder = async () => {
    setIsProcessing(true);
    try {
      // Order placement logic here
      toast.success('Order placed successfully!');
      clearCart();
      router.push('/orders');
    } catch (error) {
      toast.error('Failed to place order. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  if (cartItems.length === 0) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-3 sm:py-4">
            <button
              onClick={() => router.back()}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ChevronLeft size={18} className="sm:w-5 sm:h-5" />
              <span className="text-xs sm:text-sm font-medium">Back</span>
            </button>
            <h1 className="text-lg sm:text-xl font-bold text-gray-900">
              Secure Checkout
            </h1>
            <div className="flex items-center gap-1 text-green-600">
              <Shield className="w-4 h-4" />
              <span className="text-xs font-medium hidden sm:inline">Secure</span>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-8">
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {CHECKOUT_STEPS.map((step, index) => (
              <StepIndicator
                key={step.id}
                step={step}
                isActive={currentStep === step.id}
                isCompleted={CHECKOUT_STEPS.findIndex(s => s.id === currentStep) > index}
                onClick={() => handleStepChange(step.id)}
              />
            ))}
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {currentStep === 'address' && (
              <AddressStep
                user={user}
                userAddresses={userAddresses}
                selectedAddress={selectedAddress}
                onAddressSelect={setSelectedAddress}
                guestFormData={guestFormData}
                onGuestFormChange={setGuestFormData}
                availableCounties={availableCounties}
                onNext={() => handleStepChange('payment')}
              />
            )}

            {currentStep === 'payment' && (
              <PaymentStep
                paymentMethod={paymentMethod}
                onPaymentMethodChange={setPaymentMethod}
                onNext={() => handleStepChange('review')}
                onBack={() => handleStepChange('address')}
              />
            )}

            {currentStep === 'review' && (
              <ReviewStep
                cartWithDetails={cartWithDetails}
                deliveryAddress={deliveryAddress}
                paymentMethod={paymentMethod}
                total={total}
                deliveryCost={deliveryCost}
                subtotal={subtotal}
                onPlaceOrder={handlePlaceOrder}
                onBack={() => handleStepChange('payment')}
                isProcessing={isProcessing}
              />
            )}
          </div>

          {/* Order Summary Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-24">
              <EnhancedOrderSummary
                cartItems={cartWithDetails}
                deliveryAddress={deliveryAddress}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function StepIndicator({ step, isActive, isCompleted, onClick }) {
  const Icon = step.icon;

  return (
    <button
      onClick={onClick}
      className={`flex flex-col items-center gap-2 p-2 rounded-lg transition-colors ${
        isActive
          ? 'text-primarycolor'
          : isCompleted
            ? 'text-green-600'
            : 'text-gray-400'
      }`}
    >
      <div className={`w-10 h-10 rounded-full flex items-center justify-center border-2 transition-colors ${
        isActive
          ? 'border-primarycolor bg-primarycolor/10'
          : isCompleted
            ? 'border-green-600 bg-green-50'
            : 'border-gray-300 bg-gray-50'
      }`}>
        {isCompleted ? (
          <CheckCircle className="w-5 h-5" />
        ) : (
          <Icon className="w-5 h-5" />
        )}
      </div>
      <span className="text-xs font-medium text-center">{step.title}</span>
    </button>
  );
}

function AddressStep({
  user,
  userAddresses,
  selectedAddress,
  onAddressSelect,
  guestFormData,
  onGuestFormChange,
  availableCounties,
  onNext
}) {
  const isValid = user ? selectedAddress : (
    guestFormData.fullName &&
    guestFormData.email &&
    guestFormData.phone &&
    guestFormData.county &&
    guestFormData.streetAddress
  );

  return (
    <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
      {user ? (
        <AddressSelector
          userAddresses={userAddresses}
          selectedAddress={selectedAddress}
          onAddressSelect={onAddressSelect}
          user={user}
        />
      ) : (
        <GuestAddressForm
          formData={guestFormData}
          onFormChange={onGuestFormChange}
          availableCounties={availableCounties}
        />
      )}

      <div className="flex justify-end mt-6 pt-6 border-t border-gray-100">
        <button
          onClick={onNext}
          disabled={!isValid}
          className={`flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-colors ${
            isValid
              ? 'bg-primarycolor text-white hover:bg-primarycolor-700'
              : 'bg-gray-100 text-gray-400 cursor-not-allowed'
          }`}
        >
          Continue to Payment
          <ArrowRight className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
}

function GuestAddressForm({ formData, onFormChange, availableCounties }) {
  const handleChange = (field, value) => {
    onFormChange(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3 mb-6">
        <User className="w-6 h-6 text-primarycolor" />
        <h3 className="text-lg font-semibold text-gray-900">Delivery Information</h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Full Name *
          </label>
          <input
            type="text"
            value={formData.fullName}
            onChange={(e) => handleChange('fullName', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primarycolor focus:border-transparent"
            placeholder="Enter your full name"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Phone Number *
          </label>
          <input
            type="tel"
            value={formData.phone}
            onChange={(e) => handleChange('phone', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primarycolor focus:border-transparent"
            placeholder="0712345678"
          />
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Email Address *
          </label>
          <input
            type="email"
            value={formData.email}
            onChange={(e) => handleChange('email', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primarycolor focus:border-transparent"
            placeholder="<EMAIL>"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            County *
          </label>
          <select
            value={formData.county}
            onChange={(e) => handleChange('county', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primarycolor focus:border-transparent"
          >
            <option value="">Select County</option>
            {availableCounties.map(county => (
              <option key={county} value={county}>{county}</option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            City/Town
          </label>
          <input
            type="text"
            value={formData.city}
            onChange={(e) => handleChange('city', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primarycolor focus:border-transparent"
            placeholder="Enter city or town"
          />
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Street Address *
          </label>
          <textarea
            value={formData.streetAddress}
            onChange={(e) => handleChange('streetAddress', e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primarycolor focus:border-transparent"
            placeholder="Enter detailed street address, building name, apartment number, etc."
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Postal Code
          </label>
          <input
            type="text"
            value={formData.postalCode}
            onChange={(e) => handleChange('postalCode', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primarycolor focus:border-transparent"
            placeholder="00100"
          />
        </div>
      </div>
    </div>
  );
}

function PaymentStep({ paymentMethod, onPaymentMethodChange, onNext, onBack }) {
  const paymentMethods = [
    {
      id: 'mpesa',
      name: 'M-Pesa',
      description: 'Pay with M-Pesa mobile money',
      icon: '📱',
      popular: true
    },
    {
      id: 'card',
      name: 'Credit/Debit Card',
      description: 'Visa, Mastercard, American Express',
      icon: '💳'
    },
    {
      id: 'bank',
      name: 'Bank Transfer',
      description: 'Direct bank transfer',
      icon: '🏦'
    }
  ];

  return (
    <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
      <div className="flex items-center gap-3 mb-6">
        <CreditCard className="w-6 h-6 text-primarycolor" />
        <h3 className="text-lg font-semibold text-gray-900">Payment Method</h3>
      </div>

      <div className="space-y-3 mb-6">
        {paymentMethods.map((method) => (
          <div
            key={method.id}
            onClick={() => onPaymentMethodChange(method.id)}
            className={`relative p-4 border-2 rounded-xl cursor-pointer transition-all ${
              paymentMethod === method.id
                ? 'border-primarycolor bg-primarycolor/5'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center gap-4">
              <span className="text-2xl">{method.icon}</span>
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <h4 className="font-medium text-gray-900">{method.name}</h4>
                  {method.popular && (
                    <span className="px-2 py-0.5 text-xs font-medium bg-primarycolor text-white rounded-full">
                      Popular
                    </span>
                  )}
                </div>
                <p className="text-sm text-gray-600">{method.description}</p>
              </div>
              <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                paymentMethod === method.id
                  ? 'border-primarycolor bg-primarycolor'
                  : 'border-gray-300'
              }`}>
                {paymentMethod === method.id && (
                  <div className="w-2 h-2 bg-white rounded-full" />
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="flex justify-between pt-6 border-t border-gray-100">
        <button
          onClick={onBack}
          className="flex items-center gap-2 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          <ChevronLeft className="w-4 h-4" />
          Back
        </button>
        <button
          onClick={onNext}
          disabled={!paymentMethod}
          className={`flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-colors ${
            paymentMethod
              ? 'bg-primarycolor text-white hover:bg-primarycolor-700'
              : 'bg-gray-100 text-gray-400 cursor-not-allowed'
          }`}
        >
          Review Order
          <ArrowRight className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
}

function ReviewStep({
  cartWithDetails,
  deliveryAddress,
  paymentMethod,
  total,
  deliveryCost,
  subtotal,
  onPlaceOrder,
  onBack,
  isProcessing
}) {
  const paymentMethodNames = {
    mpesa: 'M-Pesa',
    card: 'Credit/Debit Card',
    bank: 'Bank Transfer'
  };

  return (
    <div className="space-y-6">
      {/* Order Items */}
      <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Items</h3>
        <div className="space-y-4">
          {cartWithDetails.map((item) => (
            <div key={item.productId} className="flex gap-4 p-4 bg-gray-50 rounded-lg">
              <div className="w-16 h-16 bg-white rounded-lg overflow-hidden">
                <img
                  src={Array.isArray(item.product?.image_url) ? item.product.image_url[0] : item.product?.image_url}
                  alt={item.product?.name}
                  className="w-full h-full object-contain p-1"
                />
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-gray-900">
                  {toTitleCase(item.product?.name || 'Unknown Product')}
                </h4>
                <p className="text-sm text-gray-600">Quantity: {item.quantity}</p>
                <p className="font-semibold text-primarycolor">
                  Ksh. {Math.round(Number(item.product?.price || 0) * Number(item.quantity || 0)).toLocaleString()}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Delivery & Payment Summary */}
      <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>

        <div className="space-y-4">
          <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
            <MapPin className="w-5 h-5 text-primarycolor mt-0.5" />
            <div>
              <h4 className="font-medium text-gray-900">Delivery Address</h4>
              <p className="text-sm text-gray-600">
                {deliveryAddress?.full_name || deliveryAddress?.fullName}
              </p>
              <p className="text-sm text-gray-600">
                {deliveryAddress?.street_address || deliveryAddress?.streetAddress}
                {deliveryAddress?.city && `, ${deliveryAddress.city}`}
                {deliveryAddress?.county && `, ${deliveryAddress.county}`}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
            <CreditCard className="w-5 h-5 text-primarycolor" />
            <div>
              <h4 className="font-medium text-gray-900">Payment Method</h4>
              <p className="text-sm text-gray-600">{paymentMethodNames[paymentMethod]}</p>
            </div>
          </div>

          <div className="border-t border-gray-200 pt-4 space-y-2">
            <div className="flex justify-between text-gray-600">
              <span>Subtotal</span>
              <span>Ksh. {Math.round(subtotal).toLocaleString()}</span>
            </div>
            <div className="flex justify-between text-gray-600">
              <span>Delivery</span>
              <span>Ksh. {Math.round(deliveryCost).toLocaleString()}</span>
            </div>
            <div className="flex justify-between text-lg font-semibold text-gray-900 pt-2 border-t border-gray-200">
              <span>Total</span>
              <span>Ksh. {Math.round(total).toLocaleString()}</span>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-between">
        <button
          onClick={onBack}
          className="flex items-center gap-2 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          <ChevronLeft className="w-4 h-4" />
          Back
        </button>
        <button
          onClick={onPlaceOrder}
          disabled={isProcessing}
          className="flex items-center gap-2 px-8 py-3 bg-primarycolor text-white rounded-lg hover:bg-primarycolor-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isProcessing ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              Processing...
            </>
          ) : (
            <>
              <CheckCircle className="w-4 h-4" />
              Place Order
            </>
          )}
        </button>
      </div>
    </div>
  );
}
