import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Line } from 'react-chartjs-2';
import { useSupabase } from '../hooks/useSupabase';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

export default function SalesGraph() {
  const { useDashboardData } = useSupabase();
  const { data: dashboardData, isLoading } = useDashboardData();

  if (isLoading) return <div>Loading chart...</div>;

  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Sales Revenue Over Time',
      },
    },
    scales: {
      x: {
        type: 'category',
      },
      y: {
        type: 'linear',
      },
    },
  };

  const data = {
    labels: dashboardData?.salesData?.map((entry) => entry.date) || [],
    datasets: [
      {
        label: 'Confirmed Sales',
        data: dashboardData?.salesData?.map((entry) => entry.confirmed_sales) || [],
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        borderColor: 'rgba(75, 192, 192, 1)',
        borderWidth: 1,
      },
      {
        label: 'Pending Sales',
        data: dashboardData?.salesData?.map((entry) => entry.pending_sales) || [],
        backgroundColor: 'rgba(255, 159, 64, 0.2)',
        borderColor: 'rgba(255, 159, 64, 1)',
        borderWidth: 1,
      }
    ],
  };
  
  return <Line options={options} data={data} />;
}
