import React from 'react'
import { render } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { CartProvider } from '../../app/context/cartContext'
import { WishlistProvider } from '../../app/context/wishlistContext'
import { SupabaseProvider } from '../../app/context/supabaseContext'

// Mock providers for testing
const MockAuthProvider = ({ children, user = null, isAuthenticated = false }) => {
  const mockAuthContext = {
    user,
    isAuthenticated,
    userDetails: user ? { id: user.id, name: user.name, email: user.email, role: 'user' } : null,
    signIn: jest.fn(),
    signOut: jest.fn(),
    signUp: jest.fn(),
    fetchUserDetails: jest.fn(),
  }

  return React.createElement(
    'div',
    { 'data-testid': 'mock-auth-provider' },
    children
  )
}

// Create a custom render function that includes providers
const customRender = (ui, options = {}) => {
  const {
    user = null,
    isAuthenticated = false,
    initialCartItems = [],
    initialWishlistItems = [],
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    }),
    ...renderOptions
  } = options

  const Wrapper = ({ children }) => {
    return (
      <QueryClientProvider client={queryClient}>
        <MockAuthProvider user={user} isAuthenticated={isAuthenticated}>
          <SupabaseProvider>
            <WishlistProvider>
              <CartProvider>
                {children}
              </CartProvider>
            </WishlistProvider>
          </SupabaseProvider>
        </MockAuthProvider>
      </QueryClientProvider>
    )
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

// Mock data generators
export const mockUser = {
  id: 'user-123',
  name: 'Test User',
  email: '<EMAIL>',
  role: 'user',
}

export const mockAdmin = {
  id: 'admin-123',
  name: 'Admin User',
  email: '<EMAIL>',
  role: 'admin',
}

export const mockProduct = {
  id: 'product-123',
  name: 'Test Product',
  description: 'This is a test product',
  price: 1000,
  discount: 10,
  quantity: 50,
  category_id: 'category-123',
  category_name: 'Test Category',
  image_url: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
  is_promoted: false,
  created_at: '2024-01-01T00:00:00Z',
}

export const mockPromotedProduct = {
  ...mockProduct,
  id: 'promoted-product-123',
  name: 'Promoted Product',
  is_promoted: true,
  promotion_start_date: '2024-01-01T00:00:00Z',
  promotion_end_date: '2024-12-31T23:59:59Z',
  promotion_type: 'percentage',
}

export const mockCategory = {
  id: 'category-123',
  name: 'Test Category',
  description: 'This is a test category',
  image_url: 'https://example.com/category.jpg',
  slug: 'test-category',
}

export const mockCartItem = {
  id: 'cart-item-123',
  user_id: 'user-123',
  product_id: 'product-123',
  quantity: 2,
  price_at_time: 1000,
  created_at: '2024-01-01T00:00:00Z',
  products: mockProduct,
}

export const mockWishlistItem = {
  id: 'wishlist-item-123',
  user_id: 'user-123',
  product_id: 'product-123',
  created_at: '2024-01-01T00:00:00Z',
  products: mockProduct,
}

export const mockOrder = {
  id: 'order-123',
  user_id: 'user-123',
  status: 'PENDING',
  total_amount: 2000,
  delivery_cost: 200,
  mpesa_code: 'ABC123DEF456',
  billing_details: {
    full_name: 'Test User',
    email: '<EMAIL>',
    phone: '+254712345678',
    county: 'Nairobi',
    street: '123 Test Street',
  },
  items: [
    {
      product_id: 'product-123',
      product_name: 'Test Product',
      quantity: 2,
      price: 1000,
    },
  ],
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
}

// Mock API responses
export const mockSupabaseResponse = (data, error = null) => ({
  data,
  error,
})

export const mockSupabaseQuery = (data, error = null) => {
  const mockQuery = {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    not: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lte: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({ data, error }),
    upsert: jest.fn().mockReturnThis(),
  }

  // Make the chain methods return the final promise
  Object.keys(mockQuery).forEach(key => {
    if (key !== 'single' && typeof mockQuery[key] === 'function') {
      mockQuery[key].mockImplementation(() => {
        return {
          ...mockQuery,
          then: (callback) => callback({ data, error }),
        }
      })
    }
  })

  return mockQuery
}

// Test helpers
export const waitForLoadingToFinish = () => {
  return new Promise(resolve => setTimeout(resolve, 0))
}

export const mockIntersectionObserver = () => {
  const mockIntersectionObserver = jest.fn()
  mockIntersectionObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null,
  })
  window.IntersectionObserver = mockIntersectionObserver
}

export const mockLocalStorage = () => {
  const store = {}
  return {
    getItem: jest.fn(key => store[key] || null),
    setItem: jest.fn((key, value) => {
      store[key] = value.toString()
    }),
    removeItem: jest.fn(key => {
      delete store[key]
    }),
    clear: jest.fn(() => {
      Object.keys(store).forEach(key => delete store[key])
    }),
  }
}

// Custom matchers
expect.extend({
  toBeInTheDocument(received) {
    const pass = received !== null && received !== undefined
    return {
      message: () => `expected element ${pass ? 'not ' : ''}to be in the document`,
      pass,
    }
  },
})

// Export everything including the custom render
export * from '@testing-library/react'
export { customRender as render }
