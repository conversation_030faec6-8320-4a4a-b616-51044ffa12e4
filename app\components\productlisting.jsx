"use client";

import React, { useState, useMemo } from 'react';
import { Grid, List, Filter, SortAsc, SortDesc, Search } from 'lucide-react';
import EnhancedProductCard from './EnhancedProductCard';
import { ProductGridSkeleton, LazyLoadWrapper } from './LoadingStates';
import Card from './ui/Card';
import Button, { IconButton } from './ui/Button';
import { SearchInput, Select } from './ui/Input';
import { cn } from '../lib/utils';

export default function ProductListing({
  products = [],
  categoryId = null,
  isLoading = false,
  error = null,
  emptyMessage = "No products found",
  showFilters = true,
  showSearch = true,
  showViewToggle = true,
  defaultViewMode = 'grid',
  itemsPerPage = 20,
  className
}) {
  const [viewMode, setViewMode] = useState(defaultViewMode);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState('asc');
  const [priceRange, setPriceRange] = useState({ min: '', max: '' });
  const [currentPage, setCurrentPage] = useState(1);

  // Filter and sort products
  const filteredAndSortedProducts = useMemo(() => {
    let filtered = categoryId
      ? products.filter(product => product.category_id === categoryId)
      : [...products];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply price range filter
    if (priceRange.min || priceRange.max) {
      filtered = filtered.filter(product => {
        const price = Number(product.price);
        const min = priceRange.min ? Number(priceRange.min) : 0;
        const max = priceRange.max ? Number(priceRange.max) : Infinity;
        return price >= min && price <= max;
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'price':
          aValue = Number(a.price);
          bValue = Number(b.price);
          break;
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'created_at':
          aValue = new Date(a.created_at || 0);
          bValue = new Date(b.created_at || 0);
          break;
        case 'rating':
          aValue = a.rating || 0;
          bValue = b.rating || 0;
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [products, categoryId, searchTerm, sortBy, sortOrder, priceRange]);

  // Pagination
  const totalPages = Math.ceil(filteredAndSortedProducts.length / itemsPerPage);
  const paginatedProducts = filteredAndSortedProducts.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handleSearch = (query) => {
    setSearchTerm(query);
    setCurrentPage(1);
  };

  const handleSortChange = (newSortBy) => {
    if (sortBy === newSortBy) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(newSortBy);
      setSortOrder('asc');
    }
    setCurrentPage(1);
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSortBy('name');
    setSortOrder('asc');
    setPriceRange({ min: '', max: '' });
    setCurrentPage(1);
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Filters and Controls */}
      {(showFilters || showSearch || showViewToggle) && (
        <Card className="p-4">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
            {/* Search */}
            {showSearch && (
              <div className="flex-1 max-w-md">
                <SearchInput
                  placeholder="Search products..."
                  onSearch={handleSearch}
                  className="w-full"
                />
              </div>
            )}

            {/* Filters and Controls */}
            <div className="flex flex-wrap items-center gap-3">
              {/* Price Range Filter */}
              {showFilters && (
                <div className="flex items-center gap-2">
                  <input
                    type="number"
                    placeholder="Min price"
                    value={priceRange.min}
                    onChange={(e) => setPriceRange(prev => ({ ...prev, min: e.target.value }))}
                    className="w-24 px-2 py-1 border border-gray-300 rounded text-sm"
                  />
                  <span className="text-gray-500">-</span>
                  <input
                    type="number"
                    placeholder="Max price"
                    value={priceRange.max}
                    onChange={(e) => setPriceRange(prev => ({ ...prev, max: e.target.value }))}
                    className="w-24 px-2 py-1 border border-gray-300 rounded text-sm"
                  />
                </div>
              )}

              {/* Sort */}
              <div className="flex items-center gap-2">
                <Select
                  value={sortBy}
                  onChange={(e) => handleSortChange(e.target.value)}
                  className="w-32"
                >
                  <option value="name">Name</option>
                  <option value="price">Price</option>
                  <option value="created_at">Newest</option>
                  <option value="rating">Rating</option>
                </Select>

                <IconButton
                  icon={sortOrder === 'asc' ? <SortAsc className="w-4 h-4" /> : <SortDesc className="w-4 h-4" />}
                  variant="outline"
                  size="sm"
                  onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                  aria-label={`Sort ${sortOrder === 'asc' ? 'descending' : 'ascending'}`}
                />
              </div>

              {/* View Mode Toggle */}
              {showViewToggle && (
                <div className="flex border border-gray-300 rounded-lg overflow-hidden">
                  <button
                    className={cn(
                      "p-2 transition-colors",
                      viewMode === 'grid'
                        ? "bg-primarycolor text-white"
                        : "bg-white text-gray-600 hover:bg-gray-50"
                    )}
                    onClick={() => setViewMode('grid')}
                  >
                    <Grid className="w-4 h-4" />
                  </button>
                  <button
                    className={cn(
                      "p-2 transition-colors",
                      viewMode === 'list'
                        ? "bg-primarycolor text-white"
                        : "bg-white text-gray-600 hover:bg-gray-50"
                    )}
                    onClick={() => setViewMode('list')}
                  >
                    <List className="w-4 h-4" />
                  </button>
                </div>
              )}

              {/* Clear Filters */}
              {(searchTerm || priceRange.min || priceRange.max || sortBy !== 'name') && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFilters}
                  className="text-gray-600"
                >
                  Clear Filters
                </Button>
              )}
            </div>
          </div>

          {/* Results Info */}
          <div className="mt-4 pt-4 border-t border-gray-200 flex items-center justify-between text-sm text-gray-600">
            <span>
              Showing {paginatedProducts.length} of {filteredAndSortedProducts.length} products
            </span>
            {totalPages > 1 && (
              <span>
                Page {currentPage} of {totalPages}
              </span>
            )}
          </div>
        </Card>
      )}

      {/* Product Grid/List */}
      <LazyLoadWrapper
        isLoading={isLoading}
        error={error}
        skeleton={<ProductGridSkeleton count={itemsPerPage} />}
      >
        {paginatedProducts.length === 0 ? (
          <Card className="p-12 text-center">
            <div className="max-w-md mx-auto">
              <Search className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {searchTerm ? 'No products found' : emptyMessage}
              </h3>
              <p className="text-gray-600 mb-6">
                {searchTerm
                  ? `No products match "${searchTerm}". Try adjusting your search or filters.`
                  : 'Check back later for new products.'
                }
              </p>
              {(searchTerm || priceRange.min || priceRange.max) && (
                <Button onClick={clearFilters}>
                  Clear Filters
                </Button>
              )}
            </div>
          </Card>
        ) : (
          <div className={cn(
            viewMode === 'grid'
              ? "grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 md:gap-6"
              : "space-y-4"
          )}>
            {paginatedProducts.map(product => (
              <EnhancedProductCard
                key={product.id}
                product={{
                  ...product,
                  image_url: Array.isArray(product.image_url) ? product.image_url[0] : product.image_url
                }}
                compact={viewMode === 'grid'}
                showQuickActions={viewMode === 'grid'}
              />
            ))}
          </div>
        )}
      </LazyLoadWrapper>

      {/* Pagination */}
      {totalPages > 1 && (
        <Card className="p-4">
          <div className="flex items-center justify-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>

            {/* Page Numbers */}
            <div className="flex items-center gap-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const pageNum = currentPage <= 3
                  ? i + 1
                  : currentPage >= totalPages - 2
                    ? totalPages - 4 + i
                    : currentPage - 2 + i;

                if (pageNum < 1 || pageNum > totalPages) return null;

                return (
                  <button
                    key={pageNum}
                    onClick={() => setCurrentPage(pageNum)}
                    className={cn(
                      "w-8 h-8 rounded text-sm font-medium transition-colors",
                      currentPage === pageNum
                        ? "bg-primarycolor text-white"
                        : "text-gray-600 hover:bg-gray-100"
                    )}
                  >
                    {pageNum}
                  </button>
                );
              })}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
}
