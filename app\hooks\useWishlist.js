import { supabase } from '../lib/supabase';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

/**
 * Wishlist-related hooks
 * Handles wishlist operations for authenticated users
 */

export function useWishlistItems(userId) {
  return useQuery({
    queryKey: ['wishlist', userId],
    queryFn: async () => {
      if (!userId) return [];
      
      const { data, error } = await supabase
        .from('wishlists')
        .select('*, products(*)')
        .eq('user_id', userId);
      
      if (error) throw error;
      return data;
    },
    enabled: !!userId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useAddToWishlist() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ userId, productId }) => {
      if (!userId) throw new Error('User must be logged in');
      
      const { data, error } = await supabase
        .from('wishlists')
        .insert([{ 
          user_id: userId, 
          product_id: productId,
          created_at: new Date().toISOString()
        }])
        .select();
      
      if (error) {
        // Handle duplicate entry error
        if (error.code === '23505') {
          throw new Error('Item already in wishlist');
        }
        throw error;
      }
      
      return data;
    },
    onSuccess: (_, { userId }) => {
      queryClient.invalidateQueries({ queryKey: ['wishlist', userId] });
    },
  });
}

export function useRemoveFromWishlist() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ userId, productId }) => {
      if (!userId) return;
      
      const { error } = await supabase
        .from('wishlists')
        .delete()
        .match({ user_id: userId, product_id: productId });
      
      if (error) throw error;
    },
    onSuccess: (_, { userId }) => {
      queryClient.invalidateQueries({ queryKey: ['wishlist', userId] });
    },
  });
}

export function useToggleWishlistItem() {
  const addToWishlist = useAddToWishlist();
  const removeFromWishlist = useRemoveFromWishlist();
  
  return useMutation({
    mutationFn: async ({ userId, productId, isInWishlist }) => {
      if (isInWishlist) {
        return removeFromWishlist.mutateAsync({ userId, productId });
      } else {
        return addToWishlist.mutateAsync({ userId, productId });
      }
    },
  });
}

export function useClearWishlist() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (userId) => {
      if (!userId) return;
      
      const { error } = await supabase
        .from('wishlists')
        .delete()
        .eq('user_id', userId);
      
      if (error) throw error;
    },
    onSuccess: (_, userId) => {
      queryClient.invalidateQueries({ queryKey: ['wishlist', userId] });
    },
  });
}

export function useIsInWishlist(userId, productId) {
  return useQuery({
    queryKey: ['wishlist-check', userId, productId],
    queryFn: async () => {
      if (!userId || !productId) return false;
      
      const { data, error } = await supabase
        .from('wishlists')
        .select('id')
        .match({ user_id: userId, product_id: productId })
        .single();
      
      if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
        throw error;
      }
      
      return !!data;
    },
    enabled: !!userId && !!productId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Wishlist analytics
export function useWishlistAnalytics(userId) {
  return useQuery({
    queryKey: ['wishlist-analytics', userId],
    queryFn: async () => {
      if (!userId) return null;

      const { data: wishlistItems, error } = await supabase
        .from('wishlists')
        .select(`
          *,
          products (
            name,
            price,
            category_name,
            discount,
            is_promoted
          )
        `)
        .eq('user_id', userId);

      if (error) throw error;

      const totalItems = wishlistItems.length;
      const totalValue = wishlistItems.reduce((sum, item) => {
        const price = item.products.price;
        const discount = item.products.discount || 0;
        const discountedPrice = price * (1 - discount / 100);
        return sum + discountedPrice;
      }, 0);

      const categories = [...new Set(wishlistItems.map(item => item.products.category_name))];
      const promotedItems = wishlistItems.filter(item => item.products.is_promoted).length;
      
      const categoryBreakdown = categories.map(category => ({
        name: category,
        items: wishlistItems.filter(item => item.products.category_name === category).length,
        value: wishlistItems
          .filter(item => item.products.category_name === category)
          .reduce((sum, item) => {
            const price = item.products.price;
            const discount = item.products.discount || 0;
            return sum + (price * (1 - discount / 100));
          }, 0)
      }));

      return {
        totalItems,
        totalValue,
        averageItemValue: totalItems > 0 ? totalValue / totalItems : 0,
        categories: categories.length,
        promotedItems,
        categoryBreakdown
      };
    },
    enabled: !!userId,
    staleTime: 5 * 60 * 1000,
  });
}

// Wishlist recommendations
export function useWishlistRecommendations(userId) {
  return useQuery({
    queryKey: ['wishlist-recommendations', userId],
    queryFn: async () => {
      if (!userId) return [];

      // Get current wishlist items
      const { data: wishlistItems, error: wishlistError } = await supabase
        .from('wishlists')
        .select('product_id, products(category_name)')
        .eq('user_id', userId);

      if (wishlistError) throw wishlistError;

      if (wishlistItems.length === 0) return [];

      // Get categories from wishlist
      const wishlistCategories = [...new Set(wishlistItems.map(item => item.products.category_name))];
      const wishlistProductIds = wishlistItems.map(item => item.product_id);

      // Find related products from same categories
      const { data: recommendations, error: recError } = await supabase
        .from('products')
        .select('*')
        .in('category_name', wishlistCategories)
        .not('id', 'in', `(${wishlistProductIds.join(',')})`)
        .limit(8);

      if (recError) throw recError;

      return recommendations.map(product => ({
        ...product,
        image_url: Array.isArray(product.image_url) ? product.image_url : [product.image_url]
      }));
    },
    enabled: !!userId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Wishlist sharing (for future feature)
export function useShareWishlist() {
  return useMutation({
    mutationFn: async ({ userId, shareSettings }) => {
      // This would create a shareable link for the wishlist
      // For now, just return a mock response
      const shareId = `wishlist_${userId}_${Date.now()}`;
      
      // In a real implementation, you'd store this in a shares table
      return {
        shareId,
        shareUrl: `${window.location.origin}/shared/wishlist/${shareId}`,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        settings: shareSettings
      };
    },
  });
}

// Move wishlist items to cart
export function useMoveWishlistToCart() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ userId, productIds }) => {
      if (!userId || !productIds.length) return;

      // Get product details for the items
      const { data: products, error: productsError } = await supabase
        .from('products')
        .select('*')
        .in('id', productIds);

      if (productsError) throw productsError;

      // Add items to cart
      const cartItems = products.map(product => ({
        user_id: userId,
        product_id: product.id,
        quantity: 1,
        price_at_time: product.price,
        created_at: new Date().toISOString()
      }));

      const { error: cartError } = await supabase
        .from('cart')
        .upsert(cartItems, { onConflict: 'user_id,product_id' });

      if (cartError) throw cartError;

      // Remove items from wishlist
      const { error: wishlistError } = await supabase
        .from('wishlists')
        .delete()
        .eq('user_id', userId)
        .in('product_id', productIds);

      if (wishlistError) throw wishlistError;

      return { movedItems: products.length };
    },
    onSuccess: (_, { userId }) => {
      queryClient.invalidateQueries({ queryKey: ['wishlist', userId] });
      queryClient.invalidateQueries({ queryKey: ['cart', userId] });
    },
  });
}

// Wishlist price alerts (for future feature)
export function useWishlistPriceAlerts(userId) {
  return useQuery({
    queryKey: ['wishlist-price-alerts', userId],
    queryFn: async () => {
      if (!userId) return [];

      // Get wishlist items with current prices
      const { data: wishlistItems, error } = await supabase
        .from('wishlists')
        .select(`
          *,
          products (
            id,
            name,
            price,
            discount,
            image_url
          )
        `)
        .eq('user_id', userId);

      if (error) throw error;

      // Check for price drops (this would typically be done server-side)
      const alerts = wishlistItems
        .filter(item => {
          // Mock logic for price alerts
          const currentPrice = item.products.price;
          const discount = item.products.discount || 0;
          const discountedPrice = currentPrice * (1 - discount / 100);
          
          // If there's a discount, it's a price alert
          return discount > 0;
        })
        .map(item => ({
          productId: item.product_id,
          productName: item.products.name,
          originalPrice: item.products.price,
          currentPrice: item.products.price * (1 - (item.products.discount || 0) / 100),
          discount: item.products.discount,
          imageUrl: item.products.image_url,
          alertType: 'price_drop'
        }));

      return alerts;
    },
    enabled: !!userId,
    staleTime: 5 * 60 * 1000,
  });
}
