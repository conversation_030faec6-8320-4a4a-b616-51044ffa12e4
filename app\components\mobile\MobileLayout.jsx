"use client";

import React, { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import MobileBottomNav from './MobileBottomNav';
import { cn } from '../../lib/utils';

export default function MobileLayout({ children }) {
  const pathname = usePathname();
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Don't show mobile layout on admin pages
  if (pathname.startsWith('/admin')) {
    return children;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Main Content */}
      <main className="pb-16 md:pb-0">
        {children}
      </main>

      {/* Mobile Bottom Navigation */}
      <MobileBottomNav />
    </div>
  );
}

// Mobile Header Component
export function MobileHeader({ 
  title, 
  showBack = false, 
  onBack, 
  rightAction,
  className,
  sticky = true 
}) {
  return (
    <header className={cn(
      "bg-white border-b border-gray-200 z-40",
      sticky && "sticky top-0",
      className
    )}>
      <div className="flex items-center justify-between h-14 px-4">
        {/* Left Side */}
        <div className="flex items-center gap-3">
          {showBack && (
            <button
              onClick={onBack || (() => window.history.back())}
              className="p-2 -ml-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
          )}
          
          {title && (
            <h1 className="text-lg font-semibold text-gray-900 truncate">
              {title}
            </h1>
          )}
        </div>

        {/* Right Side */}
        {rightAction && (
          <div className="flex items-center gap-2">
            {rightAction}
          </div>
        )}
      </div>
    </header>
  );
}

// Mobile Section Component
export function MobileSection({ 
  title, 
  subtitle, 
  action, 
  children, 
  className,
  padding = true 
}) {
  return (
    <section className={cn("bg-white", className)}>
      {(title || subtitle || action) && (
        <div className={cn("flex items-center justify-between", padding && "px-4 py-3")}>
          <div>
            {title && (
              <h2 className="text-lg font-semibold text-gray-900">
                {title}
              </h2>
            )}
            {subtitle && (
              <p className="text-sm text-gray-600 mt-0.5">
                {subtitle}
              </p>
            )}
          </div>
          {action && action}
        </div>
      )}
      
      <div className={cn(padding && "px-4 pb-4")}>
        {children}
      </div>
    </section>
  );
}

// Mobile Card Component
export function MobileCard({ 
  children, 
  className, 
  padding = true,
  onClick,
  ...props 
}) {
  return (
    <div
      className={cn(
        "bg-white rounded-lg border border-gray-200",
        onClick && "cursor-pointer hover:shadow-md transition-shadow",
        padding && "p-4",
        className
      )}
      onClick={onClick}
      {...props}
    >
      {children}
    </div>
  );
}

// Mobile List Item Component
export function MobileListItem({ 
  icon, 
  title, 
  subtitle, 
  rightContent, 
  onClick,
  className 
}) {
  return (
    <div
      className={cn(
        "flex items-center gap-3 p-4 bg-white border-b border-gray-100 last:border-b-0",
        onClick && "cursor-pointer hover:bg-gray-50 active:bg-gray-100 transition-colors",
        className
      )}
      onClick={onClick}
    >
      {icon && (
        <div className="flex-shrink-0">
          {icon}
        </div>
      )}
      
      <div className="flex-1 min-w-0">
        <p className="font-medium text-gray-900 truncate">
          {title}
        </p>
        {subtitle && (
          <p className="text-sm text-gray-600 truncate">
            {subtitle}
          </p>
        )}
      </div>
      
      {rightContent && (
        <div className="flex-shrink-0">
          {rightContent}
        </div>
      )}
      
      {onClick && (
        <div className="flex-shrink-0">
          <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </div>
      )}
    </div>
  );
}

// Mobile Button Component
export function MobileButton({ 
  children, 
  variant = 'primary', 
  size = 'md',
  fullWidth = false,
  loading = false,
  disabled = false,
  className,
  ...props 
}) {
  const baseClasses = "inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed";
  
  const variants = {
    primary: "bg-primarycolor text-white hover:bg-primarycolor-700 focus:ring-primarycolor-300",
    secondary: "bg-secondarycolor text-white hover:bg-secondarycolor-600 focus:ring-secondarycolor-300",
    outline: "border-2 border-primarycolor text-primarycolor hover:bg-primarycolor hover:text-white focus:ring-primarycolor-300",
    ghost: "text-primarycolor hover:bg-primarycolor-50 focus:ring-primarycolor-300",
  };
  
  const sizes = {
    sm: "px-3 py-2 text-sm",
    md: "px-4 py-3 text-base",
    lg: "px-6 py-4 text-lg",
  };

  return (
    <button
      className={cn(
        baseClasses,
        variants[variant],
        sizes[size],
        fullWidth && "w-full",
        className
      )}
      disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
        </svg>
      )}
      {children}
    </button>
  );
}

// Mobile Input Component
export function MobileInput({ 
  label, 
  error, 
  hint,
  leftIcon,
  rightIcon,
  className,
  ...props 
}) {
  return (
    <div className="space-y-1">
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
        </label>
      )}
      
      <div className="relative">
        {leftIcon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            {leftIcon}
          </div>
        )}
        
        <input
          className={cn(
            "block w-full px-3 py-3 border border-gray-300 rounded-lg placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primarycolor focus:border-transparent",
            leftIcon && "pl-10",
            rightIcon && "pr-10",
            error && "border-red-500 focus:ring-red-500",
            className
          )}
          {...props}
        />
        
        {rightIcon && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            {rightIcon}
          </div>
        )}
      </div>
      
      {error && (
        <p className="text-sm text-red-600">
          {error}
        </p>
      )}
      
      {hint && !error && (
        <p className="text-sm text-gray-500">
          {hint}
        </p>
      )}
    </div>
  );
}

// Mobile Tabs Component
export function MobileTabs({ tabs = [], activeTab, onTabChange, className }) {
  return (
    <div className={cn("bg-white border-b border-gray-200", className)}>
      <div className="flex overflow-x-auto scrollbar-hide">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={cn(
              "flex-shrink-0 px-4 py-3 text-sm font-medium border-b-2 transition-colors",
              activeTab === tab.id
                ? "text-primarycolor border-primarycolor"
                : "text-gray-600 border-transparent hover:text-gray-900 hover:border-gray-300"
            )}
          >
            {tab.label}
          </button>
        ))}
      </div>
    </div>
  );
}
