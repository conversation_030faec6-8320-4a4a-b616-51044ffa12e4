"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import { 
  LayoutDashboard, 
  Package, 
  ShoppingCart, 
  Users, 
  BarChart3, 
  Settings,
  LogOut,
  ChevronLeft,
  ChevronRight,
  Tag,
  FileText,
  Bell,
  HelpCircle,
  Shield,
  Home,
  Truck,
  ImageIcon
} from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { cn, getInitials } from '../../lib/utils';
import Button from '../ui/Button';

export default function EnhancedSidebar({ isOpen, setIsOpen, unreadOrders = 0 }) {
  const { userDetails, signOut } = useAuth();
  const pathname = usePathname();
  const router = useRouter();
  const [collapsed, setCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 1024);
    };
    
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push('/');
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const menuItems = [
    { 
      icon: LayoutDashboard, 
      label: 'Dashboard', 
      path: '/admin',
      description: 'Overview and analytics'
    },
    { 
      icon: Package, 
      label: 'Products', 
      path: '/admin/products',
      description: 'Manage inventory'
    },
    { 
      icon: Tag, 
      label: 'Categories', 
      path: '/admin/categories',
      description: 'Product categories'
    },
    { 
      icon: ImageIcon, 
      label: 'Marketing', 
      path: '/admin/marketing',
      description: 'Banners and promotions'
    },
    { 
      icon: ShoppingCart, 
      label: 'Orders', 
      path: '/admin/orders',
      description: 'Customer orders',
      badge: unreadOrders > 0 ? unreadOrders : null
    },
    { 
      icon: Users, 
      label: 'Customers', 
      path: '/admin/customers',
      description: 'User management'
    },
    { 
      icon: Truck, 
      label: 'Delivery', 
      path: '/admin/delivery',
      description: 'Shipping and logistics'
    },
    { 
      icon: BarChart3, 
      label: 'Reports', 
      path: '/admin/reports',
      description: 'Analytics and insights'
    },
    { 
      icon: Settings, 
      label: 'Settings', 
      path: '/admin/settings',
      description: 'System configuration'
    },
  ];

  const handleLinkClick = () => {
    if (isMobile) {
      setIsOpen(false);
    }
  };

  return (
    <>
      {/* Mobile Overlay */}
      {isMobile && isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Sidebar */}
      <aside
        className={cn(
          "fixed lg:static top-0 left-0 h-screen bg-white border-r border-gray-200 z-50 transition-all duration-300 ease-in-out flex flex-col",
          isMobile 
            ? isOpen ? "translate-x-0 w-64" : "-translate-x-full w-64"
            : collapsed ? "w-16" : "w-64"
        )}
      >
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            {!collapsed && (
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primarycolor rounded-xl flex items-center justify-center">
                  <span className="text-xl font-bold text-white">P</span>
                </div>
                <div>
                  <h1 className="text-lg font-bold text-gray-900">Pinchez</h1>
                  <p className="text-xs text-gray-500">Admin Panel</p>
                </div>
              </div>
            )}
            
            {!isMobile && (
              <button
                onClick={() => setCollapsed(!collapsed)}
                className="p-1.5 rounded-lg hover:bg-gray-100 transition-colors"
              >
                {collapsed ? (
                  <ChevronRight className="w-4 h-4 text-gray-600" />
                ) : (
                  <ChevronLeft className="w-4 h-4 text-gray-600" />
                )}
              </button>
            )}
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-3 py-4 space-y-1 overflow-y-auto">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = pathname === item.path;
            
            return (
              <Link
                key={item.path}
                href={item.path}
                onClick={handleLinkClick}
                className={cn(
                  "flex items-center gap-3 px-3 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 relative group",
                  isActive
                    ? "bg-primarycolor text-white shadow-sm"
                    : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                )}
              >
                <Icon className={cn(
                  "w-5 h-5 flex-shrink-0",
                  isActive ? "text-white" : "text-gray-500"
                )} />
                
                {!collapsed && (
                  <>
                    <span className="flex-1">{item.label}</span>
                    
                    {/* Badge */}
                    {item.badge && (
                      <span className={cn(
                        "px-2 py-0.5 text-xs font-semibold rounded-full",
                        isActive 
                          ? "bg-white text-primarycolor"
                          : "bg-error text-white"
                      )}>
                        {item.badge}
                      </span>
                    )}
                  </>
                )}

                {/* Tooltip for collapsed state */}
                {collapsed && (
                  <div className="absolute left-full ml-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap z-50">
                    <div className="flex flex-col">
                      <span className="font-medium">{item.label}</span>
                      <span className="text-xs text-gray-300">{item.description}</span>
                    </div>
                    <div className="absolute top-1/2 -left-1 -translate-y-1/2 w-2 h-2 bg-gray-900 rotate-45" />
                  </div>
                )}
              </Link>
            );
          })}
        </nav>

        {/* User Profile & Actions */}
        <div className="p-3 border-t border-gray-200">
          {/* Quick Actions */}
          {!collapsed && (
            <div className="mb-4">
              <div className="grid grid-cols-2 gap-2">
                <Link
                  href="/"
                  className="flex items-center justify-center gap-2 px-3 py-2 text-xs font-medium text-gray-600 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <Home className="w-4 h-4" />
                  <span>Store</span>
                </Link>
                <button className="flex items-center justify-center gap-2 px-3 py-2 text-xs font-medium text-gray-600 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <HelpCircle className="w-4 h-4" />
                  <span>Help</span>
                </button>
              </div>
            </div>
          )}

          {/* User Profile */}
          <div className={cn(
            "flex items-center gap-3 p-3 rounded-lg bg-gray-50",
            collapsed && "justify-center"
          )}>
            <div className="w-8 h-8 bg-primarycolor rounded-full flex items-center justify-center text-white text-sm font-medium flex-shrink-0">
              {userDetails?.avatar_url ? (
                <Image
                  src={userDetails.avatar_url}
                  alt="Profile"
                  width={32}
                  height={32}
                  className="w-full h-full rounded-full object-cover"
                />
              ) : (
                getInitials(userDetails?.name || 'Admin')
              )}
            </div>
            
            {!collapsed && (
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {userDetails?.name || 'Admin'}
                </p>
                <p className="text-xs text-gray-500 truncate">
                  Administrator
                </p>
              </div>
            )}
          </div>

          {/* Sign Out */}
          <div className="mt-3">
            <button
              onClick={handleSignOut}
              className={cn(
                "flex items-center gap-3 w-full px-3 py-2.5 text-sm font-medium text-red-600 hover:bg-red-50 rounded-lg transition-colors",
                collapsed && "justify-center"
              )}
            >
              <LogOut className="w-5 h-5" />
              {!collapsed && <span>Sign Out</span>}
            </button>
          </div>
        </div>
      </aside>
    </>
  );
}
